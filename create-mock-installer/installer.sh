#!/usr/bin/env bash
# installer.sh - HiNAS Control Mock Simulator 설치 스크립트
# Ubuntu 22.04 최적화 완전 버전 (docker-compose.yaml 지원)
#   1) 필수 라이브러리 + Docker + Docker‑Compose 설치
#   2) <bundle>.tar 압축 해제 (대화형 입력 지원)
#   3) images/*.tar 전부 docker load (타임아웃 및 재시도 기능)
#   4) docker‑compose up -d
#   5) X11 root·docker 로컬 접근 권한을 현재·향후 세션에 영구 부여

set -eo pipefail

# 색상 정의
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 로그 함수
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_progress() { echo -e "${CYAN}🔄 $1${NC}"; }

# 파일 크기를 읽기 쉬운 형태로 변환
human_readable_size() {
    local size=$1
    if (( size > 1073741824 )); then
        # GB 단위
        local gb=$((size / 1073741824))
        local remainder=$((size % 1073741824))
        local decimal=$((remainder * 10 / 1073741824))
        printf "%d.%dGB" $gb $decimal
    elif (( size > 1048576 )); then
        # MB 단위
        local mb=$((size / 1048576))
        local remainder=$((size % 1048576))
        local decimal=$((remainder * 10 / 1048576))
        printf "%d.%dMB" $mb $decimal
    elif (( size > 1024 )); then
        # KB 단위
        local kb=$((size / 1024))
        local remainder=$((size % 1024))
        local decimal=$((remainder * 10 / 1024))
        printf "%d.%dKB" $kb $decimal
    else
        printf "%dB" $size
    fi
}

# TAR 파일 선택 함수
select_tar_file() {
    local script_dir="$1"
    local provided_bundle="${2:-}"
    
    # 파라미터로 제공된 경우
    if [[ -n "$provided_bundle" ]]; then
        if [[ -f "$script_dir/$provided_bundle" ]]; then
            echo "$provided_bundle"
            return 0
        elif [[ -f "$provided_bundle" ]]; then
            echo "$provided_bundle"
            return 0
        else
            log_error "지정된 파일을 찾을 수 없습니다: $provided_bundle"
        fi
    fi
    
    # 현재 디렉토리에서 .tar 파일 찾기
    local tar_files=()
    while IFS= read -r -d '' file; do
        tar_files+=("$(basename "$file")")
    done < <(find "$script_dir" -maxdepth 1 -name "*.tar" -type f -print0 2>/dev/null)
    
    # TAR 파일이 없는 경우
    if [[ ${#tar_files[@]} -eq 0 ]]; then
        log_error "현재 디렉토리에 .tar 파일이 없습니다."
        log_info "현재 디렉토리: $script_dir"
        log_info "사용법: sudo ./installer.sh [파일명.tar]"
        exit 12
    fi
    
    # TAR 파일이 1개인 경우 자동 선택
    if [[ ${#tar_files[@]} -eq 1 ]]; then
        log_info "TAR 파일 자동 선택: ${tar_files[0]}"
        echo "${tar_files[0]}"
        return 0
    fi
    
    # 여러 TAR 파일이 있는 경우 선택 메뉴 표시
    echo ""
    log_info "여러 개의 TAR 파일이 발견되었습니다:"
    echo ""
    
    for i in "${!tar_files[@]}"; do
        local file_path="$script_dir/${tar_files[$i]}"
        local file_size=$(stat -c%s "$file_path" 2>/dev/null || echo 0)
        local file_size_str=$(human_readable_size $file_size)
        printf "  %d) %s (%s)\n" $((i+1)) "${tar_files[$i]}" "$file_size_str"
    done
    
    echo ""
    local max_attempts=5
    local attempt=0

    while [[ $attempt -lt $max_attempts ]]; do
        printf "설치할 TAR 파일을 선택하세요 (1-${#tar_files[@]}): "

        # read 명령어 (간단하게)
        local choice=""
        if read -r choice </dev/tty; then
            # 입력값 검증
            if [[ -n "${choice:-}" ]] && [[ "$choice" =~ ^[0-9]+$ ]] && [[ $choice -ge 1 ]] && [[ $choice -le ${#tar_files[@]} ]]; then
                local selected_file="${tar_files[$((choice-1))]}"
                log_info "선택된 파일: $selected_file"
                echo "$selected_file"
                return 0
            else
                log_error "잘못된 선택입니다. 1부터 ${#tar_files[@]} 사이의 숫자를 입력하세요."
                attempt=$((attempt + 1))
                if [[ $attempt -lt $max_attempts ]]; then
                    echo "남은 시도 횟수: $((max_attempts - attempt))"
                fi
            fi
        else
            log_error "입력을 읽을 수 없습니다."
            attempt=$((attempt + 1))
            if [[ $attempt -lt $max_attempts ]]; then
                echo "남은 시도 횟수: $((max_attempts - attempt))"
            fi
        fi

        if [[ $attempt -ge $max_attempts ]]; then
            log_error "최대 시도 횟수를 초과했습니다. 첫 번째 파일을 자동 선택합니다."
            local selected_file="${tar_files[0]}"
            log_info "자동 선택된 파일: $selected_file"
            echo "$selected_file"
            return 0
        fi

        echo ""
    done
}

# Root 권한 체크
(( EUID == 0 )) || { 
    log_error "Root 권한이 필요합니다"
    echo "사용법: sudo ./installer.sh [bundle.tar]"
    exit 1
}

# 변수 설정
USER_NAME="${SUDO_USER:-$(logname 2>/dev/null || echo 'ubuntu')}"
USER_HOME=$(getent passwd "$USER_NAME" | cut -d: -f6)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# TAR 파일 선택
BUNDLE=$(select_tar_file "$SCRIPT_DIR" "${1:-}")
BUNDLE_DIR="${BUNDLE%.tar}"

export DEBIAN_FRONTEND=noninteractive

echo ""
echo "========================================"
log_info "HiNAS Control Mock Simulator 설치 시작"
echo "========================================"
log_info "사용자: $USER_NAME"
log_info "설치 파일: $BUNDLE"
log_info "작업 디렉토리: $SCRIPT_DIR"
echo "========================================"
echo ""

# Ubuntu 버전 확인
UBUNTU_VERSION=$(lsb_release -rs 2>/dev/null || echo "unknown")
log_info "Ubuntu 버전: $UBUNTU_VERSION"

# 1. 패키지 갱신 및 필수 라이브러리 설치
log_info "패키지 갱신 및 필수 라이브러리 설치"
apt-get update -y || { log_error "패키지 업데이트 실패"; exit 2; }

apt-get install -y --no-install-recommends \
  ca-certificates \
  curl \
  gnupg \
  lsb-release \
  software-properties-common \
  apt-transport-https \
  openssh-server \
  openssh-client \
  net-tools \
  iputils-ping \
  wget \
  vim \
  nano \
  htop \
  bc \
  pv \
  libgl1-mesa-dri \
  libglx-mesa0 \
  libqt5core5a \
  libqt5gui5 \
  libqt5widgets5 \
  libxcb-xinerama0 \
  libxkbcommon-x11-0 \
  x11-xserver-utils \
  xauth \
  || { log_error "필수 패키지 설치 실패"; exit 3; }

# SSH 서비스 시작 및 활성화
log_info "SSH 서비스 설정"
systemctl enable ssh || log_warning "SSH 서비스 활성화 실패"
systemctl start ssh || log_warning "SSH 서비스 시작 실패"

if systemctl is-active --quiet ssh; then
    log_success "SSH 서비스 실행 중"
    SSH_PORT=$(ss -tlnp | grep :22 | head -1 | awk '{print $4}' | cut -d: -f2)
    log_info "SSH 포트: ${SSH_PORT:-22}"
else
    log_warning "SSH 서비스 시작 실패 (선택사항이므로 계속 진행)"
fi

log_success "필수 라이브러리 설치 완료"

# 2. Docker 설치
log_info "Docker 설치 확인 및 설치"
if command -v docker >/dev/null 2>&1; then
    log_warning "Docker 이미 설치됨 ($(docker --version))"
else
    log_info "Docker 공식 저장소 추가"

    # GPG 키 추가
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg || {
        log_error "Docker GPG 키 추가 실패"
        exit 4
    }

    # 저장소 추가
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | \
        tee /etc/apt/sources.list.d/docker.list > /dev/null || {
        log_error "Docker 저장소 추가 실패"
        exit 5
    }

    # Docker 설치
    apt-get update -y
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin || {
        log_error "Docker 설치 실패"
        exit 6
    }

    log_success "Docker 설치 완료"
fi

# 3. Docker Compose 설치
log_info "Docker Compose 설치 확인 및 설치"
if command -v docker-compose >/dev/null 2>&1; then
    log_warning "Docker Compose 이미 설치됨 ($(docker-compose --version))"
else
    log_info "Docker Compose 설치 중..."

    # 최신 버전 다운로드
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" \
        -o /usr/local/bin/docker-compose || {
        log_error "Docker Compose 다운로드 실패"
        exit 7
    }

    chmod +x /usr/local/bin/docker-compose
    log_success "Docker Compose 설치 완료 (${COMPOSE_VERSION})"
fi

# 4. Docker 서비스 시작 및 확인
log_info "Docker 서비스 시작"
systemctl enable docker || { log_error "Docker 서비스 활성화 실패"; exit 8; }
systemctl start docker || { log_error "Docker 서비스 시작 실패"; exit 9; }

# Docker 서비스 상태 확인
sleep 2
if systemctl is-active --quiet docker; then
    log_success "Docker 서비스 실행 중"
else
    log_error "Docker 서비스 시작 실패"
    systemctl status docker
    exit 10
fi

# 5. 사용자를 docker 그룹에 추가 및 시스템 설정
log_info "사용자($USER_NAME)를 docker 그룹에 추가"
usermod -aG docker "$USER_NAME" || { log_error "Docker 그룹 추가 실패"; exit 11; }

# 5-1. 시스템 레벨 네트워크 설정
log_info "시스템 네트워크 설정 (멀티캐스트 지원)"

# 멀티캐스트 라우팅을 위한 시스템 설정
cat > /etc/systemd/system/hinas-network-setup.service << 'EOF'
[Unit]
Description=HiNAS Control Mock Simulator Network Setup
After=network.target
Wants=network.target

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/bin/bash -c '
    # 멀티캐스트 라우팅 설정
    DEFAULT_INTERFACE=$(ip route | grep default | awk "{print \$5}" | head -1)
    if [ -n "$DEFAULT_INTERFACE" ] && ! ip route show | grep -q "*********/8"; then
        echo "$(date): Adding multicast route via $DEFAULT_INTERFACE"
        ip route add *********/8 dev $DEFAULT_INTERFACE 2>/dev/null || true
    fi

    # IPv4 포워딩 활성화 (Docker 네트워킹용)
    echo 1 > /proc/sys/net/ipv4/ip_forward 2>/dev/null || true

    # 멀티캐스트 관련 커널 파라미터 설정
    echo 1 > /proc/sys/net/ipv4/conf/all/mc_forwarding 2>/dev/null || true
    echo 1 > /proc/sys/net/ipv4/conf/default/mc_forwarding 2>/dev/null || true

    echo "$(date): HiNAS network setup completed"
'

[Install]
WantedBy=multi-user.target
EOF

# 네트워크 설정 서비스 활성화
systemctl daemon-reload
systemctl enable hinas-network-setup.service 2>/dev/null || log_warning "네트워크 설정 서비스 활성화 실패"

# 5-2. 커널 파라미터 영구 설정
log_info "커널 파라미터 영구 설정"
cat > /etc/sysctl.d/99-hinas-simulator.conf << 'EOF'
# HiNAS Control Mock Simulator - 커널 파라미터 설정
# 재부팅 후 자동 적용

# IPv4 포워딩 활성화 (Docker 네트워킹)
net.ipv4.ip_forward = 1

# 멀티캐스트 포워딩 활성화
net.ipv4.conf.all.mc_forwarding = 1
net.ipv4.conf.default.mc_forwarding = 1

# 네트워크 버퍼 크기 증가 (대용량 데이터 전송용)
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728

# 멀티캐스트 관련 설정
net.ipv4.igmp_max_memberships = 20
net.ipv4.igmp_max_msf = 10
EOF

# 커널 파라미터 즉시 적용
sysctl -p /etc/sysctl.d/99-hinas-simulator.conf 2>/dev/null || log_warning "커널 파라미터 적용 실패"

log_success "Docker 그룹 추가 및 시스템 설정 완료"

# 6. X11 접근 허용 (현재 세션)
log_info "X11 접근 허용 설정 (현재 세션)"
if [[ -n "${DISPLAY:-}" ]] && command -v xhost >/dev/null 2>&1; then
    sudo -u "$USER_NAME" DISPLAY="$DISPLAY" xhost +SI:localuser:root 2>/dev/null || log_warning "root 사용자 X11 접근 설정 실패"
    sudo -u "$USER_NAME" DISPLAY="$DISPLAY" xhost +local:docker 2>/dev/null || log_warning "docker X11 접근 설정 실패"
    log_success "현재 세션 X11 접근 허용 완료"
else
    log_warning "DISPLAY 환경변수가 없거나 xhost 명령어를 찾을 수 없음"
fi

# 7. X11 및 시스템 설정 (영구 설정)
log_info "X11 및 시스템 설정 (영구)"

# 7-1. .xprofile 설정 (X11 세션 시작 시 실행)
XPROFILE="$USER_HOME/.xprofile"
log_info "X11 접근 허용 설정 (.xprofile)"

# .xprofile 파일 생성 또는 업데이트
cat > "$XPROFILE" << 'EOF'
#!/bin/bash
# HiNAS Control Mock Simulator - X11 및 시스템 설정
# 재부팅 후 자동 적용

LOG_FILE="$HOME/.hinas-setup.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" | tee -a "$LOG_FILE"
}

log_message "HiNAS .xprofile 설정 시작"

# DISPLAY 환경변수 설정 (없는 경우)
if [ -z "$DISPLAY" ]; then
    export DISPLAY=:0
    log_message "DISPLAY 환경변수 설정: $DISPLAY"
fi

# X11 접근 허용 (Docker 컨테이너용) - 더 강력한 설정
if command -v xhost >/dev/null 2>&1; then
    log_message "X11 접근 허용 설정 적용 중..."

    # 기본 권한 설정
    xhost +SI:localuser:root 2>/dev/null && log_message "root 사용자 X11 접근 허용"
    xhost +local:docker 2>/dev/null && log_message "docker X11 접근 허용"
    xhost +local: 2>/dev/null && log_message "로컬 X11 접근 허용"

    # 추가 권한 설정 (더 관대한 설정)
    xhost + 2>/dev/null && log_message "전체 X11 접근 허용"

    log_message "X11 접근 허용 설정 완료"
else
    log_message "xhost 명령어를 찾을 수 없음"
fi

# Docker 서비스 상태 확인 및 시작
if command -v systemctl >/dev/null 2>&1; then
    if ! systemctl is-active --quiet docker; then
        log_message "Docker 서비스 시작 중..."
        sudo systemctl start docker 2>/dev/null || log_message "Docker 서비스 시작 실패"
    else
        log_message "Docker 서비스 실행 중"
    fi
fi

# 네트워크 설정 확인 (멀티캐스트 라우팅)
if command -v ip >/dev/null 2>&1; then
    # 멀티캐스트 라우팅 설정 확인 및 추가
    if ! ip route show | grep -q "*********/8"; then
        log_message "멀티캐스트 라우팅 설정 중..."
        DEFAULT_INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)
        if [ -n "$DEFAULT_INTERFACE" ]; then
            sudo ip route add *********/8 dev "$DEFAULT_INTERFACE" 2>/dev/null && \
                log_message "멀티캐스트 라우팅 설정 완료: $DEFAULT_INTERFACE"
        fi
    else
        log_message "멀티캐스트 라우팅 이미 설정됨"
    fi
fi

log_message "HiNAS .xprofile 설정 완료"
EOF

chown "$USER_NAME:$USER_NAME" "$XPROFILE" 2>/dev/null || true
chmod 755 "$XPROFILE" 2>/dev/null || true

# 7-2. .bashrc 설정 (터미널 세션 시작 시 실행)
BASHRC="$USER_HOME/.bashrc"
log_info "터미널 환경 설정 (.bashrc)"

# .bashrc에 HiNAS 관련 설정 추가
BASHRC_SECTION="# HiNAS Control Mock Simulator Settings"
if ! grep -q "$BASHRC_SECTION" "$BASHRC" 2>/dev/null; then
    cat >> "$BASHRC" << 'EOF'

# HiNAS Control Mock Simulator Settings
# Docker 관련 환경변수 및 별칭 설정

# Docker 환경변수
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# X11 포워딩 설정
export DISPLAY=${DISPLAY:-:0}

# X11 권한 설정 함수
setup_x11_permissions() {
    if [ -n "$DISPLAY" ] && command -v xhost >/dev/null 2>&1; then
        echo "$(date): X11 권한 설정 중..."
        xhost +SI:localuser:root 2>/dev/null || true
        xhost +local:docker 2>/dev/null || true
        xhost +local: 2>/dev/null || true
        xhost + 2>/dev/null || true
        echo "$(date): X11 권한 설정 완료"
    fi
}

# 터미널 시작 시 X11 권한 자동 설정
if [ -n "$DISPLAY" ] && [ "$TERM" != "dumb" ]; then
    setup_x11_permissions
fi

# HiNAS 관련 별칭
alias hinas-status='docker-compose ps'
alias hinas-logs='docker-compose logs -f'
alias hinas-restart='docker-compose restart'
alias hinas-stop='docker-compose down'
alias hinas-start='docker-compose up -d'
alias hinas-x11='setup_x11_permissions'

# Docker 관련 유용한 별칭
alias docker-clean='docker system prune -f'
alias docker-images='docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"'
alias docker-ps='docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"'

# 네트워크 관련 별칭
alias show-ips='ip addr show | grep "inet " | grep -v 127.0.0.1'
alias show-ports='ss -tlnp'

# X11 문제 해결 별칭
alias fix-x11='setup_x11_permissions'

echo "HiNAS Control Mock Simulator 환경 설정 로드됨"
echo "X11 문제 발생 시 'fix-x11' 또는 'hinas-x11' 명령어를 실행하세요"
EOF
fi

# 7-3. 시스템 레벨 X11 권한 설정 스크립트 생성
log_info "시스템 레벨 X11 권한 설정 스크립트 생성"

# X11 권한 설정 스크립트 생성
cat > /usr/local/bin/hinas-x11-setup.sh << 'EOF'
#!/bin/bash
# HiNAS Control Mock Simulator X11 권한 설정 스크립트
# 재부팅 후 자동 실행

LOG_FILE="/var/log/hinas-x11-setup.log"
MAX_RETRIES=30
RETRY_DELAY=2

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" | tee -a "$LOG_FILE"
}

log_message "========================================="
log_message "HiNAS X11 권한 설정 시작"
log_message "========================================="

# X11 서버가 시작될 때까지 대기
wait_for_x11() {
    local retry_count=0

    while [ $retry_count -lt $MAX_RETRIES ]; do
        # X11 서버 프로세스 확인
        if pgrep -f "X.*:0" >/dev/null 2>&1; then
            log_message "X11 서버 프로세스 발견"

            # X11 소켓 파일 확인
            if [ -S "/tmp/.X11-unix/X0" ]; then
                log_message "X11 소켓 파일 확인됨"
                return 0
            fi
        fi

        log_message "X11 서버 대기 중... (시도 $((retry_count + 1))/$MAX_RETRIES)"
        sleep $RETRY_DELAY
        retry_count=$((retry_count + 1))
    done

    log_message "X11 서버 대기 시간 초과"
    return 1
}

# X11 서버 대기
if ! wait_for_x11; then
    log_message "X11 서버를 찾을 수 없음, 강제로 권한 설정 시도"
fi

# 현재 활성 X11 세션 찾기 및 권한 설정
setup_x11_permissions() {
    local success=false

    for display_num in 0 1 2; do
        DISPLAY_VAR=":$display_num"

        # X11 서버가 실행 중인지 확인
        if [ -S "/tmp/.X11-unix/X$display_num" ]; then
            log_message "X11 디스플레이 $DISPLAY_VAR 발견"

            # 여러 방법으로 X11 소유자 찾기
            X_USER=""

            # 방법 1: X 프로세스에서 찾기
            X_USER=$(ps aux | grep "X.*:$display_num" | grep -v grep | awk '{print $1}' | head -1)

            # 방법 2: who 명령어로 찾기
            if [ -z "$X_USER" ] || [ "$X_USER" = "root" ]; then
                X_USER=$(who | grep "(:$display_num)" | awk '{print $1}' | head -1)
            fi

            # 방법 3: loginctl로 찾기
            if [ -z "$X_USER" ] || [ "$X_USER" = "root" ]; then
                X_USER=$(loginctl list-sessions --no-legend | awk '{print $3}' | grep -v root | head -1)
            fi

            # 방법 4: 기본 사용자 사용
            if [ -z "$X_USER" ] || [ "$X_USER" = "root" ]; then
                X_USER="avikus"  # 기본 사용자명
            fi

            if [ -n "$X_USER" ] && [ "$X_USER" != "root" ]; then
                log_message "X11 소유자: $X_USER, 디스플레이: $DISPLAY_VAR"

                # 해당 사용자로 xhost 명령 실행 (여러 번 시도)
                for attempt in 1 2 3; do
                    log_message "X11 권한 설정 시도 $attempt/3"

                    if sudo -u "$X_USER" DISPLAY="$DISPLAY_VAR" xhost +SI:localuser:root 2>/dev/null; then
                        log_message "root 사용자 X11 접근 허용 완료"
                        success=true
                    fi

                    if sudo -u "$X_USER" DISPLAY="$DISPLAY_VAR" xhost +local:docker 2>/dev/null; then
                        log_message "docker X11 접근 허용 완료"
                        success=true
                    fi

                    if sudo -u "$X_USER" DISPLAY="$DISPLAY_VAR" xhost +local: 2>/dev/null; then
                        log_message "로컬 X11 접근 허용 완료"
                        success=true
                    fi

                    # 추가 권한 설정 (더 관대한 설정)
                    if sudo -u "$X_USER" DISPLAY="$DISPLAY_VAR" xhost + 2>/dev/null; then
                        log_message "전체 X11 접근 허용 완료"
                        success=true
                    fi

                    if $success; then
                        break
                    fi

                    sleep 1
                done
            else
                log_message "유효한 X11 사용자를 찾을 수 없음"
            fi
        fi
    done

    return $success
}

# X11 시스템 대기 (부팅 시 X11이 아직 준비되지 않을 수 있음)
wait_for_x11_system() {
    local max_wait=60  # 최대 60초 대기
    local count=0

    log_message "X11 시스템 준비 대기 중..."

    while [ $count -lt $max_wait ]; do
        # X11 소켓이나 프로세스가 있는지 확인
        if [ -d "/tmp/.X11-unix" ] && [ "$(ls -A /tmp/.X11-unix 2>/dev/null)" ]; then
            log_message "X11 시스템 준비 완료 (${count}초 후)"
            return 0
        fi

        # Xorg 프로세스 확인
        if pgrep -f "Xorg\|X.*:" >/dev/null 2>&1; then
            log_message "X11 프로세스 발견 (${count}초 후)"
            return 0
        fi

        sleep 1
        count=$((count + 1))
    done

    log_message "X11 시스템 대기 시간 초과, 강제 진행"
    return 1
}

# X11 시스템 대기
wait_for_x11_system

# X11 권한 설정 실행 (여러 번 재시도)
for attempt in 1 2 3; do
    log_message "X11 권한 설정 시도 #${attempt}"
    if setup_x11_permissions; then
        log_message "X11 권한 설정 성공"
        break
    else
        log_message "X11 권한 설정 실패, 재시도 예정"
        sleep 5
    fi
done

# Docker 서비스 상태 확인 및 시작
if systemctl is-active --quiet docker; then
    log_message "Docker 서비스 실행 중"
else
    log_message "Docker 서비스 시작 중..."
    if systemctl start docker 2>/dev/null; then
        log_message "Docker 서비스 시작 완료"
    else
        log_message "Docker 서비스 시작 실패"
    fi
fi

# 최종 상태 확인
log_message "========================================="
log_message "최종 상태 확인:"
log_message "X11 소켓: $(ls -la /tmp/.X11-unix/ 2>/dev/null | wc -l) 개"
log_message "X11 프로세스: $(pgrep -f 'X.*:' | wc -l) 개"
log_message "Docker 상태: $(systemctl is-active docker)"
log_message "========================================="
log_message "HiNAS X11 권한 설정 완료"

exit 0
EOF

chmod +x /usr/local/bin/hinas-x11-setup.sh

# 7-4. systemd 시스템 서비스 생성 (부팅 시 자동 실행)
log_info "systemd 시스템 서비스 설정"

cat > /etc/systemd/system/hinas-x11-setup.service << 'EOF'
[Unit]
Description=HiNAS Control Mock Simulator X11 Setup
After=network.target systemd-user-sessions.service
Before=graphical.target
DefaultDependencies=no

[Service]
Type=forking
RemainAfterExit=yes
Restart=on-failure
RestartSec=5
TimeoutStartSec=30
User=root
Environment=DISPLAY=:0
ExecStart=/bin/bash -c '/usr/local/bin/hinas-x11-setup.sh &'
# 빠른 시작을 위한 단순화된 실행

[Install]
WantedBy=multi-user.target
EOF

# 7-5. systemd 타이머 서비스 생성 (주기적 실행)
log_info "systemd 타이머 서비스 설정"

cat > /etc/systemd/system/hinas-x11-setup.timer << 'EOF'
[Unit]
Description=HiNAS Control Mock Simulator X11 Setup Timer
After=multi-user.target

[Timer]
OnBootSec=10sec
OnStartupSec=10sec
OnUnitActiveSec=2min
Persistent=true
AccuracySec=1s

[Install]
WantedBy=timers.target multi-user.target
EOF

# 서비스 및 타이머 활성화
log_info "systemd 서비스 활성화 중..."
systemctl daemon-reload

# 서비스 활성화 (부팅 시 자동 시작)
if systemctl enable hinas-x11-setup.service; then
    log_success "X11 설정 서비스 부팅 시 자동 시작 활성화 완료"
else
    log_warning "X11 설정 서비스 활성화 실패"
fi

if systemctl enable hinas-x11-setup.timer; then
    log_success "X11 설정 타이머 부팅 시 자동 시작 활성화 완료"
else
    log_warning "X11 설정 타이머 활성화 실패"
fi

# 즉시 서비스 시작 (테스트용)
log_info "서비스 즉시 시작 중..."
if systemctl start hinas-x11-setup.service; then
    log_success "X11 설정 서비스 즉시 시작 완료"
else
    log_warning "X11 설정 서비스 즉시 시작 실패"
fi

if systemctl start hinas-x11-setup.timer; then
    log_success "X11 설정 타이머 즉시 시작 완료"
else
    log_warning "X11 설정 타이머 즉시 시작 실패"
fi

# 서비스 상태 확인
log_info "서비스 상태 확인:"
systemctl is-enabled hinas-x11-setup.service && log_success "  ✓ hinas-x11-setup.service: 부팅 시 자동 시작 활성화됨"
systemctl is-enabled hinas-x11-setup.timer && log_success "  ✓ hinas-x11-setup.timer: 부팅 시 자동 시작 활성화됨"

# 7-6. cron을 이용한 주기적 X11 권한 설정 (추가 백업 방법)
log_info "cron을 이용한 주기적 X11 권한 설정"

# root crontab에 추가 (오류 처리 개선)
if ! (crontab -l 2>/dev/null; echo "# HiNAS X11 권한 설정 (매 5분마다)"; echo "*/5 * * * * /usr/local/bin/hinas-x11-setup.sh >/dev/null 2>&1") | crontab - 2>/dev/null; then
    log_warning "crontab 설정 실패, 수동으로 설정하거나 무시하고 계속 진행"
else
    log_success "crontab 설정 완료"
fi

log_success "영구 X11 및 시스템 설정 완료"

# 8. 번들 파일 확인 및 압축 해제
log_info "번들 파일 확인: $BUNDLE"
BUNDLE_PATH="$SCRIPT_DIR/$BUNDLE"

if [[ ! -f "$BUNDLE_PATH" ]]; then
    # 현재 디렉토리에서도 찾아보기
    if [[ -f "$BUNDLE" ]]; then
        BUNDLE_PATH="$BUNDLE"
    else
        log_error "번들 파일을 찾을 수 없습니다: $BUNDLE"
        log_info "현재 디렉토리 파일 목록:"
        ls -la "$SCRIPT_DIR"/*.tar 2>/dev/null || echo "  .tar 파일이 없습니다"
        exit 12
    fi
fi

log_info "번들 압축 해제: $(basename "$BUNDLE_PATH")"
cd "$SCRIPT_DIR"

# 기존 디렉토리가 있으면 백업
if [[ -d "$BUNDLE_DIR" ]]; then
    log_warning "기존 디렉토리 발견, 백업 생성: ${BUNDLE_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    mv "$BUNDLE_DIR" "${BUNDLE_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 번들 크기 확인
BUNDLE_SIZE=$(stat -c%s "$BUNDLE_PATH" 2>/dev/null || echo 0)
BUNDLE_SIZE_STR=$(human_readable_size $BUNDLE_SIZE)
log_info "번들 크기: $BUNDLE_SIZE_STR"

# 진행률 표시와 함께 압축 해제
if command -v pv >/dev/null 2>&1; then
    log_progress "압축 해제 중... (진행률 표시)"
    pv "$BUNDLE_PATH" | tar -xf - || { log_error "번들 압축 해제 실패"; exit 13; }
else
    tar -xf "$BUNDLE_PATH" || { log_error "번들 압축 해제 실패"; exit 13; }
fi

if [[ ! -d "$BUNDLE_DIR" ]]; then
    log_error "압축 해제 후 디렉토리를 찾을 수 없습니다: $BUNDLE_DIR"
    exit 14
fi

log_success "번들 압축 해제 완료"

# 9. Docker 이미지 로드
log_info "Docker 이미지 로드 준비"
IMAGES_DIR="$SCRIPT_DIR/$BUNDLE_DIR/images"

if [[ ! -d "$IMAGES_DIR" ]]; then
    log_error "이미지 디렉토리를 찾을 수 없습니다: $IMAGES_DIR"
    log_info "번들 구조:"
    find "$SCRIPT_DIR/$BUNDLE_DIR" -type f -name "*.tar" 2>/dev/null || echo "  .tar 파일이 없습니다"
    exit 15
fi

# 이미지 파일 개수 확인
IMAGE_COUNT=$(find "$IMAGES_DIR" -name "*.tar" -type f | wc -l)
if [[ $IMAGE_COUNT -eq 0 ]]; then
    log_error "이미지 파일(.tar)을 찾을 수 없습니다: $IMAGES_DIR"
    log_info "디렉토리 내용:"
    ls -la "$IMAGES_DIR"
    exit 16
fi

log_info "Docker 이미지 로드 시작"
log_info "총 $IMAGE_COUNT 개의 이미지 파일 발견"

# 이미지 파일 목록 출력
echo ""
log_info "발견된 이미지 파일들:"
find "$IMAGES_DIR" -name "*.tar" -type f | while read -r img; do
    IMG_NAME=$(basename "$img")
    IMG_SIZE=$(stat -c%s "$img" 2>/dev/null || echo 0)
    IMG_SIZE_STR=$(human_readable_size $IMG_SIZE)
    echo "  • $IMG_NAME ($IMG_SIZE_STR)"
done

echo ""
log_info "이미지 로드 진행 중..."

# 이미지 로드 진행
LOADED_COUNT=0
FAILED_COUNT=0
START_TIME=$(date +%s)

find "$IMAGES_DIR" -name "*.tar" -type f | sort | while read -r img; do
    if [[ -f "$img" ]]; then
        IMG_NAME=$(basename "$img")
        IMG_SIZE=$(stat -c%s "$img" 2>/dev/null || echo 0)
        IMG_SIZE_STR=$(human_readable_size $IMG_SIZE)

        LOADED_COUNT=$((LOADED_COUNT + 1))

        echo ""
        echo "========================================"
        log_info "[$LOADED_COUNT/$IMAGE_COUNT] 이미지 로드: $IMG_NAME"
        log_info "파일 크기: $IMG_SIZE_STR"
        echo "========================================"

        # Docker 이미지 로드 실행
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 로드 시작: $IMG_NAME"

        if docker load -i "$img"; then
            echo "$(date '+%Y-%m-%d %H:%M:%S') - ✅ 로드 성공: $IMG_NAME"
            log_success "로드 완료: $IMG_NAME"
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') - ❌ 로드 실패: $IMG_NAME"
            log_error "로드 실패: $IMG_NAME"
            FAILED_COUNT=$((FAILED_COUNT + 1))
        fi

        # 현재 상태 출력
        echo ""
        log_info "현재 진행률: $LOADED_COUNT/$IMAGE_COUNT"

        # 현재 Docker 이미지 수 확인
        CURRENT_IMAGES=$(docker images | tail -n +2 | wc -l)
        log_info "현재 로드된 총 이미지 수: $CURRENT_IMAGES 개"

        echo ""
    fi
done

# 최종 결과
END_TIME=$(date +%s)
ELAPSED=$((END_TIME - START_TIME))

echo ""
echo "========================================"
log_success "Docker 이미지 로드 작업 완료!"
echo "========================================"
log_info "소요 시간: ${ELAPSED}초"

# 최종 이미지 목록 확인
echo ""
log_info "최종 로드된 Docker 이미지 목록:"
docker images

echo ""
log_info "Docker 시스템 정보:"
docker system df 2>/dev/null || echo "시스템 정보를 가져올 수 없습니다"

echo ""

# 10. docker-compose.yaml 파일 확인
log_info "docker-compose 파일 확인"
COMPOSE_FILE=""

# docker-compose.yaml 우선 확인
if [[ -f "$SCRIPT_DIR/$BUNDLE_DIR/docker-compose.yaml" ]]; then
    COMPOSE_FILE="$SCRIPT_DIR/$BUNDLE_DIR/docker-compose.yaml"
    log_info "docker-compose.yaml 파일 발견"
elif [[ -f "$SCRIPT_DIR/$BUNDLE_DIR/docker-compose.yml" ]]; then
    COMPOSE_FILE="$SCRIPT_DIR/$BUNDLE_DIR/docker-compose.yml"
    log_info "docker-compose.yml 파일 발견"
else
    log_error "docker-compose 파일을 찾을 수 없습니다"
    log_info "번들 디렉토리 내용:"
    ls -la "$SCRIPT_DIR/$BUNDLE_DIR/"
    exit 18
fi

log_success "docker-compose 파일 확인 완료: $(basename "$COMPOSE_FILE")"

# 11. 기존 컨테이너 정리
log_info "기존 컨테이너 확인 및 정리"
cd "$SCRIPT_DIR/$BUNDLE_DIR"

# 기존 컨테이너가 실행 중이면 중지
if docker-compose ps -q 2>/dev/null | grep -q .; then
    log_warning "기존 컨테이너 발견, 중지 중..."
    docker-compose down || log_warning "기존 컨테이너 중지 실패 (무시하고 계속)"
fi

# 12. Docker Compose 실행
log_info "Docker Compose 서비스 시작"
log_info "사용 파일: $(basename "$COMPOSE_FILE")"

if docker-compose up -d; then
    log_success "Docker Compose 서비스 시작 완료"
else
    log_error "Docker Compose 서비스 시작 실패"
    log_info "로그 확인:"
    docker-compose logs --tail=20
    exit 19
fi

# 13. 서비스 상태 확인
log_info "서비스 상태 확인 중..."
sleep 5

echo ""
log_info "=== 최종 상태 확인 ==="

# 시스템 정보
echo "🖥️  시스템 정보:"
echo "  • Ubuntu: $UBUNTU_VERSION"
echo "  • 사용자: $USER_NAME"
echo "  • IP 주소: $(hostname -I | awk '{print $1}' 2>/dev/null || echo 'N/A')"

# Docker 버전 정보
echo ""
echo "🐳 Docker 정보:"
docker --version
docker-compose --version

# 네트워크 정보
echo ""
echo "🌐 네트워크 정보:"
if command -v ifconfig >/dev/null 2>&1; then
    echo "  • 네트워크 인터페이스:"
    ifconfig | grep -E "inet [0-9]" | grep -v 127.0.0.1 | awk '{print "    - " $2}' || echo "    - 정보 없음"
fi

# SSH 정보
if systemctl is-active --quiet ssh; then
    echo "  • SSH 서비스: 실행 중 (포트 22)"
    echo "  • SSH 접속: ssh $USER_NAME@$(hostname -I | awk '{print $1}' 2>/dev/null || echo 'localhost')"
fi

# 실행 중인 컨테이너 확인
echo ""
echo "📦 실행 중인 컨테이너:"
docker-compose ps

# 포트 확인
echo ""
echo "🌐 열린 포트:"
docker-compose ps --format "table {{.Name}}\t{{.Ports}}" 2>/dev/null || docker-compose ps

# 로그 미리보기
echo ""
echo "📋 최근 로그 (마지막 10줄):"
docker-compose logs --tail=10

echo ""
log_success "=== 설치 및 배포 완료 ==="

echo ""
log_info "🔄 재부팅 후 자동 적용되는 설정들:"
echo "  • X11 접근 권한 (xhost 설정)"
echo "  • 멀티캐스트 라우팅 (*********/8)"
echo "  • Docker 서비스 자동 시작"
echo "  • 네트워크 커널 파라미터"
echo "  • HiNAS 환경변수 및 별칭"

echo ""
log_info "🔧 서비스 관리 명령어:"
echo "  • 상태 확인: cd $SCRIPT_DIR/$BUNDLE_DIR && docker-compose ps"
echo "  • 로그 확인: cd $SCRIPT_DIR/$BUNDLE_DIR && docker-compose logs -f"
echo "  • 서비스 중지: cd $SCRIPT_DIR/$BUNDLE_DIR && docker-compose down"
echo "  • 서비스 재시작: cd $SCRIPT_DIR/$BUNDLE_DIR && docker-compose restart"
echo "  • 전체 상태: hinas-status (별칭)"
echo "  • 실시간 로그: hinas-logs (별칭)"

echo ""
log_info "🌐 네트워크 도구:"
echo "  • IP 확인: ifconfig 또는 ip addr"
echo "  • 포트 확인: netstat -tlnp 또는 ss -tlnp"
echo "  • 연결 테스트: ping <대상IP>"

# 웹 인터페이스 정보
if docker-compose ps | grep -q ":.*->.*80\|:.*->.*8000\|:.*->.*3000"; then
    echo ""
    log_info "🌍 웹 인터페이스가 실행 중일 수 있습니다:"
    echo "  • http://localhost (포트 확인 후 접속)"
    LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
    if [[ -n "$LOCAL_IP" ]]; then
        echo "  • http://$LOCAL_IP (외부에서 접속)"
    fi
fi

echo ""
log_success "모든 작업이 성공적으로 완료되었습니다! 🎉"

echo ""
log_info "📋 설치 후 권장 사항:"
echo "  1. 시스템 재부팅을 권장합니다 (모든 설정이 완전히 적용됨)"
echo "  2. 재부팅 후 X11 GUI 애플리케이션이 정상 작동합니다"
echo "  3. SSH로 원격 접속이 가능합니다"

echo ""
log_warning "⚠️  재부팅 명령어: sudo reboot"
log_info "재부팅 후 서비스는 자동으로 시작됩니다"

echo ""
log_info "🔄 부팅 시 자동 시작 서비스:"
echo "  • hinas-x11-setup.service: X11 권한 설정 (부팅 10초 후 자동 실행)"
echo "  • hinas-x11-setup.timer: X11 권한 주기적 점검 (2분마다)"
echo "  • GUI 로그인 없이도 부팅 시 자동으로 시작됩니다"
echo "  • 서비스 상태 확인: sudo systemctl status hinas-x11-setup"
echo "  • 서비스 로그 확인: sudo journalctl -u hinas-x11-setup -f"

echo ""
log_info "� X11 문제 발생 시 해결 방법:"
echo "  • 수동 권한 설정: fix-x11 또는 hinas-x11"
echo "  • 시스템 서비스 재시작: sudo systemctl restart hinas-x11-setup"
echo "  • 로그 확인: tail -f /var/log/hinas-x11-setup.log"
echo "  • 사용자 로그 확인: tail -f ~/.hinas-setup.log"

echo ""
log_info "�🔍 설치된 설정 파일들:"
echo "  • X11 설정: $USER_HOME/.xprofile"
echo "  • 터미널 설정: $USER_HOME/.bashrc"
echo "  • systemd 서비스: /etc/systemd/system/hinas-x11-setup.service"
echo "  • systemd 타이머: /etc/systemd/system/hinas-x11-setup.timer"
echo "  • 네트워크 서비스: /etc/systemd/system/hinas-network-setup.service"
echo "  • 커널 파라미터: /etc/sysctl.d/99-hinas-simulator.conf"

# 임시 파일 정리
rm -f /tmp/docker_load.log

exit 0
