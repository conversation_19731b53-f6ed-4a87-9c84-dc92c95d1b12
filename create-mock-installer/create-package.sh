#!/bin/bash

# HiNAS Control Mock Simulator 패키지 생성 스크립트
# 사용법: ./create-package.sh [VERSION]
# 예시: ./create-package.sh v1.0.0-RC1

set -e  # 에러 발생 시 스크립트 중단

# 전역 변수
USE_SUDO=false

# 색상 정의
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 로그 함수들
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Docker 명령 실행 헬퍼 함수
run_docker() {
    if [ "$USE_SUDO" = true ]; then
        sudo docker "$@"
    else
        docker "$@"
    fi
}

# AWS ECR 로그인 함수
aws_login() {
    log_info "AWS ECR 로그인 중..."
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI가 설치되어 있지 않습니다. AWS CLI를 먼저 설치해주세요."
        exit 1
    fi

    # 현재 사용자가 root인지 확인
    if [ "$EUID" -eq 0 ]; then
        # root 사용자인 경우
        if ! aws ecr get-login-password --region ap-northeast-2 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-northeast-2.amazonaws.com; then
            log_error "AWS ECR 로그인에 실패했습니다. AWS 자격증명을 확인해주세요."
            exit 1
        fi
    else
        # 일반 사용자인 경우 - docker 그룹 권한 확인
        if groups | grep -q docker; then
            # docker 그룹에 속한 경우
            if ! aws ecr get-login-password --region ap-northeast-2 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-northeast-2.amazonaws.com; then
                log_error "AWS ECR 로그인에 실패했습니다. AWS 자격증명을 확인해주세요."
                exit 1
            fi
        else
            # docker 그룹에 속하지 않은 경우 sudo 사용
            log_warning "현재 사용자가 docker 그룹에 속하지 않습니다. sudo를 사용합니다."
            if ! aws ecr get-login-password --region ap-northeast-2 | sudo docker login --username AWS --password-stdin ************.dkr.ecr.ap-northeast-2.amazonaws.com; then
                log_error "AWS ECR 로그인에 실패했습니다. AWS 자격증명을 확인해주세요."
                exit 1
            fi
            # sudo로 로그인했으므로 이후 docker 명령도 sudo 사용 필요
            USE_SUDO=true
        fi
    fi
    log_success "AWS ECR 로그인 완료"
}

# 버전 입력 받기
get_version() {
    if [ -z "$1" ]; then
        echo -n "패키지 버전을 입력하세요 (예: v1.0.0-RC1): "
        read VERSION
        if [ -z "$VERSION" ]; then
            log_error "버전을 입력해야 합니다."
            exit 1
        fi
    else
        VERSION="$1"
    fi
    log_info "패키지 버전: $VERSION"
}

# 패키지 폴더 생성 및 확인
create_package_folder() {
    PACKAGE_NAME="HiNAS-Control-Mock-$VERSION"
    IMAGES_DIR="$PACKAGE_NAME/images"
    ROUTES_DIR="$PACKAGE_NAME/routes"

    if [ -d "$PACKAGE_NAME" ]; then
        log_error "패키지 폴더 '$PACKAGE_NAME'가 이미 존재합니다. 삭제 후 다시 실행해주세요."
        exit 1
    fi

    log_info "패키지 폴더 생성: $PACKAGE_NAME"
    mkdir -p "$IMAGES_DIR"
    mkdir -p "$ROUTES_DIR"
    mkdir -p "$PACKAGE_NAME/runtime-root"
}

# 이미지 정의
define_images() {
    # ECR 이미지들 (고정)
    ECDIS_ECR_IMAGE="************.dkr.ecr.ap-northeast-2.amazonaws.com/nas2-ecdis:v0.0.15-furuno"
    REDIS_ECR_IMAGE="************.dkr.ecr.ap-northeast-2.amazonaws.com/library:redis-7.2.7-alpine"
    AIS_VDM_ECR_IMAGE="************.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:1.0.0-ais-vdm"
    
    # 버전별 ECR 이미지들
    AUTOPILOT_UI_ECR_IMAGE="************.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:$VERSION-autopilot-simulator-ui"
    AUTOPILOT_SERVER_ECR_IMAGE="************.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:$VERSION-autopilot-simulator"
    SHIP_SIMULATOR_ECR_IMAGE="************.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:$VERSION-ship-simulator"
    MOCK_UI_ECR_IMAGE="************.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:$VERSION-hinas-control-mock-simulator"
    
    # 로컬 태그들
    ECDIS_LOCAL_TAG="nas2-ecdis:v0.0.15-furuno"
    REDIS_LOCAL_TAG="redis:7.2.7-alpine"
    AIS_VDM_LOCAL_TAG="ais-vdm:1.0.0"
    AUTOPILOT_UI_LOCAL_TAG="autopilot-simulator-ui:$VERSION"
    AUTOPILOT_SERVER_LOCAL_TAG="autopilot-simulator:$VERSION"
    SHIP_SIMULATOR_LOCAL_TAG="ship-simulator:$VERSION"
    MOCK_UI_LOCAL_TAG="hinas-control-mock-simulator:$VERSION"
}

# 이미지 다운로드 및 태그 변경
pull_and_retag_images() {
    log_info "이미지 다운로드 및 태그 변경 시작..."

    # 이미지 배열을 개별적으로 처리
    declare -a ecr_images=(
        "$ECDIS_ECR_IMAGE"
        "$REDIS_ECR_IMAGE"
        "$AIS_VDM_ECR_IMAGE"
        "$AUTOPILOT_UI_ECR_IMAGE"
        "$AUTOPILOT_SERVER_ECR_IMAGE"
        "$SHIP_SIMULATOR_ECR_IMAGE"
        "$MOCK_UI_ECR_IMAGE"
    )

    declare -a local_tags=(
        "$ECDIS_LOCAL_TAG"
        "$REDIS_LOCAL_TAG"
        "$AIS_VDM_LOCAL_TAG"
        "$AUTOPILOT_UI_LOCAL_TAG"
        "$AUTOPILOT_SERVER_LOCAL_TAG"
        "$SHIP_SIMULATOR_LOCAL_TAG"
        "$MOCK_UI_LOCAL_TAG"
    )

    # 배열 길이 확인
    if [ ${#ecr_images[@]} -ne ${#local_tags[@]} ]; then
        log_error "ECR 이미지와 로컬 태그 배열의 길이가 다릅니다."
        exit 1
    fi

    # 각 이미지 처리
    for i in "${!ecr_images[@]}"; do
        ecr_image="${ecr_images[$i]}"
        local_tag="${local_tags[$i]}"

        log_info "다운로드 중: $ecr_image"
        if ! run_docker pull "$ecr_image"; then
            log_error "이미지 다운로드 실패: $ecr_image"
            exit 1
        fi

        log_info "태그 변경: $ecr_image -> $local_tag"
        run_docker tag "$ecr_image" "$local_tag"
    done

    log_success "모든 이미지 다운로드 및 태그 변경 완료"
}

# 이미지를 TAR 파일로 저장
save_images_to_tar() {
    log_info "이미지를 TAR 파일로 저장 시작..."

    # 로컬 태그 배열 (위에서 정의된 것과 동일)
    declare -a local_tags=(
        "$ECDIS_LOCAL_TAG"
        "$REDIS_LOCAL_TAG"
        "$AIS_VDM_LOCAL_TAG"
        "$AUTOPILOT_UI_LOCAL_TAG"
        "$AUTOPILOT_SERVER_LOCAL_TAG"
        "$SHIP_SIMULATOR_LOCAL_TAG"
        "$MOCK_UI_LOCAL_TAG"
    )

    # TAR 파일명 배열
    declare -a tar_filenames=(
        "nas2-ecdis-v0.0.15-furuno.tar"
        "redis-7.2.7-alpine.tar"
        "ais-vdm-1.0.0.tar"
        "autopilot-simulator-ui-$VERSION.tar"
        "autopilot-simulator-$VERSION.tar"
        "ship-simulator-$VERSION.tar"
        "hinas-control-mock-simulator-$VERSION.tar"
    )

    # 배열 길이 확인
    if [ ${#local_tags[@]} -ne ${#tar_filenames[@]} ]; then
        log_error "로컬 태그와 TAR 파일명 배열의 길이가 다릅니다."
        exit 1
    fi

    # 각 이미지를 TAR 파일로 저장
    for i in "${!local_tags[@]}"; do
        local_tag="${local_tags[$i]}"
        tar_filename="${tar_filenames[$i]}"
        tar_path="$IMAGES_DIR/$tar_filename"

        log_info "저장 중: $local_tag -> $tar_filename"
        if ! run_docker save -o "$tar_path" "$local_tag"; then
            log_error "이미지 저장 실패: $local_tag"
            exit 1
        fi
    done

    log_success "모든 이미지 TAR 파일 저장 완료"
}

# Route 샘플 파일들 복사
copy_route_samples() {
    log_info "Route 샘플 파일들 복사 시작..."

    ROUTE_SAMPLE_DIR="route-sample"

    if [ ! -d "$ROUTE_SAMPLE_DIR" ]; then
        log_error "Route 샘플 디렉토리 '$ROUTE_SAMPLE_DIR'를 찾을 수 없습니다."
        exit 1
    fi

    # RTZ 파일만 복사 (Zone.Identifier 파일 제외)
    rtz_count=0
    for rtz_file in "$ROUTE_SAMPLE_DIR"/*.rtz; do
        if [ -f "$rtz_file" ]; then
            filename=$(basename "$rtz_file")
            log_info "복사 중: $filename"
            cp "$rtz_file" "$ROUTES_DIR/"
            rtz_count=$((rtz_count + 1))
        fi
    done

    if [ $rtz_count -eq 0 ]; then
        log_warning "RTZ 파일을 찾을 수 없습니다."
    else
        log_success "Route 샘플 파일 복사 완료 (총 ${rtz_count}개 파일)"
    fi
}

# docker-compose.yaml 생성
create_docker_compose() {
    log_info "docker-compose.yaml 생성 중..."

    cat > "$PACKAGE_NAME/docker-compose.yaml" << EOF
version: '3.8'

services:
  init-config:
    image: hinas-control-mock-simulator:$VERSION
    command: >
      sh -c '
        set -e
        mkdir -p /host/ship-simulator/config /host/autopilot-simulator/config;

        if [ -z "\$(ls -A /host/ship-simulator/config 2>/dev/null)" ]; then
          echo "[init] copy ship-simulator config -> host"
          cp -a /app/ship-simulator/config/. /host/ship-simulator/config/
        fi

        if [ -z "\$(ls -A /host/autopilot-simulator/config 2>/dev/null)" ]; then
          echo "[init] copy autopilot-simulator config -> host"
          cp -a /app/autopilot-simulator/config/. /host/autopilot-simulator/config/
        fi

        echo "[init] done"
      '
    volumes:
      - /app/ship-simulator:/host/ship-simulator
      - /app/autopilot-simulator:/host/autopilot-simulator
    restart: "no"

  app:
    container_name: hinas-mock-app
    image: hinas-control-mock-simulator:$VERSION
    network_mode: host
    depends_on:
      init-config:
        condition: service_completed_successfully
    environment:
      - DISPLAY=:0
      - XDG_RUNTIME_DIR=/tmp/runtime-root
      - APP_VERSION=\${APP_VERSION:-$VERSION}
      - DEFAULT_ECDIS_IMAGE=\${DEFAULT_ECDIS_IMAGE:-nas2-ecdis:v0.0.15-furuno}
      - DEFAULT_REDIS_IMAGE=\${DEFAULT_REDIS_IMAGE:-redis:7.2.7-alpine}
      - DEFAULT_SHIP_SIMULATOR_IMAGE=\${DEFAULT_SHIP_SIMULATOR_IMAGE:-ship-simulator:$VERSION}
      - DEFAULT_AUTOPILOT_SERVER_IMAGE=\${DEFAULT_AUTOPILOT_SERVER_IMAGE:-autopilot-simulator:$VERSION}
      - DEFAULT_AUTOPILOT_UI_IMAGE=\${DEFAULT_AUTOPILOT_UI_IMAGE:-autopilot-simulator-ui:$VERSION}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /tmp/.X11-unix:/tmp/.X11-unix
      - ./runtime-root:/tmp/runtime-root
      - /app/ship-simulator/config:/app/ship-simulator/config
      - /app/autopilot-simulator/config:/app/autopilot-simulator/config
    restart: unless-stopped
EOF

    log_success "docker-compose.yaml 생성 완료"
}

# 설치 스크립트 생성
create_install_script() {
    log_info "설치 스크립트 생성 중..."

    cat > "$PACKAGE_NAME/install.sh" << 'EOF'
#!/bin/bash

# HiNAS Control Mock Simulator 설치 스크립트

set -e

# 색상 정의
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Docker 설치 확인
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker가 설치되어 있지 않습니다. Docker를 먼저 설치해주세요."
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose가 설치되어 있지 않습니다. Docker Compose를 먼저 설치해주세요."
        exit 1
    fi
}

# 이미지 로드
load_images() {
    log_info "Docker 이미지 로드 시작..."

    for tar_file in images/*.tar; do
        if [ -f "$tar_file" ]; then
            log_info "로드 중: $tar_file"
            docker load -i "$tar_file"
        fi
    done

    log_success "모든 이미지 로드 완료"
}

# 메인 실행
main() {
    log_info "HiNAS Control Mock Simulator 설치 시작"

    check_docker
    load_images

    log_success "설치 완료!"
    log_info "실행하려면: docker-compose up -d"
    log_info "중지하려면: docker-compose down"
}

main "$@"
EOF

    chmod +x "$PACKAGE_NAME/install.sh"
    log_success "설치 스크립트 생성 완료"
}

# README 파일 생성
create_readme() {
    log_info "README.md 생성 중..."

    cat > "$PACKAGE_NAME/README.md" << EOF
# HiNAS Control Mock Simulator $VERSION

## 설치 방법

1. 압축 파일을 원하는 위치에 압축 해제
2. 설치 스크립트 실행:
   \`\`\`bash
   ./install.sh
   \`\`\`

## 실행 방법

\`\`\`bash
# 실행
docker-compose up -d

# 중지
docker-compose down

# 로그 확인
docker-compose logs -f
\`\`\`

## 접속 정보

- 웹 UI: http://localhost:7778
- 이미지 관리: http://localhost:7778/images
- 로그 조회: http://localhost:7778/logs

## 포함된 이미지

- ECDIS: nas2-ecdis:v0.0.15-furuno
- Redis: redis:7.2.7-alpine
- AIS VDM: ais-vdm:1.0.0
- Ship Simulator: ship-simulator:$VERSION
- Autopilot Simulator: autopilot-simulator:$VERSION
- Autopilot UI: autopilot-simulator-ui:$VERSION
- Mock UI: hinas-control-mock-simulator:$VERSION

## 포함된 Route 샘플

\`routes/\` 폴더에 다음 RTZ 파일들이 포함되어 있습니다:
- #05_route.rtz
- 62065-scn2.rtz
- SGP-TEST.rtz
- SGP-TEST2.rtz
- V.0016J SGSIN - BRGIB edit-1.rtz

이 파일들은 ECDIS의 "HiNAS Control로 Route 송신" 기능에서 테스트용으로 사용할 수 있습니다.

## 시스템 요구사항

- Docker 20.10 이상
- Docker Compose 1.29 이상
- Linux 환경 (X11 지원)
EOF

    log_success "README.md 생성 완료"
}

# 최종 패키지 압축
create_final_package() {
    log_info "최종 패키지 압축 중..."

    FINAL_PACKAGE="$PACKAGE_NAME.tar"

    if [ -f "$FINAL_PACKAGE" ]; then
        log_error "최종 패키지 파일 '$FINAL_PACKAGE'가 이미 존재합니다."
        exit 1
    fi

    tar -cf "$FINAL_PACKAGE" "$PACKAGE_NAME"

    log_success "최종 패키지 생성 완료: $FINAL_PACKAGE"

    # 패키지 정보 출력
    log_info "패키지 정보:"
    echo "  - 패키지 파일: $FINAL_PACKAGE"
    echo "  - 패키지 크기: $(du -h "$FINAL_PACKAGE" | cut -f1)"
    echo "  - 포함된 이미지: 7개"
    echo "  - 포함된 Route 샘플: $(ls -1 "$ROUTES_DIR"/*.rtz 2>/dev/null | wc -l)개"
    echo "  - 버전: $VERSION"
}

# 정리 함수
cleanup() {
    log_info "임시 파일 정리 중..."
    # 필요시 Docker 이미지 정리
    # docker rmi 명령어들...
}

# 메인 실행 함수
main() {
    log_info "HiNAS Control Mock Simulator 패키지 생성 시작"

    get_version "$1"
    aws_login
    create_package_folder
    define_images
    pull_and_retag_images
    save_images_to_tar
    copy_route_samples
    create_docker_compose
    create_install_script
    create_readme
    create_final_package

    log_success "패키지 생성 완료!"
    log_info "생성된 패키지: $FINAL_PACKAGE"
}

# 스크립트 실행
main "$@"
