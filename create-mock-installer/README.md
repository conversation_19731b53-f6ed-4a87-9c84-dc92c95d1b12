# HiNAS Control Mock Simulator 패키지 생성기

이 도구는 HiNAS Control Mock Simulator의 배포 패키지를 생성하는 스크립트입니다.

## 사용법

### 1. 기본 사용법
```bash
./create-package.sh v1.0.0-RC1
```

### 2. 대화형 모드
```bash
./create-package.sh
# 버전을 입력하라는 프롬프트가 나타남
```

## 사전 요구사항

### 1. Docker 설치
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install docker.io docker-compose

# CentOS/RHEL
sudo yum install docker docker-compose
```

### 2. AWS CLI 설치 및 설정
```bash
# AWS CLI 설치
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# AWS 자격증명 설정
aws configure
```

### 3. 권한 설정
```bash
# Docker 권한 설정
sudo usermod -aG docker $USER
# 로그아웃 후 다시 로그인

# 스크립트 실행 권한
chmod +x create-package.sh
```

## 생성되는 패키지 구조

```
HiNAS-Control-Mock-v1.0.0-RC1/
├── docker-compose.yaml                           # Docker Compose 설정
├── install.sh                                   # 설치 스크립트
├── README.md                                    # 사용법 안내
├── runtime-root/                               # 런타임 디렉토리
└── images/                                     # Docker 이미지들
    ├── nas2-ecdis-v0.0.15-furuno.tar
    ├── redis-7.2.7-alpine.tar
    ├── autopilot-simulator-ui-v1.0.0-RC1.tar
    ├── autopilot-simulator-v1.0.0-RC1.tar
    ├── ship-simulator-v1.0.0-RC1.tar
    └── hinas-control-mock-simulator-v1.0.0-RC1.tar
```

## 패키지 생성 과정

1. **AWS ECR 로그인**: ECR에서 이미지를 다운로드하기 위한 인증
2. **이미지 다운로드**: 지정된 버전의 이미지들을 ECR에서 다운로드
3. **태그 변경**: ECR 이미지를 로컬 태그로 변경
4. **TAR 파일 생성**: 각 이미지를 개별 TAR 파일로 저장
5. **설정 파일 생성**: docker-compose.yaml, install.sh, README.md 생성
6. **최종 패키징**: 전체 폴더를 TAR 파일로 압축

## 이미지 매핑

### 고정 이미지 (버전 무관)
- `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/nas2-ecdis:v0.0.15-furuno` → `nas2-ecdis:v0.0.15-furuno`
- `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/library:redis-7.2.7-alpine` → `redis:7.2.7-alpine`

### 버전별 이미지 (v1.0.0-RC1 예시)
- `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:v1.0.0-RC1-autopilot-simulator-ui` → `autopilot-simulator-ui:v1.0.0-RC1`
- `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:v1.0.0-RC1-autopilot-simulator` → `autopilot-simulator:v1.0.0-RC1`
- `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:v1.0.0-RC1-ship-simulator` → `ship-simulator:v1.0.0-RC1`
- `479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:v1.0.0-RC1-hinas-control-mock-simulator` → `hinas-control-mock-simulator:v1.0.0-RC1`

## 에러 처리

- **폴더 존재**: 같은 이름의 패키지 폴더가 이미 존재하면 에러 발생
- **AWS 로그인 실패**: AWS 자격증명 문제 시 에러 발생
- **이미지 다운로드 실패**: ECR에서 이미지를 찾을 수 없으면 에러 발생
- **Docker 명령 실패**: Docker 관련 명령 실패 시 에러 발생

## 생성된 패키지 사용법

1. **압축 해제**:
   ```bash
   tar -xf HiNAS-Control-Mock-v1.0.0-RC1.tar
   cd HiNAS-Control-Mock-v1.0.0-RC1
   ```

2. **설치**:
   ```bash
   ./install.sh
   ```

3. **실행**:
   ```bash
   docker-compose up -d
   ```

4. **접속**:
   - 웹 UI: http://localhost:7778

## 문제 해결

### AWS 로그인 문제
```bash
# AWS 자격증명 확인
aws sts get-caller-identity

# ECR 로그인 수동 실행
aws ecr get-login-password --region ap-northeast-2 | sudo docker login --username AWS --password-stdin 479435310497.dkr.ecr.ap-northeast-2.amazonaws.com
```

### Docker 권한 문제
```bash
# 현재 사용자를 docker 그룹에 추가
sudo usermod -aG docker $USER
# 로그아웃 후 다시 로그인
```

### 디스크 공간 부족
```bash
# Docker 이미지 정리
docker system prune -a
```
