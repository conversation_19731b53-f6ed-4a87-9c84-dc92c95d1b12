#!/usr/bin/env python3
"""
Simple Scenario Manager

Usage:
    # Command line
    python scenario_manager.py                    # Show status
    python scenario_manager.py update            # Update config
    python scenario_manager.py scenario          # Set scenario mode
    python scenario_manager.py normal            # Set normal mode

    # Python code
    from scenario_manager import ScenarioManager
    manager = ScenarioManager("scenario_AA.marzip")
    manager.update_config()
    manager.set_mode('scenario')
"""

import os
import json
import yaml
import zipfile
import tempfile
import shutil
import sys
from typing import Dict, Any, Optional, Tuple


class ScenarioManager:
    """Simple scenario manager for maritime simulation."""

    def __init__(
        self,
        marzip_file: str = None,
        config_path: str = "config/simulation_config.json",
    ):
        """
        Initialize scenario manager.

        Args:
            marzip_file: Path to marzip file (if None, will look for *.marzip)
            config_path: Path to simulation_config.json
        """
        self.marzip_file = marzip_file
        self.config_path = config_path
        self.temp_dir = None

        # Auto-find marzip file if not specified
        if not self.marzip_file:
            self.marzip_file = self._find_marzip_file()

    def _find_marzip_file(self) -> Optional[str]:
        """Find marzip file in current directory."""
        import glob

        # Look for marzip files in current directory first
        marzip_files = glob.glob("*.marzip")
        if marzip_files:
            return marzip_files[0]

        # Look in ais_creator directory
        marzip_files = glob.glob("ais_creator/*.marzip")
        if marzip_files:
            return marzip_files[0]

        return None

    def _extract_marzip(self) -> Tuple[Optional[str], Optional[str]]:
        """Extract yml and json files from marzip."""
        if not self.marzip_file or not os.path.exists(self.marzip_file):
            return None, None

        self.temp_dir = tempfile.mkdtemp(prefix="marzip_")

        try:
            with zipfile.ZipFile(self.marzip_file, "r") as zip_ref:
                zip_ref.extractall(self.temp_dir)

            config_yml = None
            scenario_json = None

            # Find configuration.yml and scenario_AA.json
            for root, dirs, files in os.walk(self.temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)

                    if file.lower() == "configuration.yml":
                        config_yml = file_path
                    elif file.lower() == "scenario_aa.json":
                        scenario_json = file_path

            return config_yml, scenario_json

        except Exception as e:
            print(f"❌ Error extracting marzip: {e}")
            self._cleanup()
            return None, None

    def _cleanup(self):
        """Clean up temporary files."""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                self.temp_dir = None
            except Exception:
                pass

    def _decimal_to_dms(self, decimal_degrees: float, is_latitude: bool = True) -> str:
        """Convert decimal degrees to DMS string format."""
        if is_latitude:
            hemisphere = "N" if decimal_degrees >= 0 else "S"
        else:
            hemisphere = "E" if decimal_degrees >= 0 else "W"

        abs_degrees = abs(decimal_degrees)
        degrees = int(abs_degrees)
        minutes_decimal = (abs_degrees - degrees) * 60

        return f"{degrees}°{minutes_decimal:.3f}'{hemisphere}"

    def update_config(self, verbose: bool = True) -> bool:
        """Update simulation config from marzip file."""
        try:
            if verbose:
                print("🔍 Checking files...")

            # Check if config file exists
            if not os.path.exists(self.config_path):
                if verbose:
                    print(f"❌ Config file not found: {self.config_path}")
                return False

            # Check marzip file
            if not self.marzip_file:
                if verbose:
                    print("❌ No marzip file found")
                return False

            if verbose:
                print(f"📦 Using marzip: {self.marzip_file}")

            # Extract marzip
            yml_file, json_file = self._extract_marzip()

            if not yml_file or not json_file:
                if verbose:
                    print("❌ Could not extract files from marzip")
                return False

            # Load yml file
            with open(yml_file, "r", encoding="utf-8") as f:
                yml_data = yaml.safe_load(f)

            # Load json file
            with open(json_file, "r", encoding="utf-8") as f:
                scenario_data = json.load(f)

            if verbose:
                print("✅ Loaded configuration files")

            # Parse environment settings
            environment_data = yml_data.get("environment", {})
            wind_data = environment_data.get("wind", {})
            current_data = environment_data.get("current", {})
            wave_data = environment_data.get("wave", {})

            environment_config = {
                "wind": {
                    "speed": float(wind_data.get("speed", 0.0)),
                    "direction": float(wind_data.get("direction", 0.0)),
                    "enabled": float(wind_data.get("speed", 0.0)) > 0.0,
                },
                "current": {
                    "speed": float(current_data.get("speed", 0.0)),
                    "direction": float(current_data.get("direction", 0.0)),
                    "enabled": float(current_data.get("speed", 0.0)) > 0.0,
                },
                "wave": {
                    "sea_state": int(wave_data.get("sea_state", 0)),
                    "enabled": int(wave_data.get("sea_state", 0)) > 0,
                },
            }

            # Parse initial conditions
            traffic_situation = scenario_data.get("trafficSituation", {})
            own_ship = traffic_situation.get("ownShip", {})
            initial_data = own_ship.get("initial", {})
            position_data = initial_data.get("position", {})

            latitude = float(position_data.get("latitude", 0.0))
            longitude = float(position_data.get("longitude", 0.0))
            position_dms = f"{self._decimal_to_dms(latitude, True)} {self._decimal_to_dms(longitude, False)}"

            initial_config = {
                "position": position_dms,
                "speed": float(initial_data.get("sog", 0.0)),
                "course": float(initial_data.get("cog", 0.0)),
                "lever": 0.5,
            }

            # Parse target ships (simplified)
            target_ships = traffic_situation.get("targetShips", [])
            target_ships_config = {
                "target_ships": [],
                "total_targets": len(target_ships),
            }

            for target in target_ships:
                try:
                    target_initial = target.get("initial", {})
                    target_position = target_initial.get("position", {})

                    target_lat = float(target_position.get("latitude", 0.0))
                    target_lon = float(target_position.get("longitude", 0.0))
                    target_pos_dms = f"{self._decimal_to_dms(target_lat, True)} {self._decimal_to_dms(target_lon, False)}"

                    target_info = {
                        "position": target_pos_dms,
                        "sog": float(target_initial.get("sog", 0.0)),
                        "cog": float(target_initial.get("cog", 0.0)),
                        "heading": float(target_initial.get("heading", 0.0)),
                    }

                    target_ships_config["target_ships"].append(target_info)
                except Exception:
                    continue

            # Load current config
            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            # Create backup
            backup_path = f"{self.config_path}.backup"
            shutil.copy2(self.config_path, backup_path)

            # Update config
            config["environment"].update(environment_config)
            config["initial"].update(initial_config)
            config["scenario_display"] = target_ships_config

            # Save updated config
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            if verbose:
                print("✅ Configuration updated successfully")
                print(f"💾 Backup created: {backup_path}")

            return True

        except Exception as e:
            if verbose:
                print(f"❌ Error updating configuration: {e}")
            return False

        finally:
            self._cleanup()

    def get_current_mode(self) -> Optional[str]:
        """Get current simulation mode."""
        try:
            if not os.path.exists(self.config_path):
                return None

            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            simulation_set = config.get("simulation_set", {})
            return simulation_set.get("simulation_mode", "normal")

        except Exception:
            return None

    def set_mode(self, mode: str, verbose: bool = True) -> bool:
        """Set simulation mode."""
        if mode not in ["normal", "scenario"]:
            if verbose:
                print(f"❌ Invalid mode: {mode}")
            return False

        try:
            if not os.path.exists(self.config_path):
                if verbose:
                    print(f"❌ Config file not found: {self.config_path}")
                return False

            # Read current config
            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            # Update simulation mode
            if "simulation_set" not in config:
                config["simulation_set"] = {}

            old_mode = config["simulation_set"].get("simulation_mode", "normal")

            if old_mode == mode:
                if verbose:
                    print(f"✅ Already in {mode} mode")
                return True

            config["simulation_set"]["simulation_mode"] = mode

            # Save updated config
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            if verbose:
                print(f"✅ Mode changed: {old_mode} → {mode}")

            return True

        except Exception as e:
            if verbose:
                print(f"❌ Error setting mode: {e}")
            return False

    def print_status(self):
        """Print current status."""
        print("🎮 SCENARIO MANAGER STATUS")
        print("=" * 40)

        # Current mode
        current_mode = self.get_current_mode()
        print(f"📍 Current mode: {current_mode or 'unknown'}")

        # Config file
        if os.path.exists(self.config_path):
            print(f"✅ Config file: {self.config_path}")
        else:
            print(f"❌ Config file: {self.config_path} (missing)")
            return

        # Marzip file
        if self.marzip_file and os.path.exists(self.marzip_file):
            print(f"✅ Marzip file: {self.marzip_file}")
        else:
            print("❌ Marzip file: Not found")

        print()


# CLI interface
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Scenario Manager - Maritime simulation scenario management"
    )
    parser.add_argument(
        "command",
        nargs="?",
        choices=["status", "update", "normal", "scenario"],
        default="status",
        help="Command to execute (default: status)",
    )
    parser.add_argument("--marzip", "-m", type=str, help="Path to marzip file")
    parser.add_argument(
        "--config",
        "-c",
        type=str,
        default="config/simulation_config.json",
        help="Path to simulation config file (default: config/simulation_config.json)",
    )

    args = parser.parse_args()

    # Create manager with specified paths
    manager = ScenarioManager(marzip_file=args.marzip, config_path=args.config)

    if args.command == "status":
        manager.print_status()

    elif args.command == "update":
        success = manager.update_config()
        if success:
            print("\n🎉 Configuration updated successfully!")
        else:
            print("\n❌ Configuration update failed!")

    elif args.command in ["normal", "scenario"]:
        success = manager.set_mode(args.command)
        if success:
            print(f"\n🎉 Mode set to {args.command}!")
        else:
            print(f"\n❌ Failed to set mode to {args.command}!")

    else:
        parser.print_help()
