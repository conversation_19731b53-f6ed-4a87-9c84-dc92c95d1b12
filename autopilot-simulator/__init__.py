"""
Autopilot Simulator Package

A modular autopilot simulation system with the following components:
- AutopilotConfig: Manages autopilot parameters and control settings
- RSAParser: Parses incoming RSA messages from ship simulator
- AutopilotController: Main control logic for heading/turning control
- AutopilotSimulator: Main orchestrator class that coordinates all components

The autopilot receives RSA (Rudder, Speed, Azimuth) messages from the ship simulator
and responds with rudder commands based on the control logic.

Usage:
    from autopilot_simulator import AutopilotSimulator
    
    # Create and run autopilot simulator
    simulator = AutopilotSimulator()
    await simulator.run_simulation()
"""

from .autopilot_simulator import AutopilotSimulator
from .autopilot_config import AutopilotConfig
from .rsa_parser import RSAParser
from .autopilot_controller import AutopilotController
from .communication_manager import CommunicationManager

__version__ = "1.0.0"
__author__ = "Autopilot Simulator Team"

__all__ = [
    "AutopilotSimulator",
    "AutopilotConfig", 
    "RSAParser",
    "AutopilotController",
    "CommunicationManager",
]
