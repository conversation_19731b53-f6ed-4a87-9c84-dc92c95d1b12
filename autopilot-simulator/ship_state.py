"""
Ship state management for autopilot simulator.
Separates command state from actual ship state.
"""

from dataclasses import dataclass
from typing import Optional
import time

@dataclass
class CommandState:
    """
    Command state from HTC messages.
    Contains autopilot control commands.
    """
    # Control commands
    hdg_cmd: float = 0.0           # Heading command [deg]
    turn_mode: str = "N"           # Turn mode: N(Normal), R(Rate), T(Tactical)
    steering_mode: str = "S"       # Steering mode: M(Manual), S(Stand-by), H(Heading)
    turn_dir: str = "R"            # Turn direction: L(Left), R(Right)
    rot_cmd: float = 0.0           # Rate of turn command [deg/min]
    radot_cmd: float = 0.0         # Radius of turn command [n.miles]

    # Limits from HTC
    rudder_limit: float = 35.0     # Rudder limit [deg] from HTC
    turn_limit: float = 60.0       # Turn rate limit [deg/min] from HTC
    
    # Navigation system status
    nas_ready: bool = True         # Navigation system ready (from PAVK)

    # Cross track error (from XTE)
    cross_track_error: float = 0.0 # Cross track error distance [nautical miles]
    xte_direction: str = "L"       # XTE direction: L(Left), R(Right)
    xte_status: str = "V"          # XTE status: A(valid), V(invalid)
    arrival_status: str = "V"      # Arrival circle status: A(entered), V(not entered)

    # Timestamps
    last_htc_time: float = 0.0     # Last HTC message time
    last_pavk_time: float = 0.0    # Last PAVK message time
    last_xte_time: float = 0.0     # Last XTE message time

@dataclass
class ActualState:
    """
    Actual ship state from NMEA sentences (GGA, VTG, etc.).
    Contains real ship position, speed, heading information.
    """
    # Position (from GGA)
    latitude: float = 0.0          # Latitude [deg]
    longitude: float = 0.0         # Longitude [deg]
    altitude: float = 0.0          # Altitude [m]
    gps_quality: int = 0           # GPS quality indicator
    num_satellites: int = 0        # Number of satellites
    hdop: float = 0.0              # Horizontal dilution of precision
    
    # Speed and course (from VTG)
    cog: float = 0.0               # Course over ground [deg]
    sog: float = 0.0               # Speed over ground [knots]
    
    # Heading (from HDT/THS)
    heading: float = 0.0           # True heading [deg]
    
    # Turn rate (from ROT)
    turn_rate: float = 0.0         # Rate of turn [deg/min]
    
    # Time information
    utc_time: str = ""             # UTC time from NMEA
    
    # Timestamps
    last_gga_time: float = 0.0     # Last GGA message time
    last_vtg_time: float = 0.0     # Last VTG message time
    last_hdt_time: float = 0.0     # Last HDT message time
    last_rot_time: float = 0.0     # Last ROT message time

@dataclass
class ControlState:
    """
    Internal control state for autopilot calculations.
    Contains calculated values and control errors.
    """
    # Calculated values (in radians for internal use)
    psi: float = 0.0               # Current heading [rad]
    psi_e: float = 0.0             # Heading error [rad]
    r: float = 0.0                 # Turn rate [rad/s]
    u: float = 0.0                 # Forward speed [m/s]
    
    # Control errors and integrals
    rot_e: float = 0.0             # Turn rate error [rad/s]
    rot_e_sum: float = 0.0         # Turn rate error integral
    rot_e_prev: float = 0.0        # Previous turn rate error
    
    # Output
    rudder_cmd: float = 0.0        # Rudder command [deg]

class ShipStateManager:
    """
    Manages ship state information for autopilot.
    Separates command state, actual state, and control state.
    """
    
    def __init__(self):
        """Initialize ship state manager."""
        self.command_state = CommandState()
        self.actual_state = ActualState()
        self.control_state = ControlState()
        
        # Message timeout settings (seconds)
        self.htc_timeout = 5.0
        self.pavk_timeout = 10.0
        self.xte_timeout = 10.0
        self.nmea_timeout = 2.0
    
    def update_command_state(self, htc_data=None, pavk_data=None, xte_data=None):
        """
        Update command state from HTC, PAVK, and XTE data.

        Args:
            htc_data: HTCData object from parsed HTC message
            pavk_data: PAVKData object from parsed PAVK message
            xte_data: XTEData object from parsed XTE message
        """
        current_time = time.time()

        if htc_data:
            self.command_state.hdg_cmd = htc_data.hdg_cmd
            self.command_state.steering_mode = htc_data.steering_mode
            self.command_state.turn_dir = htc_data.turn_dir
            self.command_state.rot_cmd = htc_data.rot_cmd
            self.command_state.radot_cmd = htc_data.radot_cmd
            self.command_state.rudder_limit = htc_data.rudder_limit  # From HTC
            self.command_state.turn_limit = htc_data.turn_limit      # From HTC

            # turn_mode는 H 모드일 때만 HTC 값 사용, 다른 모드는 기본값 "N" 유지
            if htc_data.steering_mode == "H":
                self.command_state.turn_mode = htc_data.turn_mode
            else:
                self.command_state.turn_mode = "N"  # M, S 모드는 항상 N-mode

            self.command_state.last_htc_time = current_time

        if pavk_data:
            self.command_state.nas_ready = pavk_data.nas_ready
            self.command_state.last_pavk_time = current_time

        if xte_data:
            self.command_state.cross_track_error = xte_data.cross_track_error
            self.command_state.xte_direction = xte_data.direction
            self.command_state.xte_status = xte_data.status_1  # Use status_1 as primary status
            self.command_state.arrival_status = xte_data.arrival_status
            self.command_state.last_xte_time = current_time
    
    def update_actual_state(self, nmea_data: dict):
        """
        Update actual state from parsed NMEA data.
        
        Args:
            nmea_data: Dictionary containing parsed NMEA sentence data
        """
        current_time = time.time()
        
        # Update from GGA (position)
        if 'GGA' in nmea_data:
            gga = nmea_data['GGA']
            self.actual_state.latitude = gga.get('latitude', 0.0)
            self.actual_state.longitude = gga.get('longitude', 0.0)
            self.actual_state.altitude = gga.get('altitude', 0.0)
            self.actual_state.gps_quality = gga.get('quality', 0)
            self.actual_state.num_satellites = gga.get('num_satellites', 0)
            self.actual_state.hdop = gga.get('hdop', 0.0)
            self.actual_state.utc_time = gga.get('utc_time', '')
            self.actual_state.last_gga_time = current_time
        
        # Update from VTG (speed and course)
        if 'VTG' in nmea_data:
            vtg = nmea_data['VTG']
            self.actual_state.cog = vtg.get('cog_true', 0.0)
            self.actual_state.sog = vtg.get('sog_knots', 0.0)
            self.actual_state.last_vtg_time = current_time
        
        # Update from HDT (heading)
        if 'HDT' in nmea_data:
            hdt = nmea_data['HDT']
            self.actual_state.heading = hdt.get('heading', 0.0)
            self.actual_state.last_hdt_time = current_time

        # Update from HSC (heading steering command)
        if 'HSC' in nmea_data:
            hsc = nmea_data['HSC']
            self.actual_state.heading = hsc.get('heading', 0.0)
            self.actual_state.last_hdt_time = current_time  # Use same timestamp field
        
        # Update from ROT (turn rate)
        if 'ROT' in nmea_data:
            rot = nmea_data['ROT']
            self.actual_state.turn_rate = rot.get('turn_rate', 0.0)
            self.actual_state.last_rot_time = current_time
    
    def is_command_valid(self) -> bool:
        """Check if command state is valid (not timed out)."""
        current_time = time.time()
        htc_valid = (current_time - self.command_state.last_htc_time) < self.htc_timeout
        return htc_valid and self.command_state.nas_ready
    
    def is_actual_state_valid(self) -> bool:
        """Check if actual state is valid (not timed out)."""
        current_time = time.time()
        gga_valid = (current_time - self.actual_state.last_gga_time) < self.nmea_timeout
        vtg_valid = (current_time - self.actual_state.last_vtg_time) < self.nmea_timeout
        hdt_valid = (current_time - self.actual_state.last_hdt_time) < self.nmea_timeout
        return gga_valid and vtg_valid and hdt_valid
    
    def get_status_summary(self) -> dict:
        """Get status summary for monitoring."""
        current_time = time.time()
        
        return {
            "command_state": {
                "hdg_cmd": self.command_state.hdg_cmd,
                "turn_mode": self.command_state.turn_mode,
                "steering_mode": self.command_state.steering_mode,
                "nas_ready": self.command_state.nas_ready,
                "htc_age": current_time - self.command_state.last_htc_time,
                "valid": self.is_command_valid()
            },
            "actual_state": {
                "latitude": self.actual_state.latitude,
                "longitude": self.actual_state.longitude,
                "heading": self.actual_state.heading,
                "cog": self.actual_state.cog,
                "sog": self.actual_state.sog,
                "turn_rate": self.actual_state.turn_rate,
                "gga_age": current_time - self.actual_state.last_gga_time,
                "valid": self.is_actual_state_valid()
            },
            "control_state": {
                "psi_e_deg": self.control_state.psi_e * 180.0 / 3.14159,
                "rot_e": self.control_state.rot_e,
                "rudder_cmd": self.control_state.rudder_cmd
            }
        }
