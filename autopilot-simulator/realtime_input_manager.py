"""
Real-time input manager for autopilot simulator.
Monitors Redis for real-time parameter updates.
"""

import json
import os
import time
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from utils.redis_manager import RedisManager


@dataclass
class RealtimeInputData:
    """Data class for real-time input parameters."""
    steering_mode: str = "S"        # M(Manual), S(Stand-by), H(Heading)
    hdg_cmd: float = 0.0           # Heading command [degrees]
    rudder_cmd: float = 0.0        # Rudder command [degrees]
    tki_status: str = "A"          # A(valid), V(invalid)
    maker: str = "TKM"             # TKM or YDK
    last_updated: str = ""         # Last update timestamp


class RealtimeInputManager:
    """
    Manager for real-time input parameters.

    Monitors Redis for parameter updates and provides
    thread-safe access to the current parameter values.
    """

    def __init__(self):
        """
        Initialize real-time input manager.
        Redis configuration is loaded from environment variables.
        """
        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Load Redis configuration from environment variables
        self.redis_config = self._load_redis_config_from_env()

        # Initialize Redis manager
        self.redis_manager = RedisManager(**self.redis_config)

        # Redis key for user input
        self.redis_key = "autopilot:user_input"

        # Initialize data
        self.data = RealtimeInputData()
        self.check_interval = 0.05  # Check Redis every 50ms for faster response
        self.last_check_time = 0.0
        self.last_data_hash = ""

        # Data persistence settings (5틱 = 5번의 update 호출)
        self.data_hold_ticks = 5
        self.current_tick_count = 0
        self.last_valid_data = None
        self.ticks_since_last_valid = 0

        # Change delay settings (5틱 지연)
        self.change_delay_ticks = 5
        self.pending_data = None
        self.ticks_since_change = 0

        # Load initial configuration
        self._load_config()

    def _load_redis_config_from_env(self) -> dict:
        """
        Load Redis configuration from environment variables.

        Returns:
            Dictionary containing Redis configuration
        """
        def str_to_bool(value: str) -> bool:
            """Convert string to boolean."""
            return value.lower() in ('true', '1', 'yes', 'on')

        try:
            config = {
                "host": os.getenv("REDIS_HOST", "localhost"),
                "port": int(os.getenv("REDIS_PORT", "6379")),
                "password": os.getenv("REDIS_PASSWORD", ""),
                "ssl": str_to_bool(os.getenv("REDIS_SSL", "false")),
                "options": {
                    "auto_conn": str_to_bool(os.getenv("REDIS_AUTO_CONN", "true")),
                    "auto_utc_set": str_to_bool(os.getenv("REDIS_AUTO_UTC_SET", "false"))
                }
            }
            self.logger.info(f"Loaded Redis config from environment variables: host={config['host']}, port={config['port']}")
            return config
        except Exception as e:
            self.logger.error(f"Error loading Redis config from environment: {e}, using defaults")
            return {
                "host": "localhost",
                "port": 6379,
                "password": "",
                "ssl": False,
                "options": {"auto_conn": True, "auto_utc_set": False}
            }

    def _load_config(self, force_reload: bool = False) -> bool:
        """
        Load configuration from Redis.

        Returns:
            True if configuration was loaded successfully, False otherwise
        """
        try:
            # Get data from Redis
            redis_data = self.redis_manager.GetRedisMsg(self.redis_key)

            if redis_data is None:
                # No new data - check if we should hold previous data
                self.ticks_since_last_valid += 1

                if self.last_valid_data is not None and self.ticks_since_last_valid <= self.data_hold_ticks:
                    # Hold previous valid data for specified ticks
                    self.logger.debug(f"Holding previous data (tick {self.ticks_since_last_valid}/{self.data_hold_ticks})")
                    return False  # Keep using current data
                else:
                    # Exceeded hold time or no previous data
                    if not hasattr(self, '_redis_warning_shown'):
                        self.logger.warning(f"No data found in Redis key: {self.redis_key}")
                        self._redis_warning_shown = True
                    return False

            # Convert Redis data to JSON string for hash comparison
            data_str = json.dumps(redis_data, sort_keys=True)
            current_hash = hash(data_str)

            # Check if data has changed
            if not force_reload and current_hash == self.last_data_hash:
                return False  # No changes

            # Map Redis data structure to internal data structure
            # Redis: steering_mode, heading_command, rudder_command, maker
            # Internal: steering_mode, hdg_cmd, rudder_cmd, tki_status, maker
            self.data.steering_mode = redis_data.get("steering_mode", "M")
            self.data.hdg_cmd = float(redis_data.get("heading_command", 0.0))
            self.data.rudder_cmd = float(redis_data.get("rudder_command", 0.0))
            self.data.maker = redis_data.get("maker", "TKM")  # Default to TKM if not provided
            self.data.last_updated = redis_data.get("last_updated", "")

            # Validate values
            self._validate_data()

            # Check if this is a new change
            if self.last_data_hash != current_hash:
                # New data detected - start delay timer
                self.pending_data = redis_data.copy()
                self.ticks_since_change = 0
                self.logger.debug(f"New data detected, starting {self.change_delay_ticks}-tick delay")
                return False  # Don't apply change immediately

            # Store as last valid data and reset tick counter
            self.last_valid_data = redis_data.copy()
            self.ticks_since_last_valid = 0

            self.last_data_hash = current_hash
            self.logger.debug(f"Loaded config from Redis: {redis_data}")
            return True

        except Exception as e:
            self.logger.error(f"Error loading real-time input config from Redis: {e}")
            return False
    
    def _validate_data(self) -> None:
        """Validate and clamp input data to acceptable ranges."""
        # Validate steering mode
        if self.data.steering_mode not in ["M", "S", "H"]:
            print(f"Warning: Invalid steering_mode '{self.data.steering_mode}', using 'S'")
            self.data.steering_mode = "S"
            
        # Validate heading command (0-360 degrees)
        if not (0.0 <= self.data.hdg_cmd <= 360.0):
            self.data.hdg_cmd = max(0.0, min(360.0, self.data.hdg_cmd))
            print(f"Warning: Heading command clamped to {self.data.hdg_cmd}°")
            
        # Validate rudder command (-35 to +35 degrees)
        if not (-35.0 <= self.data.rudder_cmd <= 35.0):
            self.data.rudder_cmd = max(-35.0, min(35.0, self.data.rudder_cmd))
            print(f"Warning: Rudder command clamped to {self.data.rudder_cmd}°")
            
        # Validate TKI status
        if self.data.tki_status not in ["A", "V"]:
            print(f"Warning: Invalid tki_status '{self.data.tki_status}', using 'A'")
            self.data.tki_status = "A"

        # Validate maker
        if self.data.maker not in ["TKM", "YDK"]:
            print(f"Warning: Invalid maker '{self.data.maker}', using 'TKM'")
            self.data.maker = "TKM"
    
    def update(self) -> bool:
        """
        Update configuration if Redis data has changed.
        Implements data holding logic to maintain previous values for specified ticks.

        Returns:
            True if configuration was updated, False otherwise
        """
        current_time = time.time()

        # Increment tick counter for data holding logic
        self.current_tick_count += 1

        # Check Redis only at specified intervals
        if current_time - self.last_check_time < self.check_interval:
            return False

        self.last_check_time = current_time

        # Force reload every 5 seconds to avoid timing issues
        force_reload = (current_time - getattr(self, '_last_force_reload', 0)) > 5.0
        if force_reload:
            self._last_force_reload = current_time

        # Check for pending changes
        if self.pending_data is not None:
            self.ticks_since_change += 1
            if self.ticks_since_change >= self.change_delay_ticks:
                # Apply delayed change
                self.data.steering_mode = self.pending_data.get("steering_mode", "M")
                self.data.hdg_cmd = float(self.pending_data.get("heading_command", 0.0))
                self.data.rudder_cmd = float(self.pending_data.get("rudder_command", 0.0))
                self.data.maker = self.pending_data.get("maker", "TKM")
                self.data.last_updated = self.pending_data.get("last_updated", "")

                self._validate_data()

                self.last_valid_data = self.pending_data.copy()
                self.ticks_since_last_valid = 0
                self.pending_data = None

                print(f"🔧 DELAYED CHANGE APPLIED after {self.change_delay_ticks} ticks")
                return True

        result = self._load_config(force_reload)

        # Debug data holding status
        if hasattr(self, '_debug_hold_counter'):
            self._debug_hold_counter += 1
        else:
            self._debug_hold_counter = 1

        if self._debug_hold_counter % 20 == 1 and self.ticks_since_last_valid > 0:
            print(f"🔧 DATA HOLD STATUS: holding for {self.ticks_since_last_valid}/{self.data_hold_ticks} ticks")

        return result
    
    def get_data(self) -> RealtimeInputData:
        """
        Get current real-time input data.
        
        Returns:
            Current RealtimeInputData object
        """
        return self.data
    
    def get_steering_mode(self) -> str:
        """Get current steering mode."""
        return self.data.steering_mode
    
    def get_hdg_cmd(self) -> float:
        """Get current heading command."""
        return self.data.hdg_cmd
    

    
    def get_rudder_cmd(self) -> float:
        """Get current rudder command."""
        return self.data.rudder_cmd
    
    def get_tki_status(self) -> str:
        """Get current TKI status."""
        return self.data.tki_status
    
    def save_current_config(self) -> bool:
        """
        Save current configuration to file.
        
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            config = {
                "steering_mode": self.data.steering_mode,
                "hdg_cmd": self.data.hdg_cmd,
                "rudder_cmd": self.data.rudder_cmd,
                "tki_status": self.data.tki_status,
                "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                "comments": {
                    "steering_mode": "M(Manual), S(Stand-by), H(Heading)",
                    "hdg_cmd": "Heading command in degrees",
                    "rudder_cmd": "Rudder command in degrees (-35 to +35)",
                    "tki_status": "A(valid), V(invalid)"
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
            return True
            
        except Exception as e:
            print(f"❌ Error saving real-time input config: {e}")
            return False
