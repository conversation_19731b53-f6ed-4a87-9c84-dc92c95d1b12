"""
NMEA sentence generator for autopilot simulator.
Generates autopilot output sentences: RSA, ROR, TXT, TKM, TKI, HTD.
Supports both TKM and YDK makers with different message formats.
"""

import math
import time
from typing import Dict, Any, Optional

def nmea_checksum(sentence: str) -> str:
    """
    Calculate NMEA checksum.
    
    Args:
        sentence: NMEA sentence without $ and *checksum
        
    Returns:
        Two-character hexadecimal checksum
    """
    checksum = 0
    for char in sentence:
        checksum ^= ord(char)
    return f"{checksum:02X}"

class NMEAGenerator:
    """
    Generates NMEA sentences for autopilot output.
    
    Generates:
    - HTD: Heading/Turn Control Data
    - RSA: Rudder Sensor Angle
    - ROR: Rudder Order
    - TXT: Text Transmission
    - TKM: Track Control Message
    - TKI: Track Control Information
    """
    
    def __init__(self):
        """Initialize NMEA generator."""
        pass
    
    def generate_autopilot_sentences(self, command_state, actual_state, control_state, realtime_input=None, pavk_status="V", rudder_command=None, system_status="valid", ext_ready=False, control_mode_active=False) -> Dict[str, str]:
        """
        Generate all autopilot NMEA sentences based on maker type.

        Args:
            command_state: CommandState object
            actual_state: ActualState object
            control_state: ControlState object
            realtime_input: RealtimeInputManager object (optional)
            pavk_status: PAVK status from received messages (A/V)
            rudder_command: Calculated rudder command in radians
            system_status: System status from _get_system_status ("valid"/"invalid")
            ext_ready: External ready status (True/False)

        Returns:
            Dictionary of NMEA sentences
        """
        sentences = {}

        # Get maker type from realtime input
        maker = "TKM"  # Default
        if realtime_input:
            rt_data = realtime_input.get_data()
            maker = rt_data.maker

        # Generate common sentences for both makers
        sentences["HTD"] = self._generate_htd(command_state, actual_state, control_state, realtime_input, pavk_status, ext_ready, control_mode_active)
        sentences["RSA"] = self._generate_rsa(control_state, realtime_input, rudder_command)
        sentences["ROR"] = self._generate_ror(control_state, realtime_input, rudder_command)

        # Generate maker-specific sentences
        if maker == "YDK":
            # YDK maker: Send PYDK instead of TXT, TKI, TKM
            sentences["PYDK"] = self._generate_pydk(system_status, ext_ready)
        else:
            # TKM maker: Send standard TXT, TKI, TKM
            sentences["TXT"] = self._generate_txt(command_state)
            sentences["TKM"] = self._generate_tkm()
            sentences["TKI"] = self._generate_tki(system_status)

        return sentences
    
    def _generate_htd(self, command_state, actual_state, control_state, realtime_input=None, pavk_status="V", ext_ready=False, control_mode_active=False) -> str:
        """Generate HTD (Heading/Turn Control Data) sentence."""
        # HTD format: $--HTD,A,x.x,a,a,a,x.x,x.x,x.x,x.x,x.x,x.x,x.x,a,A,A,A,x.x*hh
        # Fields: status,rudder_angle,rudder_dir,steering_mode,turn_mode,rudder_limit,turn_limit,radot_cmd,rot_cmd,hdg_cmd,cross_track_error,waypoint_distance,mode_indicator,status1,status2,status3,vessel_hdg

        # Determine steering mode based on control mode (simplified logic)
        if control_mode_active:
            # Control mode: always send H
            steering_mode = "H"
        elif realtime_input:
            # Non-control mode: follow realtime_input steering_mode
            steering_mode = realtime_input.get_steering_mode()
            if steering_mode == "H" : ## override
                steering_mode = "S"
        else:
            # Fallback
            steering_mode = command_state.steering_mode

        # Determine rudder and heading commands
        if realtime_input:
            # Determine rudder command based on steering mode
            if steering_mode == "M":
                # Manual mode: use real-time input rudder command directly
                rudder_cmd_val = realtime_input.get_rudder_cmd()
            else:
                # H/S mode: use calculated rudder command
                rudder_cmd_val = control_state.rudder_cmd

            # Determine heading command based on control mode
            if control_mode_active:
                # Control mode: use HTC heading command
                hdg_cmd_val = command_state.hdg_cmd
            else:
                # Non-control mode: use real-time input heading command
                hdg_cmd_val = realtime_input.get_hdg_cmd()
        else:
            # Fallback to original values
            rudder_cmd_val = control_state.rudder_cmd
            hdg_cmd_val = command_state.hdg_cmd

        # Format fields according to HTD standard (matching HTC format)
        override = "V"  # Always V (hardcoded)
        rudder_angle = f"{abs(rudder_cmd_val):.1f}"
        rudder_dir = "R" if rudder_cmd_val > 0 else "L"
        turn_mode = command_state.turn_mode

        # Rudder limit logic:
        # 1. Default: use autopilot_config.json value
        # 2. If YDK maker and S->H mode change condition: use HTC value

        # Get default rudder limit from config
        try:
            from autopilot_config import AutopilotConfig
            config = AutopilotConfig()
            rudder_limit_default = config.control_parameters.get('rudder_limit', 35.0)
        except:
            rudder_limit_default = 35.0  # Fallback

        rudder_limit = f"{rudder_limit_default:.1f}"

        # Check YDK special condition for rudder limit override
        if realtime_input:
            rt_data = realtime_input.get_data()
            # If YDK maker and the S->H condition would be met, use HTC rudder limit
            if (rt_data.maker == "YDK" and
                hasattr(command_state, 'rudder_limit') and
                command_state.rudder_limit > 0):
                rudder_limit = f"{command_state.rudder_limit:.1f}"

        # Turn limit: always use HTC value if available, otherwise default
        turn_limit = "60.0"  # Default
        if hasattr(command_state, 'turn_limit') and command_state.turn_limit > 0:
            turn_limit = f"{command_state.turn_limit:.1f}"
        radot_cmd = f"{command_state.radot_cmd:.1f}"
        rot_cmd = f"{command_state.rot_cmd:.3f}"
        hdg_cmd = f"{hdg_cmd_val:.1f}"

        # Navigation values (dynamic based on simulation)
        import time
        sim_time = time.time() % 100  # Use time for variation

        # Cross track error: varies between -1.0 to 1.0 nm
        cross_track_error = f"{0.5 * math.sin(sim_time * 0.1):.1f}"

        # Waypoint distance: decreases over time (simulating approach)
        base_distance = 50.0 - (sim_time * 0.1)  # Slowly decreasing
        waypoint_distance = f"{max(base_distance, 5.0):.1f}"  # Minimum 5.0 nm

        mode_indicator = "T"  # T = True, M = Magnetic
        status1 = "A"  # Status indicators
        status2 = "A"
        status3 = "A"
        vessel_hdg = f"{actual_state.heading:.1f}"

        htd_temp = (f"$AGHTD,{override},{rudder_angle},{rudder_dir},{steering_mode},"
                   f"{turn_mode},{rudder_limit},{turn_limit},{radot_cmd},{rot_cmd},"
                   f"{hdg_cmd},{cross_track_error},{waypoint_distance},{mode_indicator},"
                   f"{status1},{status2},{status3},{vessel_hdg}")

        checksum = nmea_checksum(htd_temp[1:])  # Remove $ for checksum
        return f"{htd_temp}*{checksum}\r\n"
    
    def _generate_rsa(self, control_state, realtime_input=None, rudder_command=None) -> str:
        """Generate RSA (Rudder Sensor Angle) sentence."""
        # IIRSA format: $IIRSA,rudder_val,A,rudder_val,V

        # Use passed rudder command (convert from radians to degrees)
        if rudder_command is not None:
            rudder_deg = rudder_command 
            rudder_val = f"{rudder_deg:.1f}"
        else:
            # Fallback to control_state
            rudder_deg = control_state.rudder_cmd 
            rudder_val = f"{rudder_deg:.1f}"

        rsa_temp = f"$IIRSA,{rudder_val},A,{rudder_val},A"

        checksum = nmea_checksum(rsa_temp[1:])  # Remove $ for checksum
        return f"{rsa_temp}*{checksum}\r\n"
    
    def _generate_ror(self, control_state, realtime_input=None, rudder_command=None) -> str:
        """Generate ROR (Rudder Order) sentence."""
        # AGROR format: $AGROR,rudder_val,A,rudder_val,V,B

        # Use passed rudder command (convert from radians to degrees)
        if rudder_command is not None:
            rudder_deg = rudder_command 
            rudder_val = f"{rudder_deg:.1f}"
        else:
            # Fallback to control_state
            rudder_deg = control_state.rudder_cmd 
            rudder_val = f"{rudder_deg:.1f}"

        ror_temp = f"$AGROR,{rudder_val},A,{rudder_val},A,B"

        checksum = nmea_checksum(ror_temp[1:])  # Remove $ for checksum
        return f"{ror_temp}*{checksum}\r\n"
    
    def _generate_txt(self, command_state) -> str:
        """Generate TXT (Text Transmission) sentence."""
        # AGTXT format: $AGTXT,01,01,01,SM=HC PID
        
        # Generate status message based on steering mode
        if command_state.steering_mode == "H":
            status_msg = "SM=HC PID"  # Heading Control PID
        elif command_state.steering_mode == "S":
            status_msg = "SM=STBY"    # Stand-by
        elif command_state.steering_mode == "M":
            status_msg = "SM=MAN"     # Manual
        else:
            status_msg = "SM=UNK"     # Unknown
        
        txt_temp = f"$AGTXT,01,01,01,{status_msg}"
        
        checksum = nmea_checksum(txt_temp[1:])  # Remove $ for checksum
        return f"{txt_temp}*{checksum}\r\n"
    
    def _generate_tkm(self) -> str:
        """Generate TKM (Track Control Message) sentence."""
        # PTKM format: $PTKM,HDSEL,G1,A,R
        
        tkm_temp = "$PTKM,HDSEL,G1,A,R"
        
        checksum = nmea_checksum(tkm_temp[1:])  # Remove $ for checksum
        return f"{tkm_temp}*{checksum}\r\n"
    
    def _generate_tki(self, system_status="valid") -> str:
        """Generate TKI (Track Control Information) sentence."""
        # PTKI format: $PTKI,AGNAS,A/V

        # Status based on system status: A if valid, V if invalid
        if system_status == "invalid":
            tki_status = "V"
        else:
            tki_status = "A"

        tki_temp = f"$PTKI,AGNAS,{tki_status}"

        checksum = nmea_checksum(tki_temp[1:])  # Remove $ for checksum
        return f"{tki_temp}*{checksum}\r\n"

    def _generate_pydk(self, system_status="valid", ext_ready=False) -> str:
        """
        Generate PYDK sentence for YDK maker.

        Format: $PYDK,APL,status,param1,param2,param3*checksum
        Based on regex: \$(PYDK,APL|AGAPL),([AV]?),([^,\$]*),([^,\$]*),([^,\$]*)\*[a-zA-Z0-9]{2}

        Args:
            system_status: System status ("valid"/"invalid")
            ext_ready: External ready status (True/False)

        Returns:
            PYDK sentence string
        """
        # Status based on ext_ready and system status
        if system_status == "invalid" :
            status = "V"
        else:
            status = "A"

        # Generate PYDK message
        pydk_temp = f"$PYDK,APL,{status},,,"

        # Calculate checksum
        checksum = nmea_checksum(pydk_temp[1:])  # Remove $ for checksum

        return f"{pydk_temp}*{checksum}\r\n"

# Utility functions for backward compatibility
def make_autopilot_nmea_sentences(command_state, actual_state, control_state) -> Dict[str, str]:
    """
    Generate autopilot NMEA sentences (backward compatibility function).
    
    Args:
        command_state: CommandState object
        actual_state: ActualState object
        control_state: ControlState object
        
    Returns:
        Dictionary of NMEA sentences
    """
    generator = NMEAGenerator()
    return generator.generate_autopilot_sentences(command_state, actual_state, control_state)
