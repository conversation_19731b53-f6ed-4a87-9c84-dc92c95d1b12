# Autopilot Simulator

자동조타장치 시뮬레이터로, 선박 시뮬레이터(***********)로부터 RSA 메시지를 받아서 조타 명령(rudder command)을 계산하여 전송합니다.

## 개요

이 시뮬레이터는 `fn_control_apl` 제어 로직을 구현하여 다음과 같은 기능을 제공합니다:

- **RSA 메시지 수신**: 선박 시뮬레이터로부터 멀티캐스트로 RSA 데이터 수신
- **자동조타 제어**: N/R/T 모드 지원하는 조타 제어 알고리즘
- **조타 명령 전송**: 계산된 조타각을 선박 시뮬레이터로 전송

## 제어 모드

### N-mode (Normal/Heading Control)
- 침로 제어 모드
- 목표 침로와 현재 침로의 차이를 기반으로 조타각 계산
- `rudder_cmd = -(psi_e * kp_psi + r * kd_psi)`

### R-mode (Rate of Turn Control)  
- 선회율 제어 모드
- 목표 선회율과 현재 선회율의 차이를 기반으로 조타각 계산

### T-mode (Tactical Diameter Control)
- 전술직경 제어 모드
- 목표 반경을 선회율로 변환 후 R-mode와 동일한 제어

## 아키텍처

```
autopilot-simulator/
├── __init__.py                 # 패키지 초기화
├── autopilot_simulator.py      # 메인 시뮬레이터 클래스
├── autopilot_config.py         # 설정 관리
├── autopilot_controller.py     # fn_control_apl 제어 로직
├── rsa_parser.py              # RSA 메시지 파싱
├── communication_manager.py    # UDP 통신 관리
└── README.md                  # 이 파일
```

## 설정 파일

### config/autopilot_config.json
```json
{
  "control_parameters": {
    "kp_psi": 1.0,      // 침로 비례 게인
    "kd_psi": 10.0,     // 침로 미분 게인  
    "kp_rot": 1400,     // 선회율 비례 게인
    "ki_rot": 6000,     // 선회율 적분 게인
    "kff_rot": 1.0,     // 선회율 피드포워드 게인
    "rudder_limit": 35  // 조타각 제한 (도)
  },
  "ship_parameters": {
    "ship_type": 2      // 1: Ferry, 2: Container, 3: Tanker
  }
}
```

### config/autopilot_communication.json
```json
{
  "rsa_receiver": {
    "multicast_ip": "***********",  // RSA 수신 멀티캐스트 IP
    "port": 60001,                  // RSA 수신 포트
    "interface_ip": "***********"   // 네트워크 인터페이스 IP
  },
  "rudder_sender": {
    "target_ip": "***********",     // 조타 명령 전송 대상 IP
    "port": 59902                   // 조타 명령 전송 포트
  }
}
```

## 사용법

### 기본 실행
```bash
python run_autopilot.py
```

### 프로그래밍 방식 사용
```python
from autopilot_simulator import AutopilotSimulator

# 시뮬레이터 생성
simulator = AutopilotSimulator()

# 비동기 실행
await simulator.run_simulation()
```

## 통신 프로토콜

### RSA 메시지 수신 (멀티캐스트)
- **소스**: 선박 시뮬레이터 (***********)
- **프로토콜**: UDP 멀티캐스트 (***********:60001)
- **형식**: JSON 또는 CSV
- **내용**: 선박 상태 정보 (침로, 속도, 선회율 등)

### 조타 명령 전송 (유니캐스트)
- **대상**: 선박 시뮬레이터 (***********:59902)  
- **프로토콜**: UDP 유니캐스트
- **형식**: 단순 텍스트 (조타각 값)
- **예시**: `"-15.25"` (조타각 -15.25도)

## 제어 알고리즘

### fn_control_apl 구현
```python
def calculate_rudder_command(self):
    # 침로 오차 계산
    psi_e = logic_bound_abs_pi(psi - hdg_cmd)
    
    if turn_mode == "N":
        # N-mode: 침로 제어
        rudder_cmd = -(psi_e * kp_psi + r * kd_psi)
    else:
        # R/T-mode: 선회 제어
        if turn_mode == "T":
            # 반경 명령을 선회율 명령으로 변환
            rot_cmd = u / radot_cmd / 60 * radian_to_degree
        
        rot_e = r - rot_cmd * degree_to_radian / 60
        rot_e_sum += rot_e
        
        # 선박 타입별 피드포워드
        feedforward_rudder_cmd = rot_cmd / kff_rot

        rudder_cmd = (feedforward_rudder_cmd
                     - rot_e * kp_rot
                     - rot_e_sum * ki_rot * abs(rot_e))

    # 조타각 제한
    rudder_cmd = clip(rudder_cmd, -35, 35)
    return rudder_cmd
```

## 모니터링

시뮬레이터는 다음과 같은 상태 정보를 주기적으로 출력합니다:

```
Step   1250 | Mode: N | HDG:  90.5° | CMD:  90.0° | ERR:  -0.5° | ROT:   0.2°/min | RUD:   2.1° | SPD: 12.5kn
```

- **Step**: 시뮬레이션 스텝 번호
- **Mode**: 제어 모드 (N/R/T)
- **HDG**: 현재 침로 (도)
- **CMD**: 목표 침로 (도)  
- **ERR**: 침로 오차 (도)
- **ROT**: 선회율 (도/분)
- **RUD**: 조타각 명령 (도)
- **SPD**: 선박 속도 (노트)

## 의존성

- `numpy`: 수치 계산
- `asyncio`: 비동기 처리
- `utils.udp_socket_base`: UDP 소켓 통신 (기존 ship_simulator와 공유)

## 문제 해결

### 멀티캐스트 수신 문제
```bash
# Docker 환경에서 멀티캐스트 라우팅 설정
ip route add ***********/24 dev eth0
```

### 포트 충돌
- RSA 수신 포트: 60001
- 조타 명령 전송 포트: 59902
- 필요시 설정 파일에서 포트 변경 가능

### 제어 파라미터 튜닝
- `kp_psi`, `kd_psi`: 침로 제어 게인
- `kp_rot`, `ki_rot`: 선회 제어 게인
- `kff_rot`: 피드포워드 게인 (선박 타입별 조정)
