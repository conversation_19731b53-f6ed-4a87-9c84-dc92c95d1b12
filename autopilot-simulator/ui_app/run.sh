#!/bin/bash
# Autopilot Simulator UI 실행 스크립트

set -e

echo "🚢 Autopilot Simulator UI 시작"
echo "================================"

# 환경변수 파일 로드
if [ -f .env ]; then
    echo "📋 환경변수 파일 로드: .env"
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️  환경변수 파일이 없습니다. 기본값을 사용합니다."
    echo "   .env.example을 .env로 복사하여 설정을 변경할 수 있습니다."
fi

# Redis 연결 확인
echo "🔍 Redis 연결 확인..."
REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}

if command -v redis-cli &> /dev/null; then
    if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping &> /dev/null; then
        echo "✅ Redis 연결 성공: $REDIS_HOST:$REDIS_PORT"
    else
        echo "❌ Redis 연결 실패: $REDIS_HOST:$REDIS_PORT"
        echo "   Redis 서버가 실행 중인지 확인하세요."
    fi
else
    echo "⚠️  redis-cli가 설치되지 않아 연결을 확인할 수 없습니다."
fi

# Python 의존성 확인
echo "🐍 Python 의존성 확인..."
if [ -f requirements.txt ]; then
    pip install -r requirements.txt
    echo "✅ 의존성 설치 완료"
else
    echo "❌ requirements.txt 파일을 찾을 수 없습니다."
    exit 1
fi

# GUI 환경 확인 (Linux)
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if [ -z "$DISPLAY" ]; then
        echo "⚠️  DISPLAY 환경변수가 설정되지 않았습니다."
        echo "   GUI 애플리케이션 실행에 문제가 있을 수 있습니다."
    else
        echo "🖥️  DISPLAY: $DISPLAY"
    fi
fi

# 애플리케이션 실행
echo "🚀 Autopilot Simulator UI 실행 중..."
echo "================================"

# Python 경로 설정
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 애플리케이션 실행
python ui_main.py

echo "👋 Autopilot Simulator UI 종료"
