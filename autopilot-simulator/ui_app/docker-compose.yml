# Autopilot Simulator UI Docker Compose
version: '3.8'

services:
  autopilot-ui:
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    container_name: autopilot-ui:v0.1
    environment:
      # Redis 설정
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_ENABLE_SSL=${REDIS_ENABLE_SSL:-false}
      
      # GUI 설정
      - DISPLAY=${DISPLAY:-:0}
      - QT_X11_NO_MITSHM=1
      
    volumes:
      # X11 소켓 마운트 (Linux GUI 지원)
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /etc/localtime:/etc/localtime:ro
      
    networks:
      - autopilot-network
      
    depends_on:
      - redis
      
    restart: unless-stopped
    
    # GUI 애플리케이션이므로 interactive 모드
    stdin_open: true
    tty: true

  # Redis 서비스 (개발용)
  redis:
    image: redis:7-alpine
    container_name: autopilot-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - autopilot-network
    restart: unless-stopped
    command: redis-server --appendonly yes

networks:
  autopilot-network:
    driver: bridge

volumes:
  redis-data:
    driver: local
