"""
Autopilot Simulator UI Main Application
해양 autopilot 컨트롤 패널 UI
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 모듈 경로 추가
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Redis 연결 설정
try:
    from redis_client import redis_manager

    REDIS_AVAILABLE = True
    logging.info("Redis client imported successfully")
except ImportError as e:
    logging.error(f"Redis client import error: {e}")
    redis_manager = None
    REDIS_AVAILABLE = False

try:
    from widgets.autopilot_panel import AutopilotPanel
except ImportError as e:
    print(f"Import error: {e}")
    AutopilotPanel = None


class AutopilotSimulatorUI(QMainWindow):
    """Autopilot Simulator UI 메인 윈도우"""

    def __init__(self):
        super().__init__()
        self.redis_manager = None
        self.setup_redis_connection()
        self.setup_ui()
        self.setup_window()

    def setup_redis_connection(self):
        """Redis 연결 설정"""
        if REDIS_AVAILABLE and redis_manager:
            try:
                if redis_manager.is_connected:
                    self.redis_manager = redis_manager
                    logging.info("✅ Redis connection established successfully")
                    print("✅ Redis connection established successfully")
                else:
                    logging.warning("❌ Redis connection failed")
                    print("❌ Redis connection failed")
            except Exception as e:
                logging.error(f"Redis connection error: {e}")
                print(f"❌ Redis connection error: {e}")
        else:
            logging.warning("Redis manager not available")
            print("❌ Redis manager not available")

    def setup_ui(self):
        """UI 구성"""
        # 메인 autopilot 패널을 중앙 위젯으로 설정
        if AutopilotPanel:
            self.autopilot_panel = AutopilotPanel()
            self.setCentralWidget(self.autopilot_panel)
        else:
            # 임시 라벨 표시
            label = QLabel("Autopilot Panel Loading Error")
            label.setAlignment(Qt.AlignCenter)
            self.setCentralWidget(label)

    def setup_window(self):
        """윈도우 설정"""
        self.setWindowTitle("Autopilot Control Panel")
        self.setFixedSize(650, 320)  # 적당한 크기로 조정

        # 다크 테마 스타일 적용
        self.setStyleSheet(
            """
            QMainWindow {
                background-color: #2b2b2b;
                color: white;
            }
        """
        )


def main():
    """UI 애플리케이션 실행"""
    app = QApplication(sys.argv)

    # 전체 애플리케이션 폰트 설정
    font = QFont("Arial", 9)
    app.setFont(font)

    window = AutopilotSimulatorUI()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
