# Autopilot Simulator UI – fixed Dockerfile
FROM python:3.12-slim

ENV DEBIAN_FRONTEND=noninteractive

# 1) 필수 라이브러리 설치 (PyQt5는 한 패키지면 충분)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        python3-pyqt5 \
        libx11-6 libxext6 libxrender1 libxtst6 libxi6 \
        libxrandr2 libxss1 libgconf-2-4 libasound2 \
        libpangocairo-1.0-0 libatk1.0-0 libcairo-gobject2 \
        libgtk-3-0 libgdk-pixbuf2.0-0 fonts-liberation \
        xvfb net-tools iputils-ping && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# 2) 작업 디렉토리 설정
WORKDIR /app

# 3) 파이썬 의존성 설치
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 4) 애플리케이션 복사
COPY . .

# 5) 환경 변수
ENV QT_X11_NO_MITSHM=1 \
    DISPLAY=:99 \
    REDIS_HOST=localhost \
    REDIS_PORT=6379 \
    REDIS_PASSWORD="" \
    REDIS_ENABLE_SSL=false

# 6) 헬스체크 (예시 그대로 유지)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.path.append('/app'); from redis_client import redis_manager; exit(0 if redis_manager.is_connected else 1)" || exit 1

# 7) headless 실행 ─ 가상 X 서버 → UI 실행
CMD ["python", "ui_main.py"]
