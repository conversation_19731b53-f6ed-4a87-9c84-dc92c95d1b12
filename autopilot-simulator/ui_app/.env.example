# Autopilot Simulator UI Environment Variables
# Copy this file to .env and modify the values as needed

# ===== Redis Configuration =====
# Redis server hostname or IP address
REDIS_HOST=localhost

# Redis server port
REDIS_PORT=6379

# Redis password (leave empty if no password)
REDIS_PASSWORD=

# Enable Redis SSL connection (true/false)
REDIS_ENABLE_SSL=false

# ===== GUI Configuration =====
# X11 Display for GUI applications (Linux/macOS)
DISPLAY=:0

# Qt X11 shared memory setting (for Docker environments)
QT_X11_NO_MITSHM=1

# ===== Application Configuration =====
# Python path (automatically set in Docker)
# PYTHONPATH=/app

# ===== Development Settings =====
# Enable debug logging (true/false)
# DEBUG=false

# Log level (DEBUG, INFO, WARNING, ERROR)
# LOG_LEVEL=INFO
