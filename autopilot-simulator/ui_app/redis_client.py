# redis_client.py
import os
from utils.redis_manager import RedisManager

REDIS = {
    "host": os.getenv("REDIS_HOST", "localhost"),
    "port": int(os.getenv("REDIS_PORT", "6379")),
    "password": os.getenv("REDIS_PASSWORD", ""),
    "ssl": os.getenv("REDIS_ENABLE_SSL", "false").lower() in ("true", "1", "yes"),
    "ssl_ca_certs": None,
    "ssl_certfile": None,
    "ssl_keyfile": None,
    "options": {"auto_conn": True, "auto_utc_set": True},
}

redis_manager = RedisManager(**REDIS)
