"""
Autopilot Control Panel Widget
실제 해양 autopilot 컨트롤 패널을 모방한 UI
"""

from PyQt5.QtWidgets import (
    QWidget,
    QHBoxLayout,
    QVBoxLayout,
    QGridLayout,
    QPushButton,
    QLabel,
    QFrame,
    QComboBox,
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette
import sys
import os

# Redis 클라이언트 import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from redis_client import redis_manager

    REDIS_AVAILABLE = True
except ImportError:
    redis_manager = None
    REDIS_AVAILABLE = False

from .display_widget import DisplayWidget
from .heading_slider import HeadingSlider
from .rudder_slider import RudderSlider
from .steering_mode_switch import SteeringModeSwitch
from .maker_selector import MakerSelector


class AutopilotPanel(QWidget):
    """메인 autopilot 컨트롤 패널"""

    # 시그널 정의 (나중에 Redis 통신에 사용)
    heading_changed = pyqtSignal(float)
    rudder_changed = pyqtSignal(float)
    tc_hc_pressed = pyqtSignal()
    steering_mode_changed = pyqtSignal(str)
    maker_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()

        # 사용자 입력 상태 초기화
        self.user_input = {
            "steering_mode": "M",  # 기본값: HAND
            "heading_command": 0.0,
            "rudder_command": 0.0,
            "maker": "TKM",  # 기본값: Tokyokeiki
        }

        self.setup_ui()
        self.apply_styles()
        self.connect_signals()
        self.send_initial_user_input()
        self.initialize_set_hdg()

    def setup_ui(self):
        """UI 레이아웃 구성"""
        main_layout = QHBoxLayout()
        main_layout.setSpacing(8)  # 전체 간격 줄임
        main_layout.setContentsMargins(12, 12, 12, 12)  # 여백 줄임

        # 왼쪽 버튼 그룹
        left_buttons = self.create_left_buttons()
        main_layout.addWidget(left_buttons)

        # 중앙 디스플레이
        self.display_widget = DisplayWidget()
        # display_widget에 autopilot_panel 참조 설정
        self.display_widget.autopilot_panel = self
        main_layout.addWidget(self.display_widget)

        # 오른쪽 컨트롤 그룹
        right_controls = self.create_right_controls()
        main_layout.addWidget(right_controls)

        self.setLayout(main_layout)

    def create_left_buttons(self):
        """왼쪽 버튼 그룹 생성 - TC/HC 통합 버튼 + Maker 선택"""
        widget = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(5)  # 간격 더 줄임
        layout.setAlignment(Qt.AlignTop)
        layout.setContentsMargins(5, 5, 5, 5)  # 여백 더 줄임

        # TC/HC 통합 버튼 (더 컴팩트하게)
        self.tc_hc_btn = self.create_tc_hc_button()
        layout.addWidget(self.tc_hc_btn)

        # Maker 선택 (간격 줄임)
        self.maker_selector = MakerSelector()
        layout.addWidget(self.maker_selector)

        # 공간 추가
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_tc_hc_button(self):
        """TC/HC 통합 버튼 생성 (컴팩트)"""
        btn = QPushButton()
        btn.setFixedSize(65, 55)  # 더 컴팩트하게

        # 기본 스타일 저장 (나중에 상태 변경용)
        self.tc_hc_btn_default_style = """
            QPushButton {
                background-color: #4B4B4B;
                color: white;
                border: 2px solid #666;
                border-radius: 5px;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
            }
            QPushButton:pressed {
                background-color: #666;
                border: 2px solid #888;
            }
            QPushButton:hover {
                border: 2px solid #999;
            }
        """

        # 비활성 스타일 저장
        self.tc_hc_btn_disabled_style = """
            QPushButton {
                background-color: #404040;
                color: gray;
                border: 2px solid #555;
                border-radius: 5px;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
            }
            QPushButton:disabled {
                background-color: #404040;
                color: gray;
                border: 2px solid #555;
            }
        """

        btn.setStyleSheet(self.tc_hc_btn_default_style)

        # TC/HC 텍스트를 위아래로 배치
        btn.setText("TC\n━━\nHC")

        return btn

    def create_right_controls(self):
        """오른쪽 컨트롤 그룹 생성 - 가로 배치"""
        widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setAlignment(Qt.AlignTop)

        # 슬라이더들을 가로로 배치
        sliders_layout = QHBoxLayout()
        sliders_layout.setSpacing(20)

        # Heading Command 그룹
        heading_group = QVBoxLayout()
        heading_group.setSpacing(5)

        heading_label = QLabel("Heading Command")
        heading_label.setAlignment(Qt.AlignCenter)
        heading_label.setStyleSheet(
            """
            QLabel {
                color: #CCCCCC;
                font-size: 10px;
                font-weight: bold;
            }
        """
        )

        self.heading_slider = HeadingSlider()

        heading_group.addWidget(heading_label)
        heading_group.addWidget(self.heading_slider)

        # Rudder Command 그룹
        rudder_group = QVBoxLayout()
        rudder_group.setSpacing(5)

        rudder_label = QLabel("Rudder Command")
        rudder_label.setAlignment(Qt.AlignCenter)
        rudder_label.setStyleSheet(
            """
            QLabel {
                color: #CCCCCC;
                font-size: 10px;
                font-weight: bold;
            }
        """
        )

        self.rudder_slider = RudderSlider()

        rudder_group.addWidget(rudder_label)
        rudder_group.addWidget(self.rudder_slider)

        # 가로 레이아웃에 추가
        sliders_layout.addLayout(heading_group)
        sliders_layout.addLayout(rudder_group)

        main_layout.addLayout(sliders_layout)

        # 스티어링 모드를 우측 하단에 추가
        self.steering_mode_switch = SteeringModeSwitch()
        main_layout.addWidget(self.steering_mode_switch)

        widget.setLayout(main_layout)
        return widget

    def create_button(self, text, color="#4B4B4B"):
        """표준 버튼 생성"""
        btn = QPushButton(text)
        btn.setFixedSize(60, 35)
        btn.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: 2px solid #666;
                border-radius: 5px;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:pressed {{
                background-color: #666;
            }}
        """
        )
        return btn

    def apply_styles(self):
        """전체 패널 스타일 적용"""
        self.setStyleSheet(
            """
            AutopilotPanel {
                background-color: #2b2b2b;
                border: 3px solid #555;
                border-radius: 10px;
            }
        """
        )

    def connect_signals(self):
        """시그널 연결 (나중에 Redis 통신 구현 시 사용)"""
        # 버튼 클릭 이벤트 연결
        self.tc_hc_btn.clicked.connect(self.tc_hc_pressed.emit)

        # 슬라이더 이벤트 연결
        self.heading_slider.value_changed.connect(self.heading_changed.emit)
        self.rudder_slider.value_changed.connect(self.rudder_changed.emit)

        # 설정 변경 이벤트 연결
        self.steering_mode_switch.mode_changed.connect(self.steering_mode_changed.emit)
        self.maker_selector.maker_changed.connect(self.maker_changed.emit)

        # 임시 디버그 출력
        self.heading_changed.connect(lambda x: print(f"Heading changed: {x:.1f}°"))
        self.rudder_changed.connect(lambda x: print(f"Rudder changed: {x:.1f}°"))
        self.tc_hc_pressed.connect(lambda: print("TC/HC pressed"))
        self.steering_mode_changed.connect(lambda x: print(f"Steering mode: {x}"))
        self.maker_changed.connect(lambda x: print(f"Maker: {x}"))

        # LCD 내부 RUDDER 게이지는 이제 Redis autopilot:display에서 업데이트됨
        # self.rudder_changed.connect(self.display_widget.set_rudder_angle)  # 비활성화

        # 사용자 입력 이벤트 연결
        self.heading_changed.connect(self.on_heading_command_changed)
        self.rudder_changed.connect(self.on_rudder_command_changed)
        self.tc_hc_pressed.connect(self.on_tc_hc_pressed)
        self.steering_mode_changed.connect(self.on_steering_mode_changed)
        self.maker_changed.connect(self.on_maker_changed)

        # DisplayWidget 상태 변경 시 좌측 상단 TC/HC 버튼 상태 업데이트
        self.display_widget.display_state_changed.connect(
            self.update_left_tc_hc_button_state
        )

        # DisplayWidget 상태 변경 시 슬라이더 활성화 상태 업데이트
        self.display_widget.display_state_changed.connect(
            self.update_heading_slider_state
        )

        # DisplayWidget에서 초기 heading 값 수신
        self.display_widget.initial_heading_received.connect(
            self.on_initial_heading_received
        )

        # DisplayWidget에서 H 모드 set_heading 변경 수신
        self.display_widget.h_mode_set_heading_changed.connect(
            self.on_h_mode_set_heading_changed
        )

        # DisplayWidget에서 M->S 모드 변경 수신
        self.display_widget.m_to_s_mode_changed.connect(self.on_m_to_s_mode_changed)

        # DisplayWidget에서 S->H 모드 변경 수신
        self.display_widget.s_to_h_mode_changed.connect(self.on_s_to_h_mode_changed)

        # DisplayWidget에서 H->S 모드 변경 수신
        self.display_widget.h_to_s_mode_changed.connect(self.on_h_to_s_mode_changed)

    def send_initial_user_input(self):
        """초기 사용자 입력 값을 Redis에 전송"""
        if REDIS_AVAILABLE and redis_manager:
            try:
                redis_manager.SetRedisMsg("autopilot:user_input", self.user_input)
                print(f"✅ Initial user input sent: {self.user_input}")
            except Exception as e:
                print(f"❌ Failed to send initial user input: {e}")

    def initialize_set_hdg(self):
        """초기 SET HDG 값 설정"""
        # 초기 steering_mode가 M이므로 현재 heading_command 값으로 SET HDG 설정
        initial_heading_command = self.user_input.get("heading_command", 0.0)
        self.display_widget.set_target_heading(initial_heading_command)

    def send_user_input(self):
        """현재 사용자 입력 상태를 Redis에 전송"""
        if REDIS_AVAILABLE and redis_manager:
            try:
                redis_manager.SetRedisMsg("autopilot:user_input", self.user_input)
                print(f"📤 User input sent: {self.user_input}")
            except Exception as e:
                print(f"❌ Failed to send user input: {e}")

    def on_heading_command_changed(self, heading):
        """Heading Command 변경 시 처리"""
        # 소숫점 첫째자리까지만 반올림
        heading_rounded = round(heading, 1)
        self.user_input["heading_command"] = heading_rounded

        # SET HDG에 반영 (steering_mode가 "H"가 아닐 때만)
        current_steering_mode = getattr(
            self.display_widget, "current_steering_mode", "M"
        )
        if current_steering_mode != "H":
            self.display_widget.set_target_heading(heading_rounded)

        self.send_user_input()

    def on_rudder_command_changed(self, rudder):
        """Rudder Command 변경 시 처리"""
        self.user_input["rudder_command"] = rudder
        self.send_user_input()

    def on_tc_hc_pressed(self):
        """좌측 상단 TC/HC 버튼 눌림 시 처리"""
        # 현재 display 상태 확인
        current_ext_ready = getattr(self.display_widget, "current_ext_ready", False)
        current_display_steering_mode = getattr(
            self.display_widget, "current_steering_mode", "M"
        )

        # H 모드 전환 조건 확인
        can_switch_to_h = current_ext_ready and current_display_steering_mode != "H"

        if can_switch_to_h and self.user_input["steering_mode"] == "S":
            # S → H 전환
            self.user_input["steering_mode"] = "H"
            print("🔄 Left TC/HC: S → H 모드 전환")

            # S->H 변경 시 현재 display set_heading 값으로 heading_command 즉시 업데이트
            current_set_heading = getattr(self.display_widget, "redis_data", {}).get(
                "set_heading"
            )
            if current_set_heading is not None:
                try:
                    set_heading_val = float(current_set_heading)
                    if 0 <= set_heading_val <= 360:
                        # 소숫점 첫째자리까지만 반올림
                        set_heading_rounded = round(set_heading_val, 1)
                        self.user_input["heading_command"] = set_heading_rounded
                        self.heading_slider.set_value(set_heading_rounded)
                        print(
                            f"✅ S->H mode switch: heading_command set to {set_heading_rounded:.1f}°"
                        )
                except (ValueError, TypeError):
                    print("❌ Failed to get current set_heading for S->H mode switch")
        # else:
        #     # 기존 동작: M → S 또는 기타 → S
        #     self.user_input["steering_mode"] = "S"
        #     print("🔄 Left TC/HC: → S 모드 전환")

        self.send_user_input()

        # 버튼 상태 업데이트
        self.update_left_tc_hc_button_state()

    def update_left_tc_hc_button_state(self):
        """좌측 상단 TC/HC 버튼 상태 업데이트"""
        if hasattr(self, "tc_hc_btn"):
            # 현재 display 상태 확인
            current_ext_ready = getattr(self.display_widget, "current_ext_ready", False)
            current_display_steering_mode = getattr(
                self.display_widget, "current_steering_mode", "M"
            )

            # 활성 조건: ext_ready=true AND steering_mode≠H
            can_activate = current_ext_ready and (current_display_steering_mode != "H")

            if can_activate:
                # 활성 상태 (기본 색상)
                self.tc_hc_btn.setEnabled(True)
                self.tc_hc_btn.setStyleSheet(self.tc_hc_btn_default_style)
            else:
                # 비활성 상태 (회색)
                self.tc_hc_btn.setEnabled(False)
                self.tc_hc_btn.setStyleSheet(self.tc_hc_btn_disabled_style)

    def on_steering_mode_changed(self, mode):
        """우측 하단 Mode 영역 버튼 처리"""
        old_mode = self.user_input.get("steering_mode", "M")

        if mode == "M":  # HAND 버튼
            self.user_input["steering_mode"] = "M"
            print("🔄 Mode HAND: steering_mode → M")
        elif mode == "S":  # TC/HC 버튼
            self.user_input["steering_mode"] = "S"
            print("🔄 Mode TC/HC: steering_mode → S")

            # M->S 변경 시 현재 display heading 값으로 heading_command 즉시 업데이트
            if old_mode == "M":
                current_heading = getattr(self.display_widget, "redis_data", {}).get(
                    "heading"
                )
                if current_heading is not None:
                    try:
                        heading_val = float(current_heading)
                        if 0 <= heading_val <= 360:
                            # 소숫점 첫째자리까지만 반올림
                            heading_rounded = round(heading_val, 1)
                            self.user_input["heading_command"] = heading_rounded
                            self.heading_slider.set_value(heading_rounded)
                            self.display_widget.set_target_heading(heading_rounded)
                            print(
                                f"✅ M->S mode switch: heading_command set to {heading_rounded:.1f}°"
                            )
                    except (ValueError, TypeError):
                        print("❌ Failed to get current heading for M->S mode switch")

        self.send_user_input()

    def on_maker_changed(self, maker):
        """Maker 변경 시 처리"""
        # Tokyokeiki -> TKM, Yokogawa -> YDK
        if maker == "Tokyokeiki":
            self.user_input["maker"] = "TKM"
        elif maker == "Yokogawa":
            self.user_input["maker"] = "YDK"
        self.send_user_input()

    def on_initial_heading_received(self, heading):
        """초기 heading 값 수신 시 처리"""
        print(f"📥 Initial heading received: {heading:.1f}°")

        # 소숫점 첫째자리까지만 반올림
        heading_rounded = round(heading, 1)

        # user_input의 heading_command 업데이트
        self.user_input["heading_command"] = heading_rounded

        # 슬라이더 UI 업데이트
        self.heading_slider.set_value(heading_rounded)

        # SET HDG 업데이트 (현재 steering_mode가 H가 아닐 때만)
        current_steering_mode = getattr(
            self.display_widget, "current_steering_mode", "M"
        )
        if current_steering_mode != "H":
            self.display_widget.set_target_heading(heading_rounded)

        # Redis에 업데이트된 user_input 전송
        self.send_user_input()

        print(f"✅ Initial heading_command set to: {heading_rounded:.1f}°")

    def on_h_mode_set_heading_changed(self, set_heading):
        """H 모드에서 set_heading 값 변경 시 처리"""
        print(f"📥 H mode set_heading changed: {set_heading:.1f}°")

        # 소숫점 첫째자리까지만 반올림
        set_heading_rounded = round(set_heading, 1)

        # 현재 steering_mode가 H인지 확인
        current_steering_mode = getattr(
            self.display_widget, "current_steering_mode", "M"
        )

        if current_steering_mode == "H":
            # user_input의 heading_command를 set_heading 값으로 강제 업데이트
            self.user_input["heading_command"] = set_heading_rounded

            # 슬라이더 UI 업데이트
            self.heading_slider.set_value(set_heading_rounded)

            # Redis에 업데이트된 user_input 전송
            self.send_user_input()

            print(f"✅ H mode heading_command updated to: {set_heading_rounded:.1f}°")

    def update_heading_slider_state(self):
        """현재 steering_mode에 따라 heading 슬라이더 활성화 상태 업데이트"""
        current_steering_mode = getattr(
            self.display_widget, "current_steering_mode", "M"
        )

        # H 모드에서는 슬라이더 비활성화, 다른 모드에서는 활성화
        slider_enabled = current_steering_mode != "H"
        self.heading_slider.set_enabled(slider_enabled)

        if slider_enabled:
            print(f"🔓 Heading slider enabled (mode: {current_steering_mode})")
        else:
            print(f"🔒 Heading slider disabled (mode: {current_steering_mode})")

    def on_m_to_s_mode_changed(self, heading):
        """M->S 모드 변경 시 처리"""
        print(f"📥 M->S mode change: current heading {heading:.1f}°")

        # 소숫점 첫째자리까지만 반올림
        heading_rounded = round(heading, 1)

        # user_input의 heading_command를 현재 heading 값으로 강제 업데이트
        self.user_input["heading_command"] = heading_rounded

        # 슬라이더 UI 업데이트
        self.heading_slider.set_value(heading_rounded)

        # SET HDG 업데이트
        self.display_widget.set_target_heading(heading_rounded)

        # Redis에 업데이트된 user_input 전송
        self.send_user_input()

        print(f"✅ M->S mode: heading_command updated to {heading_rounded:.1f}°")

    def on_s_to_h_mode_changed(self, set_heading):
        """S->H 모드 변경 시 처리 (autopilot:display에서 감지)"""
        print(f"📥 S->H mode change (display): set_heading {set_heading:.1f}°")

        # 소숫점 첫째자리까지만 반올림
        set_heading_rounded = round(set_heading, 1)

        # user_input의 heading_command를 현재 set_heading 값으로 강제 업데이트
        self.user_input["heading_command"] = set_heading_rounded

        # 슬라이더 UI 업데이트
        self.heading_slider.set_value(set_heading_rounded)

        # Redis에 업데이트된 user_input 전송
        self.send_user_input()

        print(
            f"✅ S->H mode (display): heading_command updated to {set_heading_rounded:.1f}°"
        )

    def on_h_to_s_mode_changed(self):
        """H->S 모드 변경 시 처리 (autopilot:display에서 감지)"""
        print("📥 H->S mode change (display): steering_mode changed to S")

        # user_input의 steering_mode를 S로 변경
        self.user_input["steering_mode"] = "S"

        # UI의 Mode 스위치도 S로 업데이트 (시그널 발생 방지)
        if hasattr(self, "steering_mode_switch"):
            # 시그널 연결을 일시적으로 해제하여 무한 루프 방지
            self.steering_mode_switch.mode_changed.disconnect()
            self.steering_mode_switch.set_mode("S")
            # 시그널 연결 복원
            self.steering_mode_switch.mode_changed.connect(
                self.on_steering_mode_changed
            )
            print("🔄 UI Mode switch updated to S")

        # Redis에 업데이트된 user_input 전송
        self.send_user_input()

        print("✅ H->S mode (display): steering_mode updated to S")
