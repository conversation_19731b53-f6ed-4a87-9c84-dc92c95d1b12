"""
Rudder Gauge Widget
실제 러더각을 표시하는 좌우 바 형태의 게이지
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import <PERSON>Painter, QPen, QBrush, QColor, QFont


class RudderGauge(QWidget):
    """실제 러더각 표시 게이지 (좌우 바 형태)"""

    def __init__(self):
        super().__init__()
        self.actual_rudder_angle = 0.0  # 실제 러더각 (-35 ~ +35)
        self.setup_ui()

    def setup_ui(self):
        """UI 구성"""
        layout = QVBoxLayout()
        layout.setSpacing(5)

        # RUDDER 라벨
        rudder_label = QLabel("RUDDER")
        rudder_label.setAlignment(Qt.AlignCenter)
        rudder_label.setStyleSheet(
            """
            QLabel {
                color: #CCCCCC;
                font-size: 12px;
                font-weight: bold;
            }
        """
        )
        layout.addWidget(rudder_label)

        # 게이지 바
        self.gauge_bar = RudderGaugeBar()
        layout.addWidget(self.gauge_bar)

        # 실제 러더각 숫자 표시
        self.angle_label = QLabel("0.0°")
        self.angle_label.setAlignment(Qt.AlignCenter)
        self.angle_label.setStyleSheet(
            """
            QLabel {
                color: #FFFF00;
                font-size: 14px;
                font-weight: bold;
                background-color: #000;
                border: 1px solid #333;
                border-radius: 3px;
                padding: 3px;
                min-width: 60px;
            }
        """
        )
        layout.addWidget(self.angle_label)

        self.setLayout(layout)
        self.setFixedHeight(80)

    def set_rudder_angle(self, angle):
        """실제 러더각 설정"""
        # -35 ~ +35 범위로 제한
        if angle > 35.0:
            angle = 35.0
        elif angle < -35.0:
            angle = -35.0

        self.actual_rudder_angle = angle
        self.gauge_bar.set_angle(angle)

        # 라벨 업데이트
        if angle > 0:
            self.angle_label.setText(f"+{angle:.1f}°")
        else:
            self.angle_label.setText(f"{angle:.1f}°")


class RudderGaugeBar(QWidget):
    """러더 게이지 바 (좌우 표시)"""

    def __init__(self):
        super().__init__()
        self.angle = 0.0
        self.setFixedHeight(30)
        self.setMinimumWidth(200)

    def set_angle(self, angle):
        """각도 설정 및 업데이트"""
        self.angle = angle
        self.update()

    def paintEvent(self, event):
        """게이지 바 그리기"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        width = self.width()
        height = self.height()
        center_x = width // 2

        # 배경 바 (전체 범위)
        painter.setPen(QPen(QColor(100, 100, 100), 2))
        painter.setBrush(QBrush(QColor(40, 40, 40)))
        bar_rect = (10, height // 2 - 5, width - 20, 10)
        painter.drawRoundedRect(*bar_rect, 5, 5)

        # 중앙선 (0도 위치)
        painter.setPen(QPen(QColor(255, 255, 0), 2))
        painter.drawLine(center_x, 5, center_x, height - 5)

        # 최대/최소 표시선
        left_max = 10 + 20  # -35도 위치
        right_max = width - 10 - 20  # +35도 위치

        painter.setPen(QPen(QColor(255, 0, 0), 2))
        painter.drawLine(left_max, 5, left_max, height - 5)
        painter.drawLine(right_max, 5, right_max, height - 5)

        # 현재 러더각 표시 (항상 표시, 0도일 때는 중앙에)
        # 각도를 픽셀 위치로 변환
        angle_ratio = self.angle / 35.0  # -1 ~ +1
        indicator_x = center_x + angle_ratio * (center_x - 30)

        # 러더 인디케이터 (노란색 삼각형)
        painter.setPen(QPen(QColor(255, 255, 0), 3))
        painter.setBrush(QBrush(QColor(255, 255, 0)))

        # 삼각형 인디케이터 (위쪽 방향)
        triangle_points = [
            (int(indicator_x), 2),
            (int(indicator_x - 6), 12),
            (int(indicator_x + 6), 12),
        ]

        for i in range(len(triangle_points)):
            start = triangle_points[i]
            end = triangle_points[(i + 1) % len(triangle_points)]
            painter.drawLine(start[0], start[1], end[0], end[1])

        # 삼각형 채우기
        from PyQt5.QtCore import QPoint

        points = [
            QPoint(triangle_points[0][0], triangle_points[0][1]),
            QPoint(triangle_points[1][0], triangle_points[1][1]),
            QPoint(triangle_points[2][0], triangle_points[2][1]),
        ]
        painter.drawPolygon(points)

        # 눈금 표시
        painter.setPen(QPen(QColor(150, 150, 150), 1))
        painter.setFont(QFont("Arial", 8))

        # -35, -20, 0, +20, +35 눈금
        for angle in [-35, -20, 0, 20, 35]:
            angle_ratio = angle / 35.0
            tick_x = center_x + angle_ratio * (center_x - 30)

            # 눈금선
            painter.drawLine(int(tick_x), height - 15, int(tick_x), height - 5)

            # 라벨
            if angle == 0:
                label = "0"
            elif angle > 0:
                label = f"+{angle}"
            else:
                label = str(angle)

            # 텍스트 위치 조정
            text_width = painter.fontMetrics().width(label)
            painter.drawText(int(tick_x - text_width // 2), height - 18, label)
