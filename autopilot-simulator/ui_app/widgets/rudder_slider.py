"""
Rudder Slider Widget
마우스 드래그로 rudder command를 조절하는 수직 슬라이더 (-35 ~ +35)
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt, pyqtSignal, QPoint
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont
import math


class RudderSlider(QWidget):
    """수직 rudder 슬라이더"""

    # 값 변경 시그널
    value_changed = pyqtSignal(float)

    def __init__(self):
        super().__init__()
        self.value = 0.0  # -35 to +35 degrees
        self.min_value = -35.0
        self.max_value = 35.0
        self.last_mouse_pos = QPoint()
        self.dragging = False
        self.actual_rudder_angle = 0.0  # 실제 러더각 (현재는 0)

        self.setup_ui()

    def setup_ui(self):
        """UI 구성"""
        layout = QVBoxLayout()
        layout.setSpacing(5)

        # 슬라이더 영역
        self.slider_area = RudderSliderArea()
        self.slider_area.value_changed.connect(self.on_value_changed)
        layout.addWidget(self.slider_area)

        # 현재 값 표시 라벨 (LCD 스타일)
        self.value_label = QLabel("0.0°")
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet(
            """
            QLabel {
                color: #00FF00;
                font-size: 16px;
                font-weight: bold;
                background-color: #0C0C0C;
                border: 2px solid #333;
                border-radius: 5px;
                padding: 8px;
                min-width: 70px;
                min-height: 25px;
            }
        """
        )
        layout.addWidget(self.value_label)

        self.setLayout(layout)
        self.update_display()

    def on_value_changed(self, value):
        """값 변경 처리"""
        self.value = value
        self.update_display()
        self.value_changed.emit(value)

    def update_display(self):
        """디스플레이 업데이트"""
        # Command 값 표시
        if self.value > 0:
            self.value_label.setText(f"+{self.value:.1f}°")
        else:
            self.value_label.setText(f"{self.value:.1f}°")

    def set_value(self, value):
        """외부에서 값 설정"""
        # -35 ~ +35 범위로 제한
        if value > self.max_value:
            value = self.max_value
        elif value < self.min_value:
            value = self.min_value

        self.value = value
        self.slider_area.set_value(value)
        self.update_display()


class RudderSliderArea(QWidget):
    """실제 rudder 슬라이더 드래그 영역"""

    value_changed = pyqtSignal(float)

    def __init__(self):
        super().__init__()
        self.value = 0.0  # -35 to +35
        self.min_value = -35.0
        self.max_value = 35.0
        self.dragging = False
        self.last_mouse_y = 0

        self.setFixedSize(50, 120)
        self.setMouseTracking(True)

    def mousePressEvent(self, event):
        """마우스 클릭 시작"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.last_mouse_y = event.y()

    def mouseMoveEvent(self, event):
        """마우스 드래그"""
        if self.dragging:
            # 마우스 Y 이동량을 각도 변화로 변환
            delta_y = self.last_mouse_y - event.y()  # 위로 드래그하면 양수

            # 감도 조절 (픽셀당 각도)
            sensitivity = 0.5  # 1픽셀 = 0.5도
            angle_change = delta_y * sensitivity

            new_value = self.value + angle_change

            # -35 ~ +35 범위로 제한
            if new_value > self.max_value:
                new_value = self.max_value
            elif new_value < self.min_value:
                new_value = self.min_value

            if abs(new_value - self.value) > 0.1:  # 최소 변화량
                self.set_value(new_value)

            self.last_mouse_y = event.y()

    def mouseReleaseEvent(self, event):
        """마우스 클릭 종료"""
        if event.button() == Qt.LeftButton:
            self.dragging = False

    def wheelEvent(self, event):
        """마우스 휠 이벤트"""
        delta = event.angleDelta().y() / 120.0  # 휠 한 칸당 15도
        angle_change = delta * 2.0

        new_value = self.value + angle_change

        # -35 ~ +35 범위로 제한
        if new_value > self.max_value:
            new_value = self.max_value
        elif new_value < self.min_value:
            new_value = self.min_value

        self.set_value(new_value)

    def set_value(self, value):
        """값 설정"""
        # -35 ~ +35 범위로 제한
        if value > self.max_value:
            value = self.max_value
        elif value < self.min_value:
            value = self.min_value

        if abs(self.value - value) > 0.1:  # 최소 변화량
            self.value = value
            self.value_changed.emit(value)
            self.update()

    def paintEvent(self, event):
        """슬라이더 그리기"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        width = self.width()
        height = self.height()

        # 배경 (슬라이더 트랙)
        painter.setPen(QPen(QColor(100, 100, 100), 2))
        painter.setBrush(QBrush(QColor(40, 40, 40)))
        track_rect = (width // 2 - 3, 10, 6, height - 20)
        painter.drawRoundedRect(*track_rect, 3, 3)

        # 중앙선 (0도 위치)
        center_y = height // 2
        painter.setPen(QPen(QColor(255, 255, 0), 1))
        painter.drawLine(width // 2 - 8, center_y, width // 2 + 8, center_y)

        # 슬라이더 핸들 위치 계산
        # value -35 ~ +35를 슬라이더 위치로 변환 (0이 중앙)
        value_ratio = (self.value - self.min_value) / (self.max_value - self.min_value)
        handle_y = 10 + (height - 40) * (1.0 - value_ratio)  # 위쪽이 +35

        # 슬라이더 핸들
        painter.setPen(QPen(QColor(200, 200, 200), 2))
        painter.setBrush(QBrush(QColor(150, 150, 150)))
        handle_rect = (width // 2 - 8, int(handle_y) - 5, 16, 10)
        painter.drawRoundedRect(*handle_rect, 2, 2)

        # 드래그 중일 때 하이라이트
        if self.dragging:
            painter.setPen(QPen(QColor(255, 255, 0), 2))
            painter.setBrush(QBrush(QColor(255, 255, 0, 100)))
            painter.drawRoundedRect(*handle_rect, 2, 2)

        # 눈금 표시 (주요 각도)
        painter.setPen(QPen(QColor(150, 150, 150), 1))
        painter.setFont(QFont("Arial", 7))

        for angle in [-35, -20, 0, 20, 35]:
            value_ratio = (angle - self.min_value) / (self.max_value - self.min_value)
            tick_y = 10 + (height - 40) * (1.0 - value_ratio)

            # 눈금선 (최대/최소값은 더 굵게)
            if angle == -35 or angle == 35:
                painter.setPen(QPen(QColor(255, 0, 0), 2))  # 빨간색으로 강조
            else:
                painter.setPen(QPen(QColor(150, 150, 150), 1))
            painter.drawLine(width - 15, int(tick_y), width - 5, int(tick_y))

            # 각도 라벨
            if angle == -35:
                label = "-35°"
                painter.setPen(QPen(QColor(255, 0, 0), 1))
            elif angle == 35:
                label = "+35°"
                painter.setPen(QPen(QColor(255, 0, 0), 1))
            elif angle == 0:
                label = "0°"
                painter.setPen(QPen(QColor(255, 255, 0), 1))
            elif angle > 0:
                label = f"+{angle}°"
                painter.setPen(QPen(QColor(150, 150, 150), 1))
            else:
                label = f"{angle}°"
                painter.setPen(QPen(QColor(150, 150, 150), 1))
            painter.drawText(width - 35, int(tick_y) + 3, label)
