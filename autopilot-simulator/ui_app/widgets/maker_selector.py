"""
Maker Selector Widget
TKM, YDK 메이커를 선택하는 개선된 위젯
"""

from PyQt5.QtWidgets import QWidget, QHBoxLayout, QPushButton, QLabel, QVBoxLayout
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class MakerSelector(QWidget):
    """메이커 선택 위젯"""

    # 메이커 변경 시그널
    maker_changed = pyqtSignal(str)  # TKM, YDK

    def __init__(self):
        super().__init__()
        self.current_maker = "Tokyokeiki"  # 기본값 (내부적으로 전체 이름)
        self.setup_ui()
        self.update_buttons()

    def setup_ui(self):
        """UI 구성"""
        layout = QVBoxLayout()
        layout.setSpacing(2)  # 간격 더 줄임
        layout.setContentsMargins(0, 0, 0, 0)  # 여백 제거

        # 라벨
        label = QLabel("MAKER")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet(
            """
            QLabel {
                color: #CCCCCC;
                font-size: 10px;
                font-weight: bold;
                margin-bottom: 1px;
            }
        """
        )
        layout.addWidget(label)

        # 버튼 그룹 (세로 배치로 변경)
        button_layout = QVBoxLayout()
        button_layout.setSpacing(1)  # 버튼 간격 최소화

        # Tokyokeiki 버튼
        self.tkm_btn = self.create_maker_button("Tokyokeiki", "Tokyokeiki")
        self.tkm_btn.clicked.connect(lambda: self.set_maker("Tokyokeiki"))

        # Yokogawa 버튼
        self.ydk_btn = self.create_maker_button("Yokogawa", "Yokogawa")
        self.ydk_btn.clicked.connect(lambda: self.set_maker("Yokogawa"))

        button_layout.addWidget(self.tkm_btn)
        button_layout.addWidget(self.ydk_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def create_maker_button(self, maker, tooltip):
        """메이커 버튼 생성 (더 컴팩트)"""
        btn = QPushButton(maker)
        btn.setFixedSize(60, 22)  # 더 작게
        btn.setToolTip(tooltip)
        btn.setStyleSheet(
            """
            QPushButton {
                background-color: #2B2B2B;
                color: #CCCCCC;
                border: 2px solid #555;
                border-radius: 3px;
                font-weight: bold;
                font-size: 7px;
            }
            QPushButton:hover {
                border: 2px solid #777;
                background-color: #3B3B3B;
            }
            QPushButton:pressed {
                background-color: #1B1B1B;
            }
        """
        )
        return btn

    def set_maker(self, maker):
        """메이커 설정"""
        if self.current_maker != maker:
            self.current_maker = maker
            self.update_buttons()
            self.maker_changed.emit(maker)
            print(f"Maker changed to: {maker}")

    def update_buttons(self):
        """버튼 상태 업데이트"""
        # 모든 버튼을 기본 상태로
        buttons = [self.tkm_btn, self.ydk_btn]
        for btn in buttons:
            btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #2B2B2B;
                    color: #CCCCCC;
                    border: 2px solid #555;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 9px;
                }
                QPushButton:hover {
                    border: 2px solid #777;
                    background-color: #3B3B3B;
                }
                QPushButton:pressed {
                    background-color: #1B1B1B;
                }
            """
            )

        # 현재 선택된 버튼을 활성화 상태로
        active_btn = None
        if self.current_maker == "Tokyokeiki":
            active_btn = self.tkm_btn
        elif self.current_maker == "Yokogawa":
            active_btn = self.ydk_btn

        if active_btn:
            active_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #0066CC;
                    color: white;
                    border: 2px solid #0088FF;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 9px;
                }
                QPushButton:hover {
                    background-color: #0088FF;
                    border: 2px solid #00AAFF;
                }
                QPushButton:pressed {
                    background-color: #0044AA;
                }
            """
            )

    def get_maker(self):
        """현재 메이커 반환"""
        return self.current_maker

    def get_maker_full_name(self):
        """현재 메이커 전체 이름 반환"""
        names = {"TKM": "Tokyokeiki", "YDK": "Yokogawa"}
        return names.get(self.current_maker, "Unknown")
