"""
Rudder Gauge D Widget
D 위치의 실제 러더각 표시 게이지 (검은색 배경 테두리 박스)
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont


class RudderGaugeD(QWidget):
    """D 위치 실제 러더각 표시 게이지"""

    def __init__(self):
        super().__init__()
        self.actual_rudder_angle = 0.0  # 실제 러더각 (-35 ~ +35)
        self.error_state = False  # 에러 상태 플래그
        self.setup_ui()

    def setup_ui(self):
        """UI 구성"""
        layout = QVBoxLayout()
        layout.setSpacing(5)
        layout.setContentsMargins(10, 8, 10, 8)

        # RUDDER 라벨 (하얀색으로 변경)
        rudder_label = QLabel("RUDDER")
        rudder_label.setAlignment(Qt.AlignCenter)
        rudder_label.setStyleSheet(
            """
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                margin: 3px;
            }
        """
        )
        layout.addWidget(rudder_label)

        # 게이지 바
        self.gauge_bar = RudderGaugeBarD()
        layout.addWidget(self.gauge_bar)

        # 실제 러더각 숫자 표시
        self.angle_label = QLabel("0.0°")
        self.angle_label.setAlignment(Qt.AlignCenter)
        self.angle_label.setStyleSheet(
            """
            QLabel {
                color: #FFFF00;
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                margin: 5px;
                min-width: 70px;
            }
        """
        )
        layout.addWidget(self.angle_label)

        self.setLayout(layout)
        self.setFixedHeight(85)

        # LCD 내부에 맞는 스타일 (터미널 배경과 조화)
        self.setStyleSheet(
            """
            RudderGaugeD {
                background-color: transparent;
                border: none;
            }
        """
        )

    def set_rudder_angle(self, angle):
        """실제 러더각 설정"""
        # -35 ~ +35 범위로 제한
        if angle > 35.0:
            angle = 35.0
        elif angle < -35.0:
            angle = -35.0

        self.actual_rudder_angle = angle
        self.gauge_bar.set_angle(angle)

        # 라벨 업데이트
        if angle > 0:
            self.angle_label.setText(f"+{angle:.1f}°")
        else:
            self.angle_label.setText(f"{angle:.1f}°")

        # 에러 상태에 따른 색상 업데이트
        self.update_label_color()

    def set_error_state(self, error):
        """에러 상태 설정"""
        self.error_state = error
        self.gauge_bar.set_error_state(error)
        self.update_label_color()

    def update_label_color(self):
        """라벨 색상 업데이트"""
        if self.error_state:
            # 에러 상태: 빨간색
            self.angle_label.setStyleSheet(
                "color: #FF0000; font-size: 14px; font-weight: bold;"
            )
        else:
            # 정상 상태: 초록색
            self.angle_label.setStyleSheet(
                "color: #00FF00; font-size: 14px; font-weight: bold;"
            )


class RudderGaugeBarD(QWidget):
    """D 위치 러더 게이지 바"""

    def __init__(self):
        super().__init__()
        self.angle = 0.0
        self.error_state = False  # 에러 상태 플래그
        self.setFixedHeight(35)
        self.setMinimumWidth(250)

    def set_angle(self, angle):
        """각도 설정 및 업데이트"""
        self.angle = angle
        self.update()

    def set_error_state(self, error):
        """에러 상태 설정"""
        self.error_state = error
        self.update()

    def paintEvent(self, event):
        """게이지 바 그리기"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        width = self.width()
        height = self.height()
        center_x = width // 2

        # 배경 바 (전체 범위)
        painter.setPen(QPen(QColor(80, 80, 80), 2))
        painter.setBrush(QBrush(QColor(30, 30, 30)))
        bar_rect = (15, height // 2 - 6, width - 30, 12)
        painter.drawRoundedRect(*bar_rect, 6, 6)

        # 중앙선 (0도 위치)
        painter.setPen(QPen(QColor(255, 255, 0), 2))
        painter.drawLine(center_x, 8, center_x, height - 8)

        # 최대/최소 표시선 (-35°, +35°)
        left_max = 15 + 25  # -35도 위치
        right_max = width - 15 - 25  # +35도 위치

        painter.setPen(QPen(QColor(255, 0, 0), 2))
        painter.drawLine(left_max, 8, left_max, height - 8)
        painter.drawLine(right_max, 8, right_max, height - 8)

        # 현재 러더각 표시 (항상 표시)
        # 각도를 픽셀 위치로 변환
        angle_ratio = self.angle / 35.0  # -1 ~ +1
        indicator_x = center_x + angle_ratio * (center_x - 40)

        # 러더 인디케이터 색상 (에러 상태에 따라 변경)
        if self.error_state:
            indicator_color = QColor(255, 0, 0)  # 빨간색
        else:
            indicator_color = QColor(255, 255, 0)  # 노란색

        painter.setPen(QPen(indicator_color, 3))
        painter.setBrush(QBrush(indicator_color))

        # 삼각형 인디케이터 (위쪽 방향)
        from PyQt5.QtCore import QPoint

        triangle_points = [
            QPoint(int(indicator_x), 3),
            QPoint(int(indicator_x - 8), 15),
            QPoint(int(indicator_x + 8), 15),
        ]
        painter.drawPolygon(triangle_points)

        # 눈금 표시
        painter.setPen(QPen(QColor(150, 150, 150), 1))
        painter.setFont(QFont("Arial", 8))

        # -35, -20, 0, +20, +35 눈금
        for angle in [-35, -20, 0, 20, 35]:
            angle_ratio = angle / 35.0
            tick_x = center_x + angle_ratio * (center_x - 40)

            # 눈금선
            if angle == -35 or angle == 35:
                painter.setPen(QPen(QColor(255, 0, 0), 1))
            elif angle == 0:
                painter.setPen(QPen(QColor(255, 255, 0), 1))
            else:
                painter.setPen(QPen(QColor(150, 150, 150), 1))

            painter.drawLine(int(tick_x), height - 18, int(tick_x), height - 8)

            # 라벨
            if angle == 0:
                label = "0"
            elif angle > 0:
                label = f"+{angle}"
            else:
                label = str(angle)

            # 텍스트 위치 조정
            painter.setPen(QPen(QColor(200, 200, 200), 1))
            text_width = painter.fontMetrics().width(label)
            painter.drawText(int(tick_x - text_width // 2), height - 20, label)
