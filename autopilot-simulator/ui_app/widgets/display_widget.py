"""
Central Display Widget
중앙 LCD 디스플레이를 모방한 위젯
"""

from PyQt5.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QFrame,
    QPushButton,
)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot, pyqtSignal
from PyQt5.QtGui import QFont, QPainter, QPen, QColor
import math
import sys
import os

# Redis 클라이언트 import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from redis_client import redis_manager

    REDIS_AVAILABLE = True
except ImportError:
    redis_manager = None
    REDIS_AVAILABLE = False


class DisplayWidget(QWidget):
    """중앙 LCD 디스플레이"""

    # 상태 변경 시그널 추가
    display_state_changed = pyqtSignal()
    # 초기 heading 값 전달 시그널 추가
    initial_heading_received = pyqtSignal(float)
    # H 모드에서 set_heading 값 변경 시그널 추가
    h_mode_set_heading_changed = pyqtSignal(float)
    # M->S 모드 변경 시그널 추가
    m_to_s_mode_changed = pyqtSignal(float)  # 현재 heading 값과 함께 전달
    # S->H 모드 변경 시그널 추가
    s_to_h_mode_changed = pyqtSignal(float)  # 현재 set_heading 값과 함께 전달
    # H->S 모드 변경 시그널 추가
    h_to_s_mode_changed = pyqtSignal()  # H에서 S로 변경 시 발생

    def __init__(self):
        super().__init__()
        self.setMinimumSize(320, 450)  # LCD 최소 크기 설정
        self.current_heading = 0.0  # 초기값을 0으로 설정
        self.set_heading = 0.0  # 초기값을 0으로 설정
        self.show_ext_hdg = False  # EXT HDG 표시 여부
        self.show_hc_rdy = True  # E. HC RDY 표시 여부 (기본 켜짐)

        # Redis 데이터 상태
        self.redis_data = {}
        self.redis_connected = False
        self.data_invalid = False

        # Display 상태 (AutopilotPanel에서 접근용)
        self.current_ext_ready = False
        self.current_steering_mode = "M"

        # AutopilotPanel 참조 (나중에 설정됨)
        self.autopilot_panel = None

        # 초기화 상태 추적
        self.initial_heading_sent = False

        # H 모드에서 set_heading 값 추적
        self.previous_set_heading = None

        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        """디스플레이 UI 구성"""
        self.setFixedSize(320, 235)  # 높이 더 증가 (220 → 235)

        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # 상단 모드 표시
        self.create_header(layout)

        # 중앙 heading 표시
        self.create_heading_display(layout)

        # 하단 RUDDER 게이지 (컴프레서 바 위치)
        self.create_rudder_gauge(layout)

        self.setLayout(layout)
        self.apply_styles()

    def create_header(self, layout):
        """상단 헤더 (PID, EXT, HDG 등)"""
        # 고정 높이 헤더 컨테이너 생성 (레이아웃 움직임 방지)
        header_container = QWidget()
        header_container.setFixedHeight(30)  # 고정 높이 설정

        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # EXT HDG 표시 영역 (고정 크기 - G 잘림 방지)
        ext_hdg_container = QWidget()
        ext_hdg_container.setFixedSize(80, 25)  # 크기 증가 (70 → 80)
        ext_hdg_layout = QHBoxLayout(ext_hdg_container)
        ext_hdg_layout.setContentsMargins(0, 0, 0, 0)

        self.ext_hdg_label = QLabel("EXT HDG")
        self.ext_hdg_label.setStyleSheet(
            "background-color: #0066CC; color: white; padding: 2px 8px; font-weight: bold;"
        )
        self.ext_hdg_label.setVisible(self.show_ext_hdg)
        ext_hdg_layout.addWidget(self.ext_hdg_label)

        header_layout.addWidget(ext_hdg_container)
        header_layout.addStretch()

        # E. HC RDY 표시 영역 (고정 크기)
        hc_rdy_container = QWidget()
        hc_rdy_container.setFixedSize(80, 25)  # 고정 크기
        hc_rdy_layout = QHBoxLayout(hc_rdy_container)
        hc_rdy_layout.setContentsMargins(0, 0, 0, 0)

        from PyQt5.QtWidgets import QPushButton

        self.hc_rdy_btn = QPushButton("E. HC RDY")
        self.hc_rdy_btn.setFixedSize(80, 25)
        self.hc_rdy_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #0066FF;
                color: white;
                border: 1px solid #0088FF;
                border-radius: 3px;
                font-weight: bold;
                font-size: 9px;
            }
        """
        )
        self.hc_rdy_btn.setVisible(self.show_hc_rdy)
        hc_rdy_layout.addWidget(self.hc_rdy_btn)

        header_layout.addWidget(hc_rdy_container)

        layout.addWidget(header_container)

    def create_heading_display(self, layout):
        """중앙 heading 숫자 표시"""
        # 고정 크기 컨테이너로 잘림 방지
        heading_container = QWidget()
        heading_container.setFixedHeight(110)  # 높이 줄임 (120 → 110)

        heading_layout = QVBoxLayout(heading_container)
        heading_layout.setAlignment(Qt.AlignCenter)
        heading_layout.setSpacing(1)  # 간격 더 줄임 (2 → 1)

        # NO.1 HDG 라벨 (하얀색)
        no1_label = QLabel("NO.1 HDG")
        no1_label.setAlignment(Qt.AlignCenter)
        no1_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")

        # 현재 heading (큰 숫자)
        self.current_hdg_label = QLabel("*.*")  # 초기값을 *.*로 설정
        self.current_hdg_label.setAlignment(Qt.AlignCenter)
        self.current_hdg_label.setStyleSheet(
            "color: #FF0000; font-size: 32px; font-weight: bold;"  # 초기에는 빨간색으로 표시
        )

        # SET HDG 라벨 (하얀색)
        set_label = QLabel("SET HDG")
        set_label.setAlignment(Qt.AlignCenter)
        set_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")

        # 설정 heading
        self.set_hdg_label = QLabel("*.*")  # 초기값을 *.*로 설정
        self.set_hdg_label.setAlignment(Qt.AlignCenter)
        self.set_hdg_label.setStyleSheet(
            "color: #00FF00; font-size: 18px; font-weight: bold;"  # 폰트 크기 줄임 (20 → 18)
        )

        heading_layout.addWidget(no1_label)
        heading_layout.addWidget(self.current_hdg_label)
        heading_layout.addWidget(set_label)
        heading_layout.addWidget(self.set_hdg_label)

        layout.addWidget(heading_container)

    def create_rudder_gauge(self, layout):
        """하단 RUDDER 게이지 (컴프레서 바 위치)"""
        from .rudder_gauge_d import RudderGaugeD

        # 고정 크기 컨테이너로 잘림 방지
        rudder_container = QWidget()
        rudder_container.setFixedHeight(95)  # 높이 증가 (90 → 95)
        rudder_layout = QVBoxLayout(rudder_container)
        rudder_layout.setContentsMargins(0, 5, 0, 0)  # 상단 여백 추가

        self.rudder_gauge = RudderGaugeD()
        rudder_layout.addWidget(self.rudder_gauge)

        layout.addWidget(rudder_container)

    def setup_timer(self):
        """업데이트 타이머 설정"""
        # Redis 데이터 폴링 타이머 (100ms 주기)
        self.redis_timer = QTimer()
        self.redis_timer.timeout.connect(self.update_from_redis)
        self.redis_timer.start(100)

        # 기존 업데이트 타이머
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_display)
        self.timer.start(100)  # 100ms 주기

        # 깜빡이는 타이머 설정
        self.blink_timer = QTimer()
        self.blink_timer.timeout.connect(self.toggle_hc_rdy)
        self.blink_timer.start(500)  # 500ms 주기로 깜빡임
        self.hc_rdy_visible = True

    def apply_styles(self):
        """디스플레이 스타일 적용 (터미널 배경)"""
        self.setStyleSheet(
            """
            DisplayWidget {
                background-color: #0C0C0C;
                border: 3px solid #333;
                border-radius: 8px;
                padding: 8px;
            }
        """
        )

    @pyqtSlot(float)
    def set_current_heading(self, heading):
        """현재 heading 설정"""
        self.current_heading = heading
        self.current_hdg_label.setText(f"{heading:.1f}")
        self.compass_widget.set_heading(heading)

    @pyqtSlot(float)
    def set_target_heading(self, heading):
        """목표 heading 설정"""
        self.set_heading = heading
        self.set_hdg_label.setText(f"{heading:.1f}")

    def update_set_hdg_from_heading_command(self):
        """현재 heading_command 값으로 SET HDG 업데이트"""
        if self.autopilot_panel is not None:
            try:
                current_heading_command = self.autopilot_panel.user_input.get(
                    "heading_command", 0.0
                )
                self.set_heading = current_heading_command
                self.set_hdg_label.setText(f"{current_heading_command:.1f}")
            except Exception as e:
                print(f"Error updating SET HDG from heading command: {e}")
                # 에러 발생 시 기본값 사용
                self.set_hdg_label.setText("0.0")

    def update_from_redis(self):
        """Redis에서 데이터를 가져와서 UI 업데이트"""
        if not REDIS_AVAILABLE or not redis_manager:
            self.set_redis_disconnected()
            return

        try:
            # Redis 연결 상태 확인
            if not redis_manager.connect_check_status():
                self.set_redis_disconnected()
                return

            # autopilot:display 데이터 가져오기
            data = redis_manager.GetRedisMsg("autopilot:display")

            if data is None:
                self.set_no_data()
                return

            self.redis_connected = True
            self.redis_data = data
            self.update_ui_from_data(data)

        except Exception as e:
            print(f"Redis update error: {e}")
            self.set_redis_disconnected()

    def update_ui_from_data(self, data):
        """Redis 데이터를 기반으로 UI 업데이트"""
        try:
            # 이전 상태 저장
            old_ext_ready = self.current_ext_ready
            old_steering_mode = self.current_steering_mode

            # status 확인
            status = data.get("status", "invalid")
            self.data_invalid = status == "invalid"

            # steering_mode 처리
            steering_mode = data.get("steering_mode", "")
            self.update_steering_mode(steering_mode)

            # heading 처리
            heading = data.get("heading")
            self.update_heading_display(heading)

            # rudder 처리
            rudder = data.get("rudder")
            self.update_rudder_display(rudder)

            # ext_ready 처리
            ext_ready = data.get("ext_ready", False)
            self.update_ext_ready(ext_ready, steering_mode)

            # SET HDG 값 처리 (steering_mode에 따라)
            if steering_mode == "H":
                # Auto mode일 때는 autopilot:display의 set_heading 값 사용
                set_heading = data.get("set_heading")
                if set_heading is not None:
                    try:
                        set_heading_val = float(set_heading)

                        # H 모드에서 set_heading 값이 변경되었는지 확인 또는 H 모드 진입 (모든 모드에서)
                        if (self.previous_set_heading != set_heading_val) or (
                            old_steering_mode != "H" and steering_mode == "H"
                        ):
                            self.previous_set_heading = set_heading_val
                            # AutopilotPanel에 변경된 set_heading 값 전달
                            self.h_mode_set_heading_changed.emit(set_heading_val)
                            if old_steering_mode == "S" and steering_mode == "H":
                                print(
                                    f"🔄 S->H mode change: heading_command set to {set_heading_val:.1f}°"
                                )
                            elif old_steering_mode != "H":
                                print(
                                    f"🔄 H mode entered: heading_command set to {set_heading_val:.1f}°"
                                )
                            else:
                                print(
                                    f"🔄 H mode set_heading changed: {set_heading_val:.1f}°"
                                )

                        self.set_heading = set_heading_val
                        self.set_hdg_label.setText(f"{set_heading_val:.1f}")
                    except (ValueError, TypeError):
                        # 잘못된 데이터일 때 *.* 표시
                        self.set_hdg_label.setText("*.*")
                        self.previous_set_heading = None
                else:
                    # set_heading 값이 없을 때 *.* 표시
                    self.set_hdg_label.setText("*.*")
                    self.previous_set_heading = None
            elif old_steering_mode == "H" and steering_mode != "H":
                # H 모드에서 다른 모드로 변경될 때 현재 heading_command 값으로 즉시 업데이트
                self.update_set_hdg_from_heading_command()
                self.previous_set_heading = None

            # S->H 모드 변경 감지 (autopilot:display에서 steering_mode 변경 시)
            if old_steering_mode == "S" and steering_mode == "H":
                # S에서 H로 변경될 때 현재 set_heading 값으로 heading_command 업데이트
                set_heading = data.get("set_heading")
                if set_heading is not None:
                    try:
                        set_heading_val = float(set_heading)
                        if 0 <= set_heading_val <= 360 and not self.data_invalid:
                            # 즉시 시그널 발생
                            self.s_to_h_mode_changed.emit(set_heading_val)
                            print(
                                f"🔄 S->H mode change (display): heading_command set to {set_heading_val:.1f}°"
                            )
                    except (ValueError, TypeError):
                        pass

            # H->S 모드 변경 감지 (autopilot:display에서 steering_mode 변경 시)
            if old_steering_mode == "H" and steering_mode == "S":
                # H에서 S로 변경될 때 시그널 발생
                self.h_to_s_mode_changed.emit()
                print("🔄 H->S mode change (display): steering_mode changed to S")

            # M->S 모드 변경 감지는 AutopilotPanel의 on_steering_mode_changed에서 처리
            # (사용자 스위치 조작이 더 즉시적이므로 여기서는 주석처리)
            # if old_steering_mode == "M" and steering_mode == "S":
            #     # M에서 S로 변경될 때 현재 heading 값으로 heading_command 업데이트
            #     heading = data.get("heading")
            #     if heading is not None:
            #         try:
            #             heading_val = float(heading)
            #             if 0 <= heading_val <= 360 and not self.data_invalid:
            #                 # 즉시 시그널 발생
            #                 self.m_to_s_mode_changed.emit(heading_val)
            #                 print(
            #                     f"🔄 M->S mode change: heading_command set to {heading_val:.1f}°"
            #                 )
            #         except (ValueError, TypeError):
            #             pass

            # 상태 저장
            self.current_ext_ready = ext_ready
            self.current_steering_mode = steering_mode

            # 상태가 변경되었으면 시그널 발생
            if (
                old_ext_ready != self.current_ext_ready
                or old_steering_mode != self.current_steering_mode
            ):
                self.display_state_changed.emit()

        except Exception as e:
            print(f"UI update error: {e}")
            self.data_invalid = True

    def update_display(self):
        """디스플레이 업데이트 (기존 시뮬레이션용)"""
        # Redis 업데이트로 대체됨
        pass

    def toggle_hc_rdy(self):
        """E. HC RDY 버튼 깜빡이기 (위치 고정, 투명도로 깜빡임)"""
        if self.show_hc_rdy:
            self.hc_rdy_visible = not self.hc_rdy_visible
            if self.hc_rdy_visible:
                # 켜질 때: 파란색 배경으로 표시
                self.hc_rdy_btn.setStyleSheet(
                    """
                    QPushButton {
                        background-color: #0066FF;
                        color: white;
                        border: 1px solid #0088FF;
                        border-radius: 3px;
                        font-weight: bold;
                        font-size: 9px;
                    }
                """
                )
            else:
                # 꺼질 때: 투명하게 (위치는 유지)
                self.hc_rdy_btn.setStyleSheet(
                    """
                    QPushButton {
                        background-color: transparent;
                        color: transparent;
                        border: 1px solid transparent;
                        border-radius: 3px;
                        font-weight: bold;
                        font-size: 9px;
                    }
                """
                )

    def set_ext_hdg_visible(self, visible):
        """EXT HDG 표시 여부 설정"""
        self.show_ext_hdg = visible
        self.ext_hdg_label.setVisible(visible)

    def set_hc_rdy_visible(self, visible):
        """E. HC RDY 버튼 표시 여부 설정"""
        self.show_hc_rdy = visible
        self.hc_rdy_btn.setVisible(visible)

    def set_rudder_angle(self, angle):
        """실제 러더각 설정 (LCD 내부 RUDDER 게이지)"""
        if hasattr(self, "rudder_gauge"):
            self.rudder_gauge.set_rudder_angle(angle)

    def update_steering_mode(self, steering_mode):
        """Steering mode에 따른 EXT HDG 표시 업데이트"""
        show_ext_hdg = steering_mode == "H"
        self.set_ext_hdg_visible(show_ext_hdg)

    def update_heading_display(self, heading):
        """Heading 값 업데이트"""
        if heading is None or not self.redis_connected:
            # 데이터 없음 또는 Redis 연결 끊김
            self.current_hdg_label.setText("*.*")
            self.current_hdg_label.setStyleSheet(
                "color: #FF0000; font-size: 36px; font-weight: bold;"
            )
            return

        try:
            heading_val = float(heading)

            # 초기 heading 값 전달 (한 번만)
            if (
                not self.initial_heading_sent
                and 0 <= heading_val <= 360
                and not self.data_invalid
            ):
                self.initial_heading_sent = True
                self.initial_heading_received.emit(heading_val)
                print(f"✅ Initial heading sent to AutopilotPanel: {heading_val:.1f}°")

            # 범위 검증 (0-360)
            if heading_val < 0 or heading_val > 360 or self.data_invalid:
                # 범위 초과 또는 invalid 상태
                self.current_hdg_label.setText(f"{heading_val:.1f}")
                self.current_hdg_label.setStyleSheet(
                    "color: #FF0000; font-size: 36px; font-weight: bold;"
                )
            else:
                # 정상 상태
                self.current_hdg_label.setText(f"{heading_val:.1f}")
                self.current_hdg_label.setStyleSheet(
                    "color: #00FF00; font-size: 36px; font-weight: bold;"
                )

        except (ValueError, TypeError):
            # 잘못된 데이터 형식
            self.current_hdg_label.setText("*.*")
            self.current_hdg_label.setStyleSheet(
                "color: #FF0000; font-size: 36px; font-weight: bold;"
            )

    def update_rudder_display(self, rudder):
        """Rudder 값 업데이트"""
        if rudder is None or not self.redis_connected:
            # 데이터 없음 또는 Redis 연결 끊김 - 게이지를 빨간색으로 표시
            if hasattr(self, "rudder_gauge"):
                self.rudder_gauge.set_rudder_angle(0)  # 중앙 위치
                self.rudder_gauge.set_error_state(True)  # 에러 상태 표시
            return

        try:
            rudder_val = float(rudder)

            # 범위 검증 (-35 ~ +35)
            if rudder_val < -35 or rudder_val > 35 or self.data_invalid:
                # 범위 초과 또는 invalid 상태
                if hasattr(self, "rudder_gauge"):
                    self.rudder_gauge.set_rudder_angle(rudder_val)
                    self.rudder_gauge.set_error_state(True)  # 빨간색 표시
            else:
                # 정상 상태
                if hasattr(self, "rudder_gauge"):
                    self.rudder_gauge.set_rudder_angle(rudder_val)
                    self.rudder_gauge.set_error_state(False)  # 정상 색상

        except (ValueError, TypeError):
            # 잘못된 데이터 형식
            if hasattr(self, "rudder_gauge"):
                self.rudder_gauge.set_rudder_angle(0)
                self.rudder_gauge.set_error_state(True)

    def update_ext_ready(self, ext_ready, steering_mode):
        """ext_ready 상태에 따른 E.HC RDY 깜빡임 제어"""
        # ext_ready가 true이고 steering_mode가 S일 때만 깜빡임
        # M(Manual/Hand) 모드에서는 표시하지 않음
        should_blink = ext_ready and (steering_mode == "S")
        self.set_hc_rdy_visible(should_blink)

    def set_redis_disconnected(self):
        """Redis 연결 끊김 상태 처리"""
        self.redis_connected = False
        self.current_hdg_label.setText("*.*")
        self.current_hdg_label.setStyleSheet(
            "color: #FF0000; font-size: 36px; font-weight: bold;"
        )

        if hasattr(self, "rudder_gauge"):
            self.rudder_gauge.set_rudder_angle(0)
            self.rudder_gauge.set_error_state(True)

        self.set_hc_rdy_visible(False)
        self.set_ext_hdg_visible(False)

    def set_no_data(self):
        """데이터 없음 상태 처리"""
        self.redis_connected = True  # Redis는 연결되어 있지만 데이터가 없음
        self.current_hdg_label.setText("*.*")
        self.current_hdg_label.setStyleSheet(
            "color: #FFFF00; font-size: 36px; font-weight: bold;"
        )  # 노란색으로 구분

        if hasattr(self, "rudder_gauge"):
            self.rudder_gauge.set_rudder_angle(0)
            self.rudder_gauge.set_error_state(False)  # 데이터 없음은 에러가 아님
