"""
Control Buttons Widget
각종 컨트롤 버튼들을 관리하는 위젯
"""
from PyQt5.QtWidgets import QWidget, QPushButton, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

class ControlButtons(QWidget):
    """컨트롤 버튼 그룹"""
    
    # 시그널 정의
    alarm_pressed = pyqtSignal()
    power_pressed = pyqtSignal()
    menu_pressed = pyqtSignal()
    nfu_pressed = pyqtSignal()
    auto_pressed = pyqtSignal()
    standby_pressed = pyqtSignal()
    acknowledge_pressed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """버튼 UI 구성"""
        layout = QVBoxLayout()
        layout.setSpacing(5)
        
        # 상단 버튼 행
        top_row = QHBoxLayout()
        self.alarm_btn = self.create_button("ALARM", "#8B4513")
        self.power_btn = self.create_button("POWER", "#4B4B4B")
        top_row.addWidget(self.alarm_btn)
        top_row.addWidget(self.power_btn)
        
        # 메뉴 버튼 (전체 너비)
        self.menu_btn = self.create_button("MENU", "#4B4B4B")
        
        # 중간 버튼 행
        middle_row = QHBoxLayout()
        self.nfu_btn = self.create_button("NFU", "#4B4B4B")
        self.auto_btn = self.create_button("AUTO", "#4B4B4B")
        middle_row.addWidget(self.nfu_btn)
        middle_row.addWidget(self.auto_btn)
        
        # 하단 버튼 행
        bottom_row = QHBoxLayout()
        self.standby_btn = self.create_button("STBY", "#4B4B4B")
        self.ack_btn = self.create_button("ACK", "#FF4444")  # 빨간색
        bottom_row.addWidget(self.standby_btn)
        bottom_row.addWidget(self.ack_btn)
        
        # 레이아웃에 추가
        layout.addLayout(top_row)
        layout.addWidget(self.menu_btn)
        layout.addLayout(middle_row)
        layout.addLayout(bottom_row)
        
        self.setLayout(layout)
    
    def create_button(self, text, color="#4B4B4B"):
        """표준 버튼 생성"""
        btn = QPushButton(text)
        btn.setFixedSize(60, 35)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: 2px solid #666;
                border-radius: 5px;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:pressed {{
                background-color: #666;
                border: 2px solid #888;
            }}
            QPushButton:hover {{
                border: 2px solid #999;
            }}
        """)
        return btn
    
    def connect_signals(self):
        """버튼 시그널 연결"""
        self.alarm_btn.clicked.connect(self.alarm_pressed.emit)
        self.power_btn.clicked.connect(self.power_pressed.emit)
        self.menu_btn.clicked.connect(self.menu_pressed.emit)
        self.nfu_btn.clicked.connect(self.nfu_pressed.emit)
        self.auto_btn.clicked.connect(self.auto_pressed.emit)
        self.standby_btn.clicked.connect(self.standby_pressed.emit)
        self.ack_btn.clicked.connect(self.acknowledge_pressed.emit)
    
    def set_auto_active(self, active):
        """AUTO 버튼 활성화 상태 설정"""
        if active:
            self.auto_btn.setStyleSheet("""
                QPushButton {
                    background-color: #00AA00;
                    color: white;
                    border: 2px solid #666;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 10px;
                }
                QPushButton:pressed {
                    background-color: #008800;
                }
            """)
        else:
            self.auto_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4B4B4B;
                    color: white;
                    border: 2px solid #666;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 10px;
                }
                QPushButton:pressed {
                    background-color: #666;
                }
            """)
    
    def set_standby_active(self, active):
        """STANDBY 버튼 활성화 상태 설정"""
        if active:
            self.standby_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FFAA00;
                    color: white;
                    border: 2px solid #666;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 10px;
                }
                QPushButton:pressed {
                    background-color: #DD8800;
                }
            """)
        else:
            self.standby_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4B4B4B;
                    color: white;
                    border: 2px solid #666;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 10px;
                }
                QPushButton:pressed {
                    background-color: #666;
                }
            """)
