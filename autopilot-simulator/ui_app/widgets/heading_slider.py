"""
Heading Slider Widget
마우스 드래그로 heading을 조절하는 수직 슬라이더
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt, pyqtSignal, QPoint
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont
import math


class HeadingSlider(QWidget):
    """수직 heading 슬라이더"""

    # 값 변경 시그널
    value_changed = pyqtSignal(float)

    def __init__(self):
        super().__init__()
        self.value = 0.0  # 0-359 degrees
        self.min_value = 0.0
        self.max_value = 359.0
        self.last_mouse_pos = QPoint()
        self.dragging = False
        self.enabled = True  # 슬라이더 활성화 상태

        self.setup_ui()

    def setup_ui(self):
        """UI 구성"""
        layout = QVBoxLayout()
        layout.setSpacing(5)

        # 슬라이더 영역
        self.slider_area = SliderArea()
        self.slider_area.value_changed.connect(self.on_value_changed)
        layout.addWidget(self.slider_area)

        # 현재 값 표시 라벨 (LCD 스타일)
        self.value_label = QLabel("000°")
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet(
            """
            QLabel {
                color: #00FF00;
                font-size: 16px;
                font-weight: bold;
                background-color: #0C0C0C;
                border: 2px solid #333;
                border-radius: 5px;
                padding: 8px;
                min-width: 70px;
                min-height: 25px;
            }
        """
        )
        layout.addWidget(self.value_label)

        self.setLayout(layout)
        self.update_display()

    def on_value_changed(self, value):
        """값 변경 처리"""
        self.value = value
        self.update_display()
        self.value_changed.emit(value)

    def update_display(self):
        """디스플레이 업데이트"""
        self.value_label.setText(f"{self.value:05.1f}°")

    def set_value(self, value):
        """외부에서 값 설정"""
        # 0-360 범위로 제한 (360도까지 허용)
        if value < 0:
            value = 0.0
        elif value > 360:
            value = 360.0

        self.value = value
        self.slider_area.set_value(value)
        self.update_display()

    def set_enabled(self, enabled):
        """슬라이더 활성화/비활성화 설정"""
        self.enabled = enabled
        self.slider_area.set_enabled(enabled)

        # 비활성화 시 시각적 피드백
        if enabled:
            self.value_label.setStyleSheet(
                """
                QLabel {
                    color: #00FF00;
                    font-size: 16px;
                    font-weight: bold;
                    background-color: #0C0C0C;
                    border: 2px solid #333;
                    border-radius: 5px;
                    padding: 8px;
                    min-width: 70px;
                    min-height: 25px;
                }
            """
            )
        else:
            self.value_label.setStyleSheet(
                """
                QLabel {
                    color: #666666;
                    font-size: 16px;
                    font-weight: bold;
                    background-color: #0C0C0C;
                    border: 2px solid #333;
                    border-radius: 5px;
                    padding: 8px;
                    min-width: 70px;
                    min-height: 25px;
                }
            """
            )


class SliderArea(QWidget):
    """실제 슬라이더 드래그 영역"""

    value_changed = pyqtSignal(float)

    def __init__(self):
        super().__init__()
        self.value = 0.0
        self.dragging = False
        self.last_mouse_y = 0
        self.enabled = True  # 슬라이더 활성화 상태

        self.setFixedSize(50, 120)
        self.setMouseTracking(True)

    def mousePressEvent(self, event):
        """마우스 클릭 시작"""
        if not self.enabled:
            return
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.last_mouse_y = event.y()

    def mouseMoveEvent(self, event):
        """마우스 드래그"""
        if not self.enabled or not self.dragging:
            return
        if self.dragging:
            # 마우스 Y 이동량을 각도 변화로 변환
            delta_y = self.last_mouse_y - event.y()  # 위로 드래그하면 양수

            # 감도 조절 (픽셀당 각도)
            sensitivity = 0.5  # 2픽셀 = 1도 (더 정밀한 조작)
            angle_change = delta_y * sensitivity

            new_value = self.value + angle_change

            # 0-360 범위로 제한 (360도까지 허용)
            if new_value < 0:
                new_value = 0.0
            elif new_value > 360:
                new_value = 360.0

            if abs(new_value - self.value) > 0.05:  # 최소 변화량 0.1도 단위
                self.set_value(new_value)

            self.last_mouse_y = event.y()

    def mouseReleaseEvent(self, event):
        """마우스 클릭 종료"""
        if not self.enabled:
            return
        if event.button() == Qt.LeftButton:
            self.dragging = False

    def wheelEvent(self, event):
        """마우스 휠 이벤트"""
        if not self.enabled:
            return
        delta = event.angleDelta().y() / 120.0  # 휠 한 칸당
        angle_change = delta * 1.0  # 휠 한 칸당 1도

        new_value = self.value + angle_change

        # 0-360 범위로 제한 (360도까지 허용)
        if new_value < 0:
            new_value = 0.0
        elif new_value > 360:
            new_value = 360.0

        self.set_value(new_value)

    def set_enabled(self, enabled):
        """슬라이더 활성화/비활성화 설정"""
        self.enabled = enabled
        self.update()  # 시각적 업데이트

    def set_value(self, value):
        """값 설정"""
        # 0-360 범위로 제한 (360도까지 허용)
        if value > 360.0:
            value = 360.0
        elif value < 0.0:
            value = 0.0

        if abs(self.value - value) > 0.05:  # 최소 변화량 0.1도 단위
            self.value = value
            self.value_changed.emit(value)
            self.update()

    def paintEvent(self, event):
        """슬라이더 그리기"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        width = self.width()
        height = self.height()

        # 배경 (슬라이더 트랙)
        painter.setPen(QPen(QColor(100, 100, 100), 2))
        painter.setBrush(QBrush(QColor(40, 40, 40)))
        track_rect = (width // 2 - 3, 10, 6, height - 20)
        painter.drawRoundedRect(*track_rect, 3, 3)

        # 슬라이더 핸들 위치 계산
        # value 0-360을 슬라이더 위치로 변환 (0이 맨 아래)
        handle_y = 10 + (height - 40) * (1.0 - self.value / 360.0)

        # 슬라이더 핸들
        if self.enabled:
            painter.setPen(QPen(QColor(200, 200, 200), 2))
            painter.setBrush(QBrush(QColor(150, 150, 150)))
        else:
            # 비활성화 상태일 때 회색으로 표시
            painter.setPen(QPen(QColor(100, 100, 100), 2))
            painter.setBrush(QBrush(QColor(80, 80, 80)))

        handle_rect = (width // 2 - 8, int(handle_y) - 5, 16, 10)
        painter.drawRoundedRect(*handle_rect, 2, 2)

        # 드래그 중일 때 하이라이트 (활성화 상태에서만)
        if self.dragging and self.enabled:
            painter.setPen(QPen(QColor(255, 255, 0), 2))
            painter.setBrush(QBrush(QColor(255, 255, 0, 100)))
            painter.drawRoundedRect(*handle_rect, 2, 2)

        # 눈금 표시 (주요 방향)
        painter.setPen(QPen(QColor(150, 150, 150), 1))
        painter.setFont(QFont("Arial", 7))

        for angle in [0, 90, 180, 270, 360]:  # N, E, S, W, N
            tick_y = 10 + (height - 40) * (1.0 - angle / 360.0)

            # 눈금선
            painter.drawLine(width - 15, int(tick_y), width - 5, int(tick_y))

            # 방향 라벨
            directions = {0: "N", 90: "E", 180: "S", 270: "W", 360: "N"}
            direction = directions.get(angle, str(angle))
            painter.drawText(width - 25, int(tick_y) + 3, direction)
