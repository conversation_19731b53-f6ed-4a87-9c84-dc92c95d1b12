"""
Steering Mode Switch Widget
M(Hand), S(HC/TC), H 모드를 선택하는 스위치
"""

from PyQt5.QtWidgets import QWidget, QHBoxLayout, QPushButton, QLabel, QVBoxLayout
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class SteeringModeSwitch(QWidget):
    """Steering Mode 선택 스위치"""

    # 모드 변경 시그널
    mode_changed = pyqtSignal(str)  # M, S, H

    def __init__(self):
        super().__init__()
        self.current_mode = "M"  # 기본값: Hand (내부적으로는 M)
        self.setup_ui()
        self.update_buttons()

    def setup_ui(self):
        """UI 구성"""
        layout = QVBoxLayout()
        layout.setSpacing(3)

        # Mode 라벨
        mode_label = QLabel("Mode")
        mode_label.setAlignment(Qt.AlignCenter)
        mode_label.setStyleSheet(
            """
            QLabel {
                color: #CCCCCC;
                font-size: 10px;
                font-weight: bold;
            }
        """
        )
        layout.addWidget(mode_label)

        # 버튼 그룹
        button_layout = QHBoxLayout()
        button_layout.setSpacing(3)

        # HAND 버튼 (내부적으로는 M)
        self.hand_btn = self.create_mode_button("HAND", "Hand Steering")
        self.hand_btn.clicked.connect(lambda: self.set_mode("M"))

        # TC/HC 버튼 (내부적으로는 S)
        self.tc_hc_btn = self.create_mode_button(
            "TC/HC", "Track Control / Heading Control"
        )
        self.tc_hc_btn.clicked.connect(lambda: self.set_mode("S"))

        button_layout.addWidget(self.hand_btn)
        button_layout.addWidget(self.tc_hc_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def create_mode_button(self, mode, tooltip):
        """모드 버튼 생성"""
        btn = QPushButton(mode)
        btn.setFixedSize(45, 20)  # 더 컴팩트하게
        btn.setToolTip(tooltip)
        btn.setStyleSheet(
            """
            QPushButton {
                background-color: #4B4B4B;
                color: white;
                border: 1px solid #666;
                border-radius: 3px;
                font-weight: bold;
                font-size: 8px;
            }
            QPushButton:hover {
                border: 1px solid #999;
            }
            QPushButton:pressed {
                background-color: #666;
            }
        """
        )
        return btn

    def set_mode(self, mode):
        """모드 설정"""
        if self.current_mode != mode:
            self.current_mode = mode
            self.update_buttons()
            self.mode_changed.emit(mode)
            print(f"Steering mode changed to: {mode}")

    def update_buttons(self):
        """버튼 상태 업데이트"""
        # 모든 버튼을 기본 상태로
        buttons = [self.hand_btn, self.tc_hc_btn]
        for btn in buttons:
            btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #4B4B4B;
                    color: white;
                    border: 1px solid #666;
                    border-radius: 3px;
                    font-weight: bold;
                    font-size: 8px;
                }
                QPushButton:hover {
                    border: 1px solid #999;
                }
                QPushButton:pressed {
                    background-color: #666;
                }
            """
            )

        # 현재 선택된 버튼을 활성화 상태로
        active_btn = None
        if self.current_mode == "M":
            active_btn = self.hand_btn
        elif self.current_mode == "S":
            active_btn = self.tc_hc_btn

        if active_btn:
            active_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #00AA00;
                    color: white;
                    border: 2px solid #00CC00;
                    border-radius: 3px;
                    font-weight: bold;
                    font-size: 8px;
                }
                QPushButton:hover {
                    background-color: #00CC00;
                    border: 2px solid #00EE00;
                }
                QPushButton:pressed {
                    background-color: #008800;
                }
            """
            )

    def get_mode(self):
        """현재 모드 반환"""
        return self.current_mode

    def get_mode_description(self):
        """현재 모드 설명 반환"""
        descriptions = {"M": "Hand", "S": "HC/TC", "H": "Auto"}
        return descriptions.get(self.current_mode, "Unknown")
