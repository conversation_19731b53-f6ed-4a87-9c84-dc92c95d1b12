"""
Rotary Knob Widget
회전 노브를 모방한 위젯
"""
from PyQt5.QtWidgets import QWidget
from PyQt5.QtCore import Qt, pyqtSignal, QPoint
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont
import math

class RotaryKnob(QWidget):
    """회전 노브 위젯"""
    
    # 값 변경 시그널
    value_changed = pyqtSignal(float)
    
    def __init__(self):
        super().__init__()
        self.value = 0.0  # 0-359 degrees
        self.min_value = 0.0
        self.max_value = 359.0
        self.last_mouse_pos = QPoint()
        self.dragging = False
        
        self.setFixedSize(80, 80)
        self.setMouseTracking(True)
    
    def mousePressEvent(self, event):
        """마우스 클릭 시작"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.last_mouse_pos = event.pos()
    
    def mouseMoveEvent(self, event):
        """마우스 드래그"""
        if self.dragging:
            # 마우스 이동량을 각도 변화로 변환
            delta_y = self.last_mouse_pos.y() - event.pos().y()
            delta_x = event.pos().x() - self.last_mouse_pos.x()
            
            # 수직 이동을 주로 사용하되, 수평 이동도 고려
            angle_change = (delta_y + delta_x * 0.5) * 0.5
            
            new_value = self.value + angle_change
            
            # 0-359 범위로 제한
            if new_value < self.min_value:
                new_value = self.max_value
            elif new_value > self.max_value:
                new_value = self.min_value
            
            if abs(new_value - self.value) > 1.0:  # 최소 변화량
                self.set_value(new_value)
            
            self.last_mouse_pos = event.pos()
    
    def mouseReleaseEvent(self, event):
        """마우스 클릭 종료"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
    
    def wheelEvent(self, event):
        """마우스 휠 이벤트"""
        delta = event.angleDelta().y() / 120.0  # 휠 한 칸당 15도
        angle_change = delta * 5.0
        
        new_value = self.value + angle_change
        
        # 0-359 범위로 순환
        if new_value < self.min_value:
            new_value = self.max_value
        elif new_value > self.max_value:
            new_value = self.min_value
        
        self.set_value(new_value)
    
    def set_value(self, value):
        """값 설정"""
        # 0-359 범위로 정규화
        while value < 0:
            value += 360
        while value >= 360:
            value -= 360
            
        if abs(self.value - value) > 0.1:  # 최소 변화량
            self.value = value
            self.value_changed.emit(value)
            self.update()
    
    def paintEvent(self, event):
        """노브 그리기"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 중심점과 반지름
        center_x = self.width() // 2
        center_y = self.height() // 2
        radius = min(center_x, center_y) - 5
        
        # 외곽 원 (노브 테두리)
        painter.setPen(QPen(QColor(150, 150, 150), 2))
        painter.setBrush(QBrush(QColor(60, 60, 60)))
        painter.drawEllipse(center_x - radius, center_y - radius, 
                          radius * 2, radius * 2)
        
        # 내부 원 (노브 표면)
        inner_radius = radius - 8
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.setBrush(QBrush(QColor(80, 80, 80)))
        painter.drawEllipse(center_x - inner_radius, center_y - inner_radius,
                          inner_radius * 2, inner_radius * 2)
        
        # 노브 포인터 (현재 값 표시)
        angle_rad = math.radians(self.value - 90)  # -90도로 12시 방향을 0도로 설정
        pointer_length = inner_radius - 5
        
        end_x = center_x + pointer_length * math.cos(angle_rad)
        end_y = center_y + pointer_length * math.sin(angle_rad)
        
        painter.setPen(QPen(QColor(255, 255, 0), 3))
        painter.drawLine(center_x, center_y, int(end_x), int(end_y))
        
        # 중심점
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        painter.setBrush(QBrush(QColor(120, 120, 120)))
        painter.drawEllipse(center_x - 3, center_y - 3, 6, 6)
        
        # 눈금 표시 (주요 방향)
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        for angle in [0, 90, 180, 270]:  # N, E, S, W
            tick_angle_rad = math.radians(angle - 90)
            tick_start_radius = radius - 3
            tick_end_radius = radius + 2
            
            start_x = center_x + tick_start_radius * math.cos(tick_angle_rad)
            start_y = center_y + tick_start_radius * math.sin(tick_angle_rad)
            end_x = center_x + tick_end_radius * math.cos(tick_angle_rad)
            end_y = center_y + tick_end_radius * math.sin(tick_angle_rad)
            
            painter.drawLine(int(start_x), int(start_y), int(end_x), int(end_y))
        
        # 값 텍스트 표시
        painter.setPen(QPen(QColor(255, 255, 255), 1))
        painter.setFont(QFont("Arial", 8, QFont.Bold))
        text = f"{self.value:.0f}°"
        text_rect = painter.fontMetrics().boundingRect(text)
        text_x = center_x - text_rect.width() // 2
        text_y = center_y + radius + 15
        painter.drawText(text_x, text_y, text)
