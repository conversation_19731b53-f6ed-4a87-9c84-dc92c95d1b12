#!/usr/bin/env python3
"""
Autopilot Simulator Runner

This script runs the autopilot simulator that:
1. Receives RSA (Rudder, Speed, Azimuth) messages from ship simulator (***********)
2. Processes the ship state data using autopilot control logic
3. Calculates rudder commands using fn_control_apl algorithm
4. Sends rudder commands back to ship simulator

The autopilot supports three control modes:
- N-mode: Normal heading control
- R-mode: Rate of turn control  
- T-mode: Tactical diameter control

Usage:
    python run_autopilot.py
"""

import asyncio
import signal
import sys
import os

# Add the project root and autopilot-simulator directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'autopilot-simulator'))

# Import from autopilot-simulator directory
from autopilot_simulator import AutopilotSimulator

def setup_multicast_routing():
    """Setup multicast routing for Docker environment."""
    try:
        # Add multicast route for Docker bridge network
        os.system("ip route add ***********/24 dev eth0 2>/dev/null || true")
        print("✅ Multicast routing configured")
    except Exception as e:
        print(f"⚠️ Could not configure multicast routing: {e}")

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    print(f"\n🛑 Received signal {signum}, shutting down autopilot...")
    sys.exit(0)

async def main():
    """Run the autopilot simulator."""
    print("=" * 60)
    print("🚢 Autopilot Simulator")
    print("=" * 60)
    print("Implements fn_control_apl control logic")
    print("Receives RSA messages and sends rudder commands")
    print("=" * 60)

    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Setup multicast routing for Docker environment
    setup_multicast_routing()
    
    try:
        # Create autopilot simulator
        simulator = AutopilotSimulator(
            config_file="config/autopilot_config.json",
            communication_config="config/autopilot_communication.json"
        )
        
        print("\nAutopilot Configuration:")
        control_params = simulator.get_control_parameters()
        print(f"  Control Mode: N/R/T mode support")
        print(f"  Heading Control: Kp={control_params['kp_psi']}, Kd={control_params['kd_psi']}")
        print(f"  Turn Control: Kp={control_params['kp_rot']}, Ki={control_params['ki_rot']}")
        print(f"  Feedforward: Kff={control_params['kff_rot']}")
        print(f"  Rudder Limit: ±{control_params['rudder_limit']}°")
        print(f"  Ship Type: {simulator.config.get_ship_type()}")
        print()
        
        # Run simulation
        await simulator.run_simulation()
        
    except KeyboardInterrupt:
        print("\n🛑 Autopilot simulation interrupted by user")
    except Exception as e:
        print(f"❌ Autopilot simulation error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🔌 Cleaning up...")

if __name__ == "__main__":
    # Run the autopilot simulator
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Autopilot simulator stopped")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
