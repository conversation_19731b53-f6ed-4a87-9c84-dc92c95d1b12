"""
Autopilot controller implementing the fn_control_apl logic.
Handles heading control, turn control, and rudder command generation.
"""

import numpy as np
from typing import Dict, Any
from ship_state import CommandState, ActualState, ControlState, ShipStateManager
from autopilot_config import AutopilotConfig

def logic_bound_to_pi(angle: float) -> float:
    """Bound angle to [-π, π] range."""
    while angle > np.pi:
        angle -= 2 * np.pi
    while angle < -np.pi:
        angle += 2 * np.pi
    return angle

def logic_bound_abs_pi(angle: float) -> float:
    """Calculate shortest angular difference in [-π, π] range."""
    # Normalize angle to [-π, π] range
    angle = logic_bound_to_pi(angle)
    return angle

class AutopilotController:
    """
    Autopilot controller implementing the control logic from fn_control_apl.
    
    Supports three control modes:
    - N-mode: Normal heading control
    - R-mode: Rate of turn control  
    - T-mode: Tactical diameter control (radius command)
    """
    
    def __init__(self, config: AutopilotConfig):
        """
        Initialize autopilot controller.

        Args:
            config: Autopilot configuration object
        """
        self.config = config
        self.control_param = config.get_control_parameters()
        self.ship_param = config.get_ship_parameters()

        # Ship state manager
        self.state_manager = ShipStateManager()

        # Constants
        self.degree_to_radian = np.pi / 180
        self.radian_to_degree = 180 / np.pi
        self.knots_to_ms = 0.5144
        self._turn_dir = None
        
    def update_command_state(self, htc_data=None, pavk_data=None, xte_data=None) -> None:
        """
        Update command state from HTC, PAVK, and XTE data.

        Args:
            htc_data: HTCData object from parsed HTC message
            pavk_data: PAVKData object from parsed PAVK message
            xte_data: XTEData object from parsed XTE message
        """
        self.state_manager.update_command_state(htc_data, pavk_data, xte_data)

    def update_actual_state(self, nmea_data: dict) -> None:
        """
        Update actual state from NMEA data.

        Args:
            nmea_data: Dictionary containing parsed NMEA sentence data
        """
        self.state_manager.update_actual_state(nmea_data)

        # Update control state with actual values (in radians for calculations)
        actual = self.state_manager.actual_state
        control = self.state_manager.control_state

        control.psi = actual.heading * self.degree_to_radian
        control.r = actual.turn_rate * self.degree_to_radian /60   # deg/min to rad/s ## 내일이거부터 봐라 TO DO
        control.u = actual.sog * self.knots_to_ms  # knots to m/s

        # Debug control state update
        if hasattr(self, '_control_debug_counter'):
            self._control_debug_counter += 1
        else:
            self._control_debug_counter = 1

        # Show control state update occasionally (2Hz 기준)
        if self._control_debug_counter % 10 == 1:
            print(f"🔧 CONTROL STATE UPDATE:")
            print(f"   actual.heading: {actual.heading:.1f}°")
            print(f"   actual.turn_rate: {actual.turn_rate:.3f}°/min")
            print(f"   actual.sog: {actual.sog:.1f} knots")
            print(f"   control.psi: {control.psi:.3f} rad ({control.psi * self.radian_to_degree:.1f}°)")
            print(f"   control.r: {control.r:.6f} rad/s ({control.r * self.radian_to_degree * 60:.3f}°/min)")
            print(f"   control.u: {control.u:.1f} m/s")


    
    def calculate_rudder_command(self) -> float:
        """
        Calculate rudder command using the fn_control_apl logic.

        Returns:
            Rudder command in degrees
        """
        # Get state references for cleaner code
        command = self.state_manager.command_state
        actual = self.state_manager.actual_state
        control = self.state_manager.control_state

        # Calculate heading error
        control.psi = logic_bound_to_pi(control.psi)

        # Use HTC heading command directly (protected from realtime_input)
        # command.hdg_cmd should contain HTC value, not realtime_input value
        hdg_cmd = command.hdg_cmd * self.degree_to_radian
        hdg_cmd = logic_bound_to_pi(hdg_cmd)
        control.psi_e = logic_bound_abs_pi(control.psi - hdg_cmd)

        # Debug output (every 50 calculations)
        if hasattr(self, '_debug_counter'):
            self._debug_counter += 1
        else:
            self._debug_counter = 1



        # Control logic (N/R/T mode)
        if command.turn_mode == "N":
            self._turn_dir = None
            # N-mode control (heading control)
            control.rudder_cmd = -(
                control.psi_e * self.control_param["kp_psi"]
                + control.r * self.control_param["kd_psi"]
            )

            # H 모드 디버그 로그 (steering_mode가 H일 때, 2Hz 기준)
            if command.steering_mode == "H" :
                print(f"🔧 H-MODE DEBUG (N-mode control):")
                print(f"   steering_mode: {command.steering_mode}")
                print(f"   turn_mode: {command.turn_mode}")
                print(f"   heading_deg: {control.psi * self.radian_to_degree:.1f}°")
                print(f"   heading_cmd: {command.hdg_cmd:.1f}°")
                print(f"   heading_error: {control.psi_e * self.radian_to_degree:.1f}°")
                print(f"   rudder_cmd: {control.rudder_cmd}°")
                print(f"   turn_rate: {control.r * self.radian_to_degree * 60:.3f}°/min")
                print(f"   kp_psi: {self.control_param['kp_psi']}")
                print(f"   kd_psi: {self.control_param['kd_psi']}")
                print(f"   psi_e_term: {-(control.psi_e * self.control_param['kp_psi']) * self.radian_to_degree:.1f}°")
                print(f"   r_term: {-(control.r * self.control_param['kd_psi']) * self.radian_to_degree:.1f}°")

        else:
            # R/T-mode control (turning control)

            # turn_dir을 처음 한 번만 판단
            if command.turn_mode == "T":
                if not hasattr(self, "_turn_dir") or self._turn_dir is None:
                    # 정확한 방향 판단
                    diff = (command.hdg_cmd - actual.heading + 540) % 360 - 180

                    if diff == 0:
                        self._turn_dir = "R"  # default (변침 없음)
                    elif diff > 0:
                        self._turn_dir = "R"
                    else:
                        self._turn_dir = "L"
            # 방향 적용
            if self._turn_dir == "L":
                rot_cmd_with_direction = -command.rot_cmd
            else:
                rot_cmd_with_direction = command.rot_cmd

            #  로그 
            print(f"🔧 R/T-MODE DEBUG:")
            print(f"   steering_mode: {command.steering_mode}")
            print(f"   turn_mode: {command.turn_mode}")
            print(f"   heading_deg: {control.psi * self.radian_to_degree:.1f}°")
            print(f"   heading_cmd: {command.hdg_cmd:.1f}°")
            print(f"   turn_dir: {self._turn_dir}")
            print(f"   rot_cmd: {command.rot_cmd:.1f}°/min")
            print(f"   rot_cmd_with_direction: {rot_cmd_with_direction:.1f}°/min")
            print(f"   current_turn_rate: {control.r * self.radian_to_degree * 60:.3f}°/min")

            control.rot_e = (
                control.r - rot_cmd_with_direction * self.degree_to_radian / 60
            )
            control.rot_e_sum += control.rot_e

            print(f"   rot_e: {control.rot_e * self.radian_to_degree * 60:.3f}°/min")

            # Ship type specific feedforward control
            # Use rot_cmd with direction for feedforward calculation
            ship_type = self.ship_param["ship_type"]
            if ship_type == 1:  # Ferry
                feedforward_rudder_cmd = rot_cmd_with_direction / self.control_param["kff_rot"]
            elif ship_type == 2:  # Container
                if rot_cmd_with_direction < 0:
                    feedforward_rudder_cmd = rot_cmd_with_direction / self.control_param["kff_rot"]
                else:
                    feedforward_rudder_cmd = rot_cmd_with_direction / self.control_param["kff_rot"]
            elif ship_type == 3:  # Tanker
                feedforward_rudder_cmd = rot_cmd_with_direction / self.control_param["kff_rot"]
            else:
                raise ValueError(
                    f"ship_type error : {ship_type} "
                    f"and Failed to assign feedforward_rudder_cmd."
                )

            kp_rot = self.control_param["kp_rot"]
            ki_rot = self.control_param["ki_rot"] * abs(control.rot_e)

            control.rudder_cmd = (
                feedforward_rudder_cmd * self.degree_to_radian
                - control.rot_e * kp_rot
                - control.rot_e_sum * ki_rot * abs(control.rot_e)
            )

            # I control clear
            if control.rot_e * control.rot_e_prev < 0:
                control.rot_e_sum = 0

            # Save previous value
            control.rot_e_prev = control.rot_e

        # Convert rudder command from radians to degrees
        rudder_cmd_deg = control.rudder_cmd * self.radian_to_degree

        # Rudder limit (in degrees)
        rudder_limit_deg = self.config.get_rudder_limit()

        # Debug rudder saturation
        rudder_before_limit = rudder_cmd_deg
        rudder_cmd_deg = np.clip(rudder_cmd_deg, -rudder_limit_deg, rudder_limit_deg)
        rudder_after_limit = rudder_cmd_deg

        # Show rudder saturation warning
        if abs(rudder_before_limit) > rudder_limit_deg and hasattr(self, '_saturation_counter'):
            self._saturation_counter += 1
            if self._saturation_counter % 4 == 1:  # Every 2 seconds at 2Hz
                print(f"⚠️  RUDDER SATURATION")
        elif not hasattr(self, '_saturation_counter'):
            self._saturation_counter = 0

        # Store back to control state (in radians for internal use)
        control.rudder_cmd = rudder_cmd_deg * self.degree_to_radian

        return rudder_cmd_deg  # Return in degrees
    
    def get_control_status(self) -> Dict[str, Any]:
        """
        Get current control status.

        Returns:
            Dictionary containing control status information
        """
        command = self.state_manager.command_state
        actual = self.state_manager.actual_state
        control = self.state_manager.control_state

        return {
            "turn_mode": command.turn_mode,
            "steering_mode": command.steering_mode,
            "heading_deg": control.psi * self.radian_to_degree,
            "heading_cmd_deg": command.hdg_cmd,
            "heading_error_deg": control.psi_e * self.radian_to_degree,
            "turn_rate_deg_min": control.r * self.radian_to_degree * 60,
            "turn_rate_cmd_deg_min": command.rot_cmd,
            "turn_rate_error": control.rot_e,
            "rudder_cmd_deg": control.rudder_cmd,
            "speed_ms": control.u,
            "speed_knots": control.u / self.knots_to_ms,
            "nas_ready": command.nas_ready,
            "command_valid": self.state_manager.is_command_valid(),
            "actual_valid": self.state_manager.is_actual_state_valid()
        }

    def get_ship_state_manager(self) -> ShipStateManager:
        """Get ship state manager."""
        return self.state_manager

    def reset_controller(self) -> None:
        """Reset controller state."""
        control = self.state_manager.control_state
        control.rot_e_sum = 0.0
        control.rot_e_prev = 0.0
        control.psi_e = 0.0
        control.rot_e = 0.0
