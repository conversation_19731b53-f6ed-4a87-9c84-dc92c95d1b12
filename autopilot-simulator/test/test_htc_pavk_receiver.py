#!/usr/bin/env python3
"""
Simple HTC/PAVK message receiver test script.
Tests if HTC/PAVK messages are being received on port 4001.
"""

import socket
import time
import sys

def test_htc_pavk_receiver():
    """Test HTC/PAVK message reception on port 4001."""
    
    # Create UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    try:
        # Bind to port 4001 on all interfaces
        sock.bind(('***********', 4001))
        sock.settimeout(1.0)  # 1 second timeout
        
        print("🔍 HTC/PAVK Receiver Test Started")
        print(f"   Listening on ***********:4001")
        print(f"   Waiting for HTC/PAVK messages...")
        print(f"   Press Ctrl+C to stop")
        print("-" * 60)
        
        message_count = 0
        message_types = set()
        
        while True:
            try:
                # Receive data
                data, addr = sock.recvfrom(1024)
                message = data.decode('utf-8').strip()
                
                message_count += 1
                
                # Determine message type
                if 'HTC' in message or 'htc' in message.lower():
                    msg_type = "HTC"
                elif 'PAVK' in message or 'pavk' in message.lower():
                    msg_type = "PAVK"
                else:
                    msg_type = "UNKNOWN"
                
                message_types.add(msg_type)
                
                # Print first few messages and then periodic updates
                if message_count <= 10 or message_count % 50 == 0:
                    print(f"[{message_count:4d}] {msg_type}: {message[:80]}...")

                # Show summary every 100 messages
                if message_count % 100 == 0:
                    print(f"Received {message_count} messages")
                    print(f"   Message types: {', '.join(sorted(message_types))}")
                    print("-" * 60)
                    
            except socket.timeout:
                # No message received within timeout
                if message_count == 0:
                    print("No messages received yet... (waiting)")
                continue

            except KeyboardInterrupt:
                print(f"\nTest stopped by user")
                break

    except Exception as e:
        print(f"Error: {e}")
        return False
        
    finally:
        sock.close()
        
    print(f"\nFinal Statistics:")
    print(f"   Total messages received: {message_count}")
    print(f"   Message types seen: {', '.join(sorted(message_types))}")

    if message_count > 0:
        print("HTC/PAVK messages are being received successfully!")
        return True
    else:
        print("No HTC/PAVK messages received")
        return False

if __name__ == "__main__":
    success = test_htc_pavk_receiver()
    sys.exit(0 if success else 1)
