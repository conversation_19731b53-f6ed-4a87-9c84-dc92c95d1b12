#!/usr/bin/env python3
"""
Test script to set up Redis data for autopilot user input testing.
"""

import json
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.redis_manager import RedisManager

def setup_test_data():
    """Set up test data in Redis."""
    
    # Load Redis configuration
    try:
        with open("config/redis_config.json", 'r') as f:
            redis_config = json.load(f)
    except FileNotFoundError:
        print("Redis config file not found, using defaults")
        redis_config = {
            "host": "localhost",
            "port": 6379,
            "password": "",
            "ssl": False,
            "options": {"auto_conn": True, "auto_utc_set": False}
        }
    
    # Initialize Redis manager
    redis_manager = RedisManager(**redis_config)
    
    # Test data matching the expected structure
    test_data = {
        "steering_mode": "M",
        "heading_command": 65.0,
        "rudder_command": -10.0,
        "tki_status": "A",
        "last_updated": "2025-01-22T10:00:00Z"
    }
    
    # Set data in Redis
    redis_key = "autopilot:user_input"
    success = redis_manager.SetRedisMsg(redis_key, test_data)
    
    if success:
        print(f"✅ Successfully set test data in Redis key: {redis_key}")
        print(f"Data: {json.dumps(test_data, indent=2)}")
        
        # Verify by reading back
        retrieved_data = redis_manager.GetRedisMsg(redis_key)
        if retrieved_data:
            print(f"✅ Verified data retrieval: {json.dumps(retrieved_data, indent=2)}")
        else:
            print("❌ Failed to retrieve data back from Redis")
    else:
        print(f"❌ Failed to set test data in Redis")
    
    # Disconnect
    redis_manager.disconnect()

if __name__ == "__main__":
    print("Setting up Redis test data for autopilot user input...")
    setup_test_data()
