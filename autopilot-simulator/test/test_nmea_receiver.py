#!/usr/bin/env python3
"""
Simple NMEA message receiver test script.
Tests if NMEA messages are being received on port 50000 from ship_simulator.
"""

import socket
import time
import sys

def test_nmea_receiver():
    """Test NMEA message reception on port 50000."""
    
    # Create UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    try:
        # Bind to port 50000 on all interfaces
        sock.bind(('***********', 50000))
        sock.settimeout(1.0)  # 1 second timeout
        
        print("🔍 NMEA Receiver Test Started")
        print(f"   Listening on ***********:50000")
        print(f"   Waiting for NMEA messages from ship_simulator...")
        print(f"   Press Ctrl+C to stop")
        print("-" * 60)
        
        message_count = 0
        message_types = set()
        
        while True:
            try:
                # Receive data
                data, addr = sock.recvfrom(1024)
                message = data.decode('utf-8').strip()
                
                message_count += 1
                
                # Extract message type (e.g., GGA, VTG, HDT, etc.)
                if message.startswith('$'):
                    # Standard NMEA format: $GPGGA,... or $GPHDT,...
                    msg_type = message[3:6] if len(message) >= 6 else "UNK"
                elif 'UdPbC' in message:
                    # NMEA 0450 format with UdPbC header
                    # Find the actual NMEA sentence after the UdPbC header
                    if '$' in message:
                        nmea_part = message[message.find('$'):]
                        msg_type = nmea_part[3:6] if len(nmea_part) >= 6 else "UNK"
                    else:
                        msg_type = "0450"
                else:
                    msg_type = "UNK"
                
                message_types.add(msg_type)
                
                # Print first few messages and then periodic updates
                if message_count <= 10 or message_count % 50 == 0:
                    print(f"[{message_count:4d}] {msg_type}: {message[:80]}...")

                # Show summary every 100 messages
                if message_count % 100 == 0:
                    print(f"Received {message_count} messages")
                    print(f"   Message types: {', '.join(sorted(message_types))}")
                    print("-" * 60)
                    
            except socket.timeout:
                # No message received within timeout
                if message_count == 0:
                    print("No messages received yet... (waiting)")
                continue

            except KeyboardInterrupt:
                print(f"\nTest stopped by user")
                break

    except Exception as e:
        print(f"Error: {e}")
        return False
        
    finally:
        sock.close()
        
    print(f"\n📊 Final Statistics:")
    print(f"   Total messages received: {message_count}")
    print(f"   Message types seen: {', '.join(sorted(message_types))}")
    
    if message_count > 0:
        print("✅ NMEA messages are being received successfully!")
        return True
    else:
        print("❌ No NMEA messages received")
        return False

if __name__ == "__main__":
    success = test_nmea_receiver()
    sys.exit(0 if success else 1)
