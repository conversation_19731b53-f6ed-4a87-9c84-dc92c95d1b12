#!/usr/bin/env python3
"""
Test script for Redis-based RealtimeInputManager.
"""

import json
import sys
import os
import time

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from realtime_input_manager import RealtimeInputManager
from utils.redis_manager import RedisManager

def test_realtime_input_manager():
    """Test the Redis-based RealtimeInputManager."""

    print("🧪 Testing Redis-based RealtimeInputManager")
    print("=" * 50)

    # Set up environment variables for testing
    os.environ.setdefault("REDIS_HOST", "localhost")
    os.environ.setdefault("REDIS_PORT", "6379")
    os.environ.setdefault("REDIS_PASSWORD", "")
    os.environ.setdefault("REDIS_SSL", "false")
    os.environ.setdefault("REDIS_AUTO_CONN", "true")
    os.environ.setdefault("REDIS_AUTO_UTC_SET", "false")

    # First, set up test data in Redis
    print("1. Setting up test data in Redis...")

    # Load Redis configuration from environment
    redis_config = {
        "host": os.getenv("REDIS_HOST", "localhost"),
        "port": int(os.getenv("REDIS_PORT", "6379")),
        "password": os.getenv("REDIS_PASSWORD", ""),
        "ssl": os.getenv("REDIS_SSL", "false").lower() == "true",
        "options": {
            "auto_conn": os.getenv("REDIS_AUTO_CONN", "true").lower() == "true",
            "auto_utc_set": os.getenv("REDIS_AUTO_UTC_SET", "false").lower() == "true"
        }
    }
    
    # Initialize Redis manager for setup
    redis_manager = RedisManager(**redis_config)
    
    # Test data
    test_data = {
        "steering_mode": "M",
        "heading_command": 65.0,
        "rudder_command": -10.0,
        "last_updated": "2025-01-22T10:00:00Z"
    }
    
    redis_key = "autopilot:user_input"
    success = redis_manager.SetRedisMsg(redis_key, test_data)
    
    if success:
        print(f"✅ Test data set in Redis: {json.dumps(test_data, indent=2)}")
    else:
        print("❌ Failed to set test data in Redis")
        return
    
    # Test RealtimeInputManager
    print("\n2. Testing RealtimeInputManager...")
    
    try:
        # Initialize RealtimeInputManager
        input_manager = RealtimeInputManager()
        
        # Get initial data
        data = input_manager.get_data()
        print(f"✅ Initial data loaded:")
        print(f"   Steering Mode: {data.steering_mode}")
        print(f"   Heading Command: {data.hdg_cmd}°")
        print(f"   Rudder Command: {data.rudder_cmd}°")
        
        # Test update functionality
        print("\n3. Testing update functionality...")
        
        # Update Redis data
        updated_data = {
            "steering_mode": "H",
            "heading_command": 90.0,
            "rudder_command": 5.0,
            "tki_status": "V",
            "last_updated": "2025-01-22T10:05:00Z"
        }
        
        redis_manager.SetRedisMsg(redis_key, updated_data)
        print(f"✅ Updated Redis data: {json.dumps(updated_data, indent=2)}")
        
        # Wait a bit and update
        time.sleep(0.2)
        updated = input_manager.update()
        
        if updated:
            data = input_manager.get_data()
            print(f"✅ Data updated successfully:")
            print(f"   Steering Mode: {data.steering_mode}")
            print(f"   Heading Command: {data.hdg_cmd}°")
            print(f"   Rudder Command: {data.rudder_cmd}°")
            print(f"   TKI Status: {data.tki_status}")
        else:
            print("❌ Update failed or no changes detected")
        
        # Test getter methods
        print("\n4. Testing getter methods...")
        print(f"   get_steering_mode(): {input_manager.get_steering_mode()}")
        print(f"   get_hdg_cmd(): {input_manager.get_hdg_cmd()}")
        print(f"   get_rudder_cmd(): {input_manager.get_rudder_cmd()}")
        print(f"   get_tki_status(): {input_manager.get_tki_status()}")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        redis_manager.disconnect()

if __name__ == "__main__":
    test_realtime_input_manager()
