"""
Main autopilot simulator class that orchestrates all autopilot components.
"""

import asyncio
import time
import os
import logging
from typing import Dict, Any

from autopilot_config import AutopilotConfig
from command_parser import HTCPAVKParser
from nmea_parser import NMEAParser
from nmea_generator import NMEAGenerator
from autopilot_controller import <PERSON>pilot<PERSON>ontroll<PERSON>
from communication_manager import CommunicationManager
from realtime_input_manager import RealtimeInputManager
from utils.redis_manager import RedisManager

class AutopilotSimulator:
    """
    Main autopilot simulator class that coordinates all autopilot components.
    
    Orchestrates:
    - RSA message reception and parsing
    - Autopilot control logic
    - Rudder command transmission
    - Simulation timing
    """
    
    def __init__(self, config_file: str = None, communication_config: str = None):
        """
        Initialize autopilot simulator.
        
        Args:
            config_file: Path to autopilot configuration file
            communication_config: Path to communication configuration file
        """
        # Initialize components
        self.config = AutopilotConfig(config_file)
        self.htc_pavk_parser = HTCPAVKParser()
        self.nmea_parser = NMEAParser()
        self.nmea_generator = NMEAGenerator()
        self.controller = AutopilotController(self.config)
        self.communication = CommunicationManager(communication_config)
        self.realtime_input = RealtimeInputManager()

        # Initialize Redis manager for display data
        self.display_redis_manager = self._init_display_redis_manager()
        
        # Simulation parameters
        self.simulation_step = 0
        self.simulation_frequency = 2.0  # 2 Hz (0.5초마다 실행)
        self.simulation_timestep = 1.0 / self.simulation_frequency
        
        # Statistics (initialize first)
        self.total_htc_messages = 0
        self.total_pavk_messages = 0
        self.total_xte_messages = 0
        self.total_nmea_messages = 0
        self.total_output_messages = 0
        self.control_errors = 0

        # HTC message monitoring for default value setting
        self.htc_miss_count = 0  # Counter for consecutive missing HTC messages
        self.htc_miss_threshold = 50  # Threshold for setting default values
        self.htc_default_applied = False  # Flag to track if default values are applied

        # Control state
        self.last_htc_data = self._apply_htc_default_values()
        self.last_pavk_data = None
        self.last_xte_data = None
        self.last_nmea_data = {}
        self.last_rudder_command = 0.0
        self.current_pavk_status = "V"  # Track current PAVK status
        self.current_pydk_status = "V"  # Track current PYDK status (for YDK maker)
        self.current_ptki_status = "V"  # Track current PYDK status (for TKM maker)

        # Control mode status
        self.control_mode_active = False  # True when in control mode (following HTC commands)
        
        print("🚢 Autopilot Simulator initialized")
        print(f"  Simulation frequency: {self.simulation_frequency} Hz")
        print(f"  Ship type: {self.config.get_ship_type()}")
        print(f"  Rudder limit: ±{self.config.get_rudder_limit()}°")

    def _init_display_redis_manager(self) -> RedisManager:
        """
        Initialize Redis manager for display data transmission.

        Returns:
            RedisManager instance configured from environment variables
        """
        def str_to_bool(value: str) -> bool:
            """Convert string to boolean."""
            return value.lower() in ('true', '1', 'yes', 'on')

        try:
            redis_config = {
                "host": os.getenv("REDIS_HOST", "localhost"),
                "port": int(os.getenv("REDIS_PORT", "6379")),
                "password": os.getenv("REDIS_PASSWORD", ""),
                "ssl": str_to_bool(os.getenv("REDIS_SSL", "false")),
                "options": {
                    "auto_conn": str_to_bool(os.getenv("REDIS_AUTO_CONN", "true")),
                    "auto_utc_set": str_to_bool(os.getenv("REDIS_AUTO_UTC_SET", "false"))
                }
            }

            redis_manager = RedisManager(**redis_config)
            print(f"  Display Redis: {redis_config['host']}:{redis_config['port']}")
            return redis_manager

        except Exception as e:
            print(f"⚠️ Warning: Failed to initialize display Redis manager: {e}")
            # Return a dummy manager that does nothing
            return None

    async def run_simulation(self) -> None:
        """
        Run the main autopilot simulation loop asynchronously.

        This method handles:
        - RSA message reception and parsing
        - Autopilot control calculation
        - Rudder command transmission
        - Simulation timing
        """
        print("🚀 Starting autopilot simulation...")
        print(f"Listening for HINAS messages from: {self.communication.hinas_server_ip}:{self.communication.hinas_server_port}")
        print(f"Listening for NMEA messages from: {self.communication.nmea_receiver_ip}:{self.communication.nmea_receiver_port}")

        # Setup communication
        self.communication.setup_communication()

        # Main simulation loop
        while True:
            loop_start = time.perf_counter()
            self.simulation_step += 1

            # Process input messages (non-blocking)
            await self._process_input_messages()

            # Calculate and send autopilot output
            await self._calculate_and_send_output()

            # Control simulation rate (10Hz)
            sleep_time = max(0, self.simulation_timestep - (time.perf_counter() - loop_start))
            await asyncio.sleep(sleep_time)

    async def _process_input_messages(self) -> None:
        """Process incoming HTC, PAVK, and NMEA messages with batch processing."""
        try:
            # Process messages in batches for better performance at 2Hz
            # 10 rounds per loop to handle multiple messages efficiently
            for _ in range(10):
                # Process NMEA messages first and more frequently for better heading data
                await self._process_nmea_messages()

                # Process HTC, PAVK messages (non-blocking)
                await self._process_command_messages()

            # Show periodic status
            if self.simulation_step % 10 == 0:  # Every 5 seconds at 2Hz
                self._print_status()

        except Exception as e:
            print(f"Error processing input messages: {e}")
            self.control_errors += 1

    async def _process_command_messages(self) -> None:
        """Process incoming HTC/PAVK messages."""
        try:
            # Receive command message (non-blocking)
            command_message = await self.communication.receive_autopilot_command_message()

            # Check for held HTC data even if no new message
            held_htc_data = self.htc_pavk_parser.get_htc_data_with_hold()
            if held_htc_data and not command_message:
                # Use held HTC data when no new message available
                self.controller.update_command_state(htc_data=held_htc_data)

            if command_message:
                # Determine message type and parse accordingly
                if 'HTC' in command_message or 'htc' in command_message.lower():
                    # Parse HTC/HTD message
                    htc_data = self.htc_pavk_parser.parse_htc_message(command_message)

                    if htc_data:
                        self.last_htc_data = htc_data
                        self.total_htc_messages += 1
                        
                        # Reset HTC miss counter when message is received
                        self.htc_miss_count = 0
                        self.htc_default_applied = False

                        # Update controller with new command state
                        self.controller.update_command_state(htc_data=htc_data)

                        # Show sample HTC data (once every 100 messages)
                        if self.total_htc_messages % 100 == 1:
                            print(f"HTC RX: Steer={htc_data.steering_mode}, HDG_CMD={htc_data.hdg_cmd:.1f}°, Turn={htc_data.turn_mode}, Count={self.total_htc_messages}")
                    else:
                        print(f"⚠️ Failed to parse HTC message: {command_message[:50]}...")
                        # Increment miss counter for failed parse
                        self.htc_miss_count += 1

                elif self._is_pavk_message(command_message):
                    # Parse PAVK message
                    pavk_data = self.htc_pavk_parser.parse_pavk_message(command_message)

                    if pavk_data:
                        self.last_pavk_data = pavk_data
                        self.total_pavk_messages += 1

                        # Update current PAVK status
                        self.current_pavk_status = pavk_data.nas_status

                        # Update controller with new navigation status
                        self.controller.update_command_state(pavk_data=pavk_data)

                        # Show sample PAVK data (once every 100 messages)
                        if self.total_pavk_messages % 100 == 1:
                            print(f"PAVK RX: NAS={pavk_data.nas_status}, Ready={pavk_data.nas_ready}, Count={self.total_pavk_messages}")
                    else:
                        print(f"⚠️ Failed to parse PAVK message: {command_message[:50]}...")

                elif 'XTE' in command_message or 'xte' in command_message.lower():
                    # Parse XTE message
                    xte_data = self.htc_pavk_parser.parse_xte_message(command_message)

                    if xte_data:
                        self.last_xte_data = xte_data
                        self.total_xte_messages += 1

                        # Update controller with cross track error data
                        self.controller.update_command_state(xte_data=xte_data)

                        # Show sample XTE data (once every 100 messages)
                        if self.total_xte_messages % 100 == 1:
                            print(f"XTE RX: Cross_Track_Error={xte_data.cross_track_error:.3f}nm, Count={self.total_xte_messages}")
                    else:
                        print(f"Warning: Failed to parse XTE message: {command_message[:50]}...")

                else:
                    # Unknown message type
                    if hasattr(self, '_unknown_message_count'):
                        self._unknown_message_count += 1
                    else:
                        self._unknown_message_count = 1

                    # Show sample unknown message (once every 50 messages)
                    if self._unknown_message_count % 50 == 1:
                        msg_type = command_message.split(',')[0] if ',' in command_message else command_message[:10]
                        print(f"Unknown RX: {msg_type} Count={self._unknown_message_count}")
                    
                    # Increment HTC miss counter for non-HTC messages
                    self.htc_miss_count += 1
            else:
                # No command message received, increment HTC miss counter
                self.htc_miss_count += 1

            # Check if HTC messages have been missing for too long
            if self.htc_miss_count >= self.htc_miss_threshold and not self.htc_default_applied:
                self.last_htc_data = self._apply_htc_default_values()
                self.htc_default_applied = True

        except Exception as e:
            print(f"Error processing command messages: {e}")
            self.control_errors += 1

    def _is_pavk_message(self, message: str) -> bool:
        """
        Check if message is a PAVK message.

        Handles various PAVK message formats:
        - PAVK
        - PAVK,NAS
        - PAVKNAS

        Args:
            message: Message string to check

        Returns:
            True if message is PAVK format, False otherwise
        """
        message_upper = message.upper()

        # Check for various PAVK patterns
        pavk_patterns = [
            'PAVK,',      # Standard PAVK with comma
            'PAVK,NAS',   # PAVK with NAS
            'PAVKNAS',    # PAVKNAS combined
            '$PAVK,',     # With NMEA prefix and comma
            '$PAVK,NAS',  # $PAVK,NAS specific pattern
        ]

        for pattern in pavk_patterns:
            if pattern in message_upper:
                return True

        # Also check if message starts with PAVK (case insensitive)
        if message_upper.strip().startswith('PAVK'):
            return True

        return False


    async def _process_nmea_messages(self) -> None:
        """Process incoming NMEA messages with high frequency processing."""
        try:
            # Process multiple NMEA messages per loop for better responsiveness
            messages_processed = 0
            hdt_messages_processed = 0
            max_messages_per_loop = 15  # Process up to 15 messages per loop

            for _ in range(max_messages_per_loop):
                # Receive NMEA message (non-blocking)
                nmea_message = await self.communication.receive_nmea_message()

                if nmea_message:
                    messages_processed += 1

                    # Parse NMEA message
                    nmea_data = self.nmea_parser.parse_nmea_sentence(nmea_message)

                    if nmea_data:
                        self.last_nmea_data.update(nmea_data)
                        self.total_nmea_messages += 1

                        # Priority processing for HDT messages
                        if 'HDT' in nmea_data:
                            hdt_messages_processed += 1
                            # Always update controller immediately for HDT
                            self.controller.update_actual_state(nmea_data)

                            # Debug: Show HDT data (reduced frequency)
                            if self.simulation_step % 20 == 0:
                                hdt = nmea_data['HDT']
                                print(f"🔍 HDT: {hdt['heading']:.1f}°")
                        else:
                            # Update controller for other NMEA data
                            self.controller.update_actual_state(nmea_data)

                        # Update timestamp for sensor data freshness check
                        import time
                        self._last_nmea_time = time.time()
                else:
                    # No more messages available, break the loop
                    break

            # Show processing stats occasionally
            if messages_processed > 1 and self.simulation_step % 100 == 0:
                print(f"📊 Processed {messages_processed} NMEA messages ({hdt_messages_processed} HDT) in one loop")

        except Exception as e:
            print(f"❌ Error processing NMEA messages: {e}")
            self.control_errors += 1

    async def _calculate_and_send_output(self) -> None:
        """Calculate autopilot control and send output messages."""
        try:
            # Get ship state manager
            state_manager = self.controller.get_ship_state_manager()

            # Initialize rudder command
            rudder_command = 0.0

            # Debug control conditions (2Hz = 0.5초마다, 6번째 실행에서 디버그)
            if self.simulation_step == 6:
                current_time = time.time()
                htc_age = current_time - state_manager.command_state.last_htc_time
                print(f"CONTROL_DEBUG: command_valid={state_manager.is_command_valid()}")
                print(f"  HTC age: {htc_age:.1f}s (timeout: {state_manager.htc_timeout}s)")
                print(f"  nas_ready: {state_manager.command_state.nas_ready}")
                print(f"  HTC messages received: {self.total_htc_messages}")
                print(f"  PAVK messages received: {self.total_pavk_messages}")
                print(f"  Current PAVK status: {self.current_pavk_status}")

            # Update real-time input parameters first to get latest data
            self.realtime_input.update()

            # Always perform rudder calculation
            # Get real-time input data (fresh data after update)
            rt_data = self.realtime_input.get_data()

            if rt_data.steering_mode == "M":
                # Manual mode: use real-time input rudder command directly (already in degrees)
                rudder_command = rt_data.rudder_cmd

            elif self.control_mode_active:
                # Control mode: ALWAYS follow HTC heading command
                # HTC hdg_cmd is already in command_state, don't override it
                print(f"🎯 CONTROL MODE: Following HTC hdg_cmd={state_manager.command_state.hdg_cmd:.1f}°")
                rudder_command = self.controller.calculate_rudder_command()  # Returns degrees

            else:
                # Non-control mode: use real-time input hdg_cmd
                state_manager.command_state.hdg_cmd = rt_data.hdg_cmd
                rudder_command = self.controller.calculate_rudder_command()

            # Store the calculated rudder command in control state
            state_manager.control_state.rudder_cmd = rudder_command

            # Generate autopilot output sentences with real-time input and PAVK status
            system_status = self._get_system_status()

            # Calculate ext_ready before generating sentences
            rt_data = self.realtime_input.get_data()

            # Determine control mode and ext_ready based on maker
            if rt_data.maker == "TKM":
                # TKM: ext_ready based on user input steering mode
                ext_ready = ((rt_data.steering_mode == "S" or rt_data.steering_mode == "H") and
                            self.current_pavk_status == "A" and self.last_htc_data.steering_mode == "H" and
                            system_status == "valid")

                if ext_ready == True and rt_data.steering_mode =="H":
                    self.control_mode_active = True
                else:
                    self.control_mode_active = False

            elif rt_data.maker == "YDK":
                # YDK: control mode based on HTC steering mode
                ext_ready = (self.last_htc_data.steering_mode == "H" and
                                          self.current_pavk_status == "A" and
                                          system_status == "valid")
                if ext_ready  :
                    self.control_mode_active = True
                else:
                    self.control_mode_active = False


            # Note: steering_mode for HTD is now handled in _generate_htd based on control_mode_active

            nmea_sentences = self.nmea_generator.generate_autopilot_sentences(
                state_manager.command_state,
                state_manager.actual_state,
                state_manager.control_state,
                self.realtime_input,
                self.current_pavk_status,
                rudder_command,
                system_status,
                ext_ready,
                self.control_mode_active
            )



            # Send autopilot output sentences
            self.communication.send_autopilot_sentences(nmea_sentences)
            self.last_rudder_command = rudder_command
            self.total_output_messages += len(nmea_sentences)

            # Send display data to Redis
            self._send_display_data(state_manager, ext_ready, rudder_command, nmea_sentences)


        except Exception as e:
            print(f"Error calculating/sending output: {e}")
            self.control_errors += 1

    def _send_display_data(self, state_manager, ext_ready, rudder_command: float, nmea_sentences: dict) -> None:
        """
        Send display data to Redis for UI consumption using final sentence values.

        Args:
            state_manager: Ship state manager
            rt_data: Real-time input data
            rudder_command: Current rudder command in degrees
            nmea_sentences: Final NMEA sentences being transmitted
        """
        try:
            if not self.display_redis_manager:
                return  # Skip if Redis manager is not available

            # Get current heading from actual state (실제 선박의 현재 heading)
            current_heading = state_manager.actual_state.heading

            # Check if we have valid NMEA data
            has_valid_nmea = (current_heading != 0.0 and self.total_nmea_messages > 0)

            if not has_valid_nmea:
                # No valid NMEA data - set heading to None for display
                current_heading = None

            # Parse HTD sentence to get final transmitted values
            htd_sentence = nmea_sentences.get("HTD", "")
            steering_mode, heading_cmd, rudder_val = self._parse_htd_for_display(htd_sentence)

            # Handle system status based on NMEA data availability
            if not has_valid_nmea:
                # No valid NMEA data - set system to invalid
                system_status = "invalid"
                rudder_val = 0.0  # Override rudder value for invalid state
            else:
                # Valid NMEA data - use calculated system status
                system_status = self._get_system_status()

            # Determine ext_ready condition
            # H mode + PAVK NAS A status + system valid
            # Create display data using final sentence values
            display_data = {
                "steering_mode": steering_mode,  # From HTD sentence
                "heading": current_heading,  # None if no NMEA data
                "rudder": float(rudder_command),  # From HTD sentence
                "set_heading": float(heading_cmd),  # From HTD sentence
                "ext_ready": ext_ready,
                "control_mode": self.control_mode_active,
                "status": system_status
            }

            # Send to Redis
            self.display_redis_manager.SetRedisMsg("autopilot:display", display_data)

            # Debug output (every 4 steps = 2초마다)
            if self.simulation_step % 4 == 0:
                hdg_str = f"{current_heading:.1f}°" if current_heading is not None else "None"
                print(f"Display Data: Mode={steering_mode}, HDG={hdg_str}, RUD={rudder_val:.1f}°, HDG_CMD={heading_cmd:.1f}°, ExtRdy={ext_ready}, Status={system_status}")

        except Exception as e:
            print(f"Error sending display data: {e}")

    def _parse_htd_for_display(self, htd_sentence: str) -> tuple:
        """
        Parse HTD sentence to extract display values.

        Args:
            htd_sentence: HTD NMEA sentence

        Returns:
            Tuple of (steering_mode, heading_cmd, rudder_val)
        """
        try:
            if not htd_sentence or not htd_sentence.startswith('$'):
                return "S", 0.0, 0.0

            # Remove $ and checksum
            clean_sentence = htd_sentence[1:].split('*')[0]
            fields = clean_sentence.split(',')

            if len(fields) >= 11:
                # HTD format: $AGHTD,V,rudder_angle,rudder_dir,steering_mode,turn_mode,rudder_limit,turn_limit,radot_cmd,rot_cmd,hdg_cmd,...
                rudder_angle = float(fields[2]) if fields[2] else 0.0
                rudder_dir = fields[3] if fields[3] else "R"
                steering_mode = fields[4] if fields[4] else "S"
                heading_cmd = float(fields[10]) if fields[10] else 0.0

                # Apply rudder direction
                rudder_val = rudder_angle if rudder_dir == "R" else -rudder_angle

                return steering_mode, heading_cmd, rudder_val
            else:
                return "S", 0.0, 0.0

        except Exception as e:
            print(f"Error parsing HTD for display: {e}")
            return "S", 0.0, 0.0

    def _print_status(self) -> None:
        """Print periodic status information."""
        rt_data = self.realtime_input.get_data()
        state_manager = self.controller.get_ship_state_manager()

        # Get current values
        current_heading = state_manager.actual_state.heading
        current_rudder = self.last_rudder_command  # Already in degrees

        # Determine effective heading command
        if rt_data.steering_mode == 'H' and self.current_pavk_status == 'A' and self.last_htc_data:
            cmd_heading = self.last_htc_data.hdg_cmd
        else:
            cmd_heading = rt_data.hdg_cmd

        print(f"Step {self.simulation_step:6d} | "
              f"Mode: {rt_data.steering_mode} | "
              f"HDG: {current_heading:6.1f}° | "
              f"CMD: {cmd_heading:6.1f}° | "
              f"RUD: {current_rudder:6.1f}° | "
              f"Status: {self._get_system_status()}")

    def _get_system_status(self) -> str:
        """
        Determine system status based on sensor data and control capability.

        Returns:
            "valid" if system is operating normally, "invalid" if there are issues
        """
        # Check if we can receive sensor data (NMEA messages)
        if self.total_nmea_messages == 0 and self.simulation_step > 50:
            return "invalid"  # No sensor data after reasonable time

        # Check if we have recent sensor data (within last 5 seconds at 10Hz)
        if hasattr(self, '_last_nmea_time'):
            import time
            if time.time() - self._last_nmea_time > 5.0:
                return "invalid"  # Sensor data too old

        # Check if rudder control is working (control errors)
        if self.control_errors > 20:
            return "invalid"  # Too many control errors

        # Check if we have valid heading data
        state_manager = self.controller.get_ship_state_manager()
        if state_manager.actual_state.heading < 0 or state_manager.actual_state.heading > 360.0:
            return "invalid"  # Invalid heading data

        return "valid"

    def get_simulation_status(self) -> Dict[str, Any]:
        """
        Get current simulation status.

        Returns:
            Dictionary containing simulation status
        """
        status = {
            "simulation_step": self.simulation_step,
            "last_rudder_command": self.last_rudder_command,
            "statistics": {
                "total_htc_messages": self.total_htc_messages,
                "total_pavk_messages": self.total_pavk_messages,
                "total_nmea_messages": self.total_nmea_messages,
                "total_output_messages": self.total_output_messages,
                "control_errors": self.control_errors,
                "htc_parser_count": self.htc_pavk_parser.get_htc_message_count(),
                "pavk_parser_count": self.htc_pavk_parser.get_pavk_message_count(),
                "nmea_parser_counts": self.nmea_parser.get_message_counts()
            },
            "communication_status": self.communication.get_connection_info(),
        }
        
        if self.last_rsa_data:
            status["last_rsa_data"] = {
                "heading": self.last_rsa_data.heading,
                "speed": self.last_rsa_data.speed,
                "turn_rate": self.last_rsa_data.turn_rate,
                "heading_command": self.last_rsa_data.heading_command,
                "turn_mode": self.last_rsa_data.turn_mode
            }
        
        status["control_status"] = self.controller.get_control_status()
        
        return status

    def stop_simulation(self) -> None:
        """Stop the simulation and cleanup resources."""
        self.communication.close_communication()
        print("🛑 Autopilot simulation stopped")

    def reset_simulation(self) -> None:
        """Reset simulation state."""
        self.simulation_step = 0
        self.last_rsa_data = None
        self.last_rudder_command = 0.0
        self.total_rsa_messages = 0
        self.total_rudder_commands = 0
        self.control_errors = 0
        
        self.controller.reset_controller()
        
        print("🔄 Autopilot simulation reset")

    def set_control_parameters(self, **kwargs) -> None:
        """
        Update control parameters dynamically.
        
        Args:
            **kwargs: Control parameters to update
        """
        for key, value in kwargs.items():
            if key in self.controller.control_param:
                self.controller.control_param[key] = value
                print(f"Updated {key} = {value}")
            else:
                print(f"Unknown control parameter: {key}")

    def get_control_parameters(self) -> Dict[str, Any]:
        """Get current control parameters."""
        return self.controller.control_param.copy()

    def _apply_htc_default_values(self):
        """
        Apply default values when HTC messages are missing for too long.
        
        This method sets default HTC values to ensure system continues operating
        when HTC messages are not received for the specified threshold.
        
        Returns:
            HTCData object with default values
        """
        try:
            # Create default HTC data object
            from command_parser import HTCData
            
            default_htc_data = HTCData(
                steering_mode="M",  # Default to Manual mode
                turn_mode="R",      # Default turn mode
                hdg_cmd=0.0,        # Default heading command
                rot_cmd=0.0,        # Default rate of turn command
                rudder_limit=35.0,  # Default rudder limit
                turn_limit=5.0      # Default turn limit
            )
            
            # Update controller with default command state if controller exists
            if hasattr(self, 'controller') and self.controller:
                self.controller.update_command_state(htc_data=default_htc_data)
            
            # Only print if this is called after initialization (miss count > 0)
            if hasattr(self, 'htc_miss_count') and self.htc_miss_count > 0:
                print(f"⚠️ HTC Default Values Applied: Missing {self.htc_miss_count} consecutive HTC messages")
                print(f"   Default HTC: Steer={default_htc_data.steering_mode}, HDG_CMD={default_htc_data.hdg_cmd:.1f}°, Turn={default_htc_data.turn_mode}")
            
            return default_htc_data
            
        except Exception as e:
            print(f"❌ Error applying HTC default values: {e}")
            if hasattr(self, 'control_errors'):
                self.control_errors += 1
            
            # Return a minimal default object even if there's an error
            try:
                from command_parser import HTCData
                return HTCData(
                    steering_mode="M",
                    turn_mode="R", 
                    hdg_cmd=0.0,
                    rot_cmd=0.0,
                    rudder_limit=35.0,
                    turn_limit=5.0
                )
            except:
                return None
