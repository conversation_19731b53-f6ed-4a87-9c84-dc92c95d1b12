"""
Autopilot configuration manager.
Loads and manages all autopilot parameters from JSON configuration.
"""

import json
import math
import os
from pathlib import Path
from typing import Dict, Any

class AutopilotConfig:
    """
    Manages autopilot configuration from JSON file.
    
    Loads and provides access to:
    - Control parameters (kp_psi, kd_psi, kff_rot, etc.)
    - Ship type specific settings
    - Communication settings
    - Control mode settings (N/R/T mode)
    """
    
    def __init__(self, config_file: str = None):
        """
        Initialize autopilot configuration.

        Args:
            config_file: Path to JSON configuration file
        """
        # Configuration file path
        self.config_file = config_file or self._get_default_config_path()

        # Load configuration from JSON
        self.config = {}
        self.control_param = {}
        self.ship_param = {}
        self.communication_config = {}

        self._load_config()
        
        # Constants
        self.DEGREE_TO_RADIAN = math.pi / 180.0
        self.RADIAN_TO_DEGREE = 180.0 / math.pi

    def _get_default_config_path(self) -> str:
        """Get default configuration file path."""
        return os.path.join("autopilot-simulator", "config", "autopilot_config.json")
    
    def _load_config(self) -> None:
        """Load configuration from JSON file."""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"Config file not found: {self.config_file}")

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)

            self.control_param = self.config.get('control_parameters', {})
            self.ship_param = self.config.get('ship_parameters', {})
            self.communication_config = self.config.get('communication', {})

            print(f"Loaded autopilot config from: {self.config_file}")

        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in config file: {e}")
        except Exception as e:
            raise RuntimeError(f"Error loading autopilot config: {e}")
    


    def get_control_parameters(self) -> Dict[str, Any]:
        """Get control parameters."""
        return self.control_param.copy()
    
    def get_ship_parameters(self) -> Dict[str, Any]:
        """Get ship parameters."""
        return self.ship_param.copy()
    
    def get_communication_config(self) -> Dict[str, Any]:
        """Get communication configuration."""
        return self.communication_config.copy()
    
    def get_ship_type(self) -> int:
        """Get ship type."""
        return self.ship_param.get("ship_type", 2)
    
    def get_rudder_limit(self) -> float:
        """Get rudder angle limit in degrees."""
        return self.control_param.get("rudder_limit", 35.0)
    
    def save_config(self) -> None:
        """Save current configuration to file."""
        config_data = {
            "control_parameters": self.control_param,
            "ship_parameters": self.ship_param,
            "communication": self.communication_config
        }
        
        # Create config directory if it doesn't exist
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            print(f"Configuration saved to: {self.config_file}")
        except Exception as e:
            print(f"Error saving configuration: {e}")
