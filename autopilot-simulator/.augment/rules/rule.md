---
type: "always_apply"
description: "Example description"
---
# 프로젝트 작업 규칙 (Project Rules)

## �� CRITICAL: 표준 워크플로우 (Standard Workflow)

**모든 작업은 반드시 다음 워크플로우를 따라야 합니다:**

### 1. 선 계획, 후 실행

⚠️ 코드를 작성하기 전에, 먼저 문제를 완벽히 분석하고 관련 코드를 리뷰한 뒤 **task/todo.md 파일에 작업 계획을 세웁니다.**

### 2. 체크리스트 작성

📋 계획은 완료 시 하나씩 체크할 수 있는 **명확한 작업 목록 (Checklist) 형태**여야 합니다.

### 3. 계획 확정

✅ 작성한 계획을 확정하고 바로 작업을 시작합니다.

### 4. 작업 진행 및 보고

📊 계획이 승인되면, 목록의 작업을 순서대로 진행하며 완료된 항목을 체크하고 각 단계에서 변경 사항을 간략히 설명합니다.

### 5. 단순함 유지

🎯 모든 코드 변경은 가능한 한 **가장 단순하고 최소한으로 유지**합니다. 코드에 미치는 영향을 최소화하는 것이 핵심이며, 복잡하고 큰 수정은 피합니다.

### 6. 작업 단위 분리

🔄 하나의 작업은 **논리적으로 가장 작은 단위로 분리**합니다. 여러 가지 작업을 한 번에 묶어서 처리하지 않습니다.

### 7. 최종 요약 및 검토

📝 모든 작업이 끝나면 [**todo.md](http://todo.md/) 파일에 검토 (Review) 섹션을 추가**하여 전체 변경 사항과 관련 내용을 최종적으로 요약합니다.

---

## �� Git Commit Style Guide

### 기본 형식

```
<type>: <description>

```

### 타입 목록

- **feat**: 새로운 기능 추가
- **fix**: 버그 수정
- **docs**: 문서 변경
- **style**: 코드 스타일 변경 (포맷팅, 세미콜론 등)
- **refactor**: 리팩토링
- **test**: 테스트 코드 추가/수정
- **chore**: 빌드 도구, 패키지 매니저 설정 등

### 작성 규칙

- **description**은 50자 이내로 작성
- 영어 사용 시 동사 원형으로 시작 (add, fix, update 등)
- 마침표 사용하지 않음
- 명령형으로 작성
- 밑에 추가 설명이 들어갈 경우, 가급적 간결하게 두-세줄 이내로 작성

### 예시

```
feat: add user authentication system
fix: resolve memory leak in image processing
docs: update API documentation
style: format code with prettier
refactor: simplify database connection logic
test: add unit tests for payment module
chore: update dependencies to latest versions

```

---

## �� 코드 작성 스타일

### 기본 규칙

- 가급적 모든 코드 및 주석은 **영어로 작성**할 것
- Python 코드의 경우 **ruff**와 **mypy** 포매터 기준을 준수할 것

### 테스트 파일 관리

- 파일을 생성하고 테스트를 진행하였을 경우, **반드시 반드시 해당 파일은 테스트 이후 삭제할 것**

---

## ⚡ 중요 사항

### task/todo.md 관리

- **task/todo.md는 반드시 업데이트되어야 함**
- 테스트 및 작업이 실패하였을 경우, 해당 원인을 항상 todo.md에 업데이트
- 모든 작업 계획과 진행 상황을 기록

### Git 작업 범위

- 성공적으로 작업이 끝났을 경우 commit까지 진행
- **push는 하지 않음**