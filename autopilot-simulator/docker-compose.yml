services:
  autopilot-simulator:
    image: "autopilot-simulator:v0.0.1"
    container_name: autopilot-simulator
    network_mode: host
    privileged: true
    cap_add:
      - NET_ADMIN
      - NET_RAW
    volumes:
      - /home/<USER>/workspace/hinas-control-mock-simulator/autopilot-simulator:/app
    environment:
      - PYTHONPATH=/app
      - REDIS_HOST=localhost
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_SSL=false
      - REDIS_AUTO_CONN=true
      - REDIS_AUTO_UTC_SET=false
    # command: sleep infinity
