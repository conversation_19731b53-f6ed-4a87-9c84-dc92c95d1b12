#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import redis
import datetime
import json
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logging = logging.getLogger(__name__)


class RedisManager:
    def __init__(self, **params):
        self.host = params["host"]
        self.port = params["port"]
        password = params["password"]
        if password == "" or not password:
            self.password = None
        else:
            self.password = password

        self.is_connected_ = False
        self.ssl = params.get("ssl", False)
        self.ssl_ca_certs = params.get("ssl_ca_certs", None)
        self.ssl_cert_reqs = params.get("ssl_cert_reqs", None)
        self.ssl_certfile = params.get("ssl_certfile", None)
        self.ssl_keyfile = params.get("ssl_keyfile", None)

        self.auto_conn = params.get("options", {}).get("auto_conn", True)
        self.auto_utc_set = params.get("options", {}).get("auto_utc_set", True)
        self.socket_connect_timeout = 3
        self.socket_timeout = 3

        if self.auto_conn:
            self.connect()

    def _created_conn_contents(self):
        return redis.StrictRedis(
            host=self.host,
            port=self.port,
            password=self.password,
            ssl=self.ssl,
            ssl_cert_reqs=self.ssl_cert_reqs,
            ssl_certfile=self.ssl_certfile,
            ssl_keyfile=self.ssl_keyfile,
            socket_connect_timeout=self.socket_connect_timeout,
            socket_timeout=self.socket_timeout,
        )

    def connect(self):
        try:
            self.redis_conn = self._created_conn_contents()
            logging.info("Trying to connect to Redis")

            self.is_connected_ = self.redis_conn.ping()
            # logging.info(f"Connected to Redis: {self.is_connected_}")

            return True
        except redis.ConnectionError as e:
            logging.error(f"ConnectionError: Could not connect to Redis - {e}")
            self.is_connected_ = False
            return False
        except Exception as e:
            logging.exception(f"Unexpected error while connecting to Redis: {e}")
            self.is_connected_ = False
            return False

    def connect_check_status(self):
        """
        ping 으로 연결 상태 확인.
        socket_timeout 에 따라 짧게 실패 처리됩니다.
        """
        try:
            ok = self.redis_conn.ping()
            self.is_connected_ = bool(ok)
            return self.is_connected_
        except redis.ConnectionError as e:
            logging.error(f"[Redis] ConnectionError: {e}")
        except Exception as e:
            logging.exception(f"[Redis] Unexpected error: {e}")
        self.is_connected_ = False
        return False

    def get_pipeline(self):
        if not self.is_connected_:
            self.connect()
        return self.redis_conn.pipeline()

    def SetRedisMsg(self, channel: str, inputs: dict or str or list, ttl: int = None):
        if not self.is_connected_:
            self.connect()

        # --- 리스트 형태면 바로 JSON 직렬화하여 저장 ---
        print(inputs, type(inputs))
        if isinstance(inputs, list):
            try:
                output_json = json.dumps(inputs)
                if ttl:
                    self.redis_conn.set(channel, output_json, ex=ttl)
                    logging.debug(f"SET {channel} (ttl={ttl}): {output_json}")
                else:
                    self.redis_conn.set(channel, output_json)
                    logging.debug(f"SET {channel}: {output_json}")
                return True
            except Exception as e:
                logging.error(f"Redis SET error (list payload): {e}")
                return False

        if isinstance(inputs, str):
            try:
                inputs_dict = json.loads(inputs)
                if not isinstance(inputs_dict, dict):
                    raise ValueError("Invalid JSON format: Not a dictionary")
                inputs = inputs_dict
            except json.JSONDecodeError:
                logging.warning(
                    f"MessageError: Invalid JSON format in input string: {inputs}"
                )
                return False
            except ValueError as ve:
                logging.warning(f"MessageError: {ve}")
                return False

        output = {key: value for key, value in inputs.items() if value is not None}

        if self.auto_utc_set and "utc" not in output:
            output["issued_time"] = (
                datetime.datetime.utcnow().isoformat(timespec="seconds") + "Z"
            )

        if not output:
            logging.warning(f"MessageError: No data to set Redis in: {inputs}")
            return False

        output_json = json.dumps(output)

        try:
            if ttl:
                self.redis_conn.set(channel, output_json, ex=ttl)
                logging.debug(f"SET {channel} (ttl={ttl}): {output_json}")
            else:
                self.redis_conn.set(channel, output_json)
                logging.debug(f"SET {channel}: {output_json}")
            return True
        except Exception as e:
            logging.error(f"Redis SET error: {e}")
            return False

    def GetRedisMsg(self, channel: str):
        if not self.is_connected_:
            self.connect()

        try:
            if self.redis_conn.exists(channel):
                value_json = self.redis_conn.get(channel)
                value = json.loads(value_json)
                logging.debug(f"GET {channel}: {value}")
                return value
            else:
                logging.warning(f"MessageError: No data from Redis[{channel}]")
        except Exception as e:
            logging.error(f"Redis GET error: {e}")
            return None

    def DeleteRedisMsg(self, channel: str):  # -> bool:
        """
        주어진 Redis key(channel)를 삭제합니다.
        반환값: key가 삭제되었으면 True, 없었거나 실패하면 False
        """
        if not self.is_connected_:
            self.connect()

        try:
            deleted = self.redis_conn.delete(channel)
            logging.debug(f"DEL {channel}: {deleted}")
            return bool(deleted)
        except Exception as e:
            logging.error(f"Redis DELETE error: {e}")
            return False

    def disconnect(self):
        if not self.is_connected_:
            return False
        try:
            self.redis_conn.close()
            self.is_connected_ = False
            logging.info("Disconnected from Redis")
            return True
        except Exception as e:
            logging.error(f"Error during Redis disconnect: {e}")
            return False

    @property
    def is_connected(self):
        return self.is_connected_