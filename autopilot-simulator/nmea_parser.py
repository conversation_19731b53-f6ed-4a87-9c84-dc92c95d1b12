"""
NMEA sentence parser for autopilot simulator.
Parses NMEA 0450 sentences from ship simulator (GGA, VTG, HDT, ROT, etc.).
"""

import re
import time
from typing import Dict, Optional, Any

class NMEAParser:
    """
    Parser for NMEA 0450 sentences from ship simulator.
    
    Handles parsing of:
    - GGA: Global Positioning System Fix Data
    - VTG: Track Made Good and Ground Speed
    - HDT: Heading - True
    - ROT: Rate of Turn
    - THS: True Heading and Status
    - VBW: Dual Ground/Water Speed
    """
    
    def __init__(self):
        """Initialize NMEA parser."""
        self.message_counts = {
            'GGA': 0,
            'VTG': 0,
            'HDT': 0,
            'ROT': 0,
            'THS': 0,
            'RMC': 0,
            'VBW': 0
        }
        self.last_parsed_data = {}
    
    def parse_nmea_sentence(self, sentence: str) -> Optional[Dict[str, Any]]:
        """
        Parse NMEA sentence and extract data.
        
        Args:
            sentence: Raw NMEA sentence string
            
        Returns:
            Dictionary with parsed data, or None if parsing fails
        """
        try:
            # Clean the sentence
            sentence = sentence.strip()
            if not sentence.startswith('$'):
                return None
            
            # Remove checksum if present
            if '*' in sentence:
                sentence = sentence.split('*')[0]
            
            # Split into fields
            fields = sentence.split(',')
            if len(fields) < 1:
                return None
            
            # Extract talker and sentence type
            header = fields[0][1:]  # Remove $
            if len(header) < 5:
                return None

            talker = header[:2]
            sentence_type = header[2:5]  # Extract only the 3-letter sentence type
            
            # Parse based on sentence type
            if sentence_type == 'GGA':
                return self._parse_gga(fields)
            elif sentence_type == 'VTG':
                return self._parse_vtg(fields)
            elif sentence_type == 'HDT':
                return self._parse_hdt(fields)
            elif sentence_type == 'ROT':
                return self._parse_rot(fields)
            elif sentence_type == 'THS':
                return self._parse_ths(fields)
            elif sentence_type == 'RMC':
                return self._parse_rmc(fields)
            elif sentence_type == 'VBW':
                return self._parse_vbw(fields)
            elif sentence_type == 'HSC':
                return self._parse_hsc(fields)  # Heading Steering Command
            else:
                return None
                
        except Exception as e:
            print(f"Error parsing NMEA sentence: {e}")
            return None
    
    def _parse_gga(self, fields: list) -> Optional[Dict[str, Any]]:
        """Parse GGA sentence - Global Positioning System Fix Data."""
        try:
            if len(fields) < 15:
                return None
            
            self.message_counts['GGA'] += 1
            
            # Parse latitude
            lat_str = fields[2]
            lat_dir = fields[3]
            latitude = 0.0
            if lat_str and lat_dir:
                lat_deg = int(lat_str[:2])
                lat_min = float(lat_str[2:])
                latitude = lat_deg + lat_min / 60.0
                if lat_dir == 'S':
                    latitude = -latitude
            
            # Parse longitude
            lon_str = fields[4]
            lon_dir = fields[5]
            longitude = 0.0
            if lon_str and lon_dir:
                lon_deg = int(lon_str[:3])
                lon_min = float(lon_str[3:])
                longitude = lon_deg + lon_min / 60.0
                if lon_dir == 'W':
                    longitude = -longitude
            
            gga_data = {
                'sentence_type': 'GGA',
                'utc_time': fields[1],
                'latitude': latitude,
                'longitude': longitude,
                'quality': int(fields[6]) if fields[6] else 0,
                'num_satellites': int(fields[7]) if fields[7] else 0,
                'hdop': float(fields[8]) if fields[8] else 0.0,
                'altitude': float(fields[9]) if fields[9] else 0.0,
                'altitude_units': fields[10],
                'geoid_height': float(fields[11]) if fields[11] else 0.0,
                'timestamp': time.time()
            }
            
            self.last_parsed_data['GGA'] = gga_data
            return {'GGA': gga_data}
            
        except (ValueError, IndexError) as e:
            print(f"Error parsing GGA sentence: {e}")
            return None
    
    def _parse_vtg(self, fields: list) -> Optional[Dict[str, Any]]:
        """Parse VTG sentence - Track Made Good and Ground Speed."""
        try:
            if len(fields) < 10:
                return None
            
            self.message_counts['VTG'] += 1
            
            vtg_data = {
                'sentence_type': 'VTG',
                'cog_true': float(fields[1]) if fields[1] else 0.0,
                'cog_magnetic': float(fields[3]) if fields[3] else 0.0,
                'sog_knots': float(fields[5]) if fields[5] else 0.0,
                'sog_kmh': float(fields[7]) if fields[7] else 0.0,
                'mode': fields[9] if len(fields) > 9 else '',
                'timestamp': time.time()
            }
            
            self.last_parsed_data['VTG'] = vtg_data
            return {'VTG': vtg_data}
            
        except (ValueError, IndexError) as e:
            print(f"Error parsing VTG sentence: {e}")
            return None
    
    def _parse_hdt(self, fields: list) -> Optional[Dict[str, Any]]:
        """Parse HDT sentence - Heading - True."""
        try:
            if len(fields) < 3:
                return None
            
            self.message_counts['HDT'] += 1
            
            hdt_data = {
                'sentence_type': 'HDT',
                'heading': float(fields[1]) if fields[1] else 0.0,
                'reference': fields[2],  # Should be 'T' for True
                'timestamp': time.time()
            }
            
            self.last_parsed_data['HDT'] = hdt_data
            return {'HDT': hdt_data}
            
        except (ValueError, IndexError) as e:
            print(f"Error parsing HDT sentence: {e}")
            return None
    
    def _parse_rot(self, fields: list) -> Optional[Dict[str, Any]]:
        """Parse ROT sentence - Rate of Turn."""
        try:
            if len(fields) < 3:
                return None
            
            self.message_counts['ROT'] += 1
            
            rot_data = {
                'sentence_type': 'ROT',
                'turn_rate': float(fields[1]) if fields[1] else 0.0,  # deg/min
                'status': fields[2],  # A = valid, V = invalid
                'timestamp': time.time()
            }
            
            self.last_parsed_data['ROT'] = rot_data
            return {'ROT': rot_data}
            
        except (ValueError, IndexError) as e:
            print(f"Error parsing ROT sentence: {e}")
            return None
    
    def _parse_ths(self, fields: list) -> Optional[Dict[str, Any]]:
        """Parse THS sentence - True Heading and Status."""
        try:
            if len(fields) < 3:
                return None
            
            self.message_counts['THS'] += 1
            
            ths_data = {
                'sentence_type': 'THS',
                'heading': float(fields[1]) if fields[1] else 0.0,
                'status': fields[2],  # A = autonomous, E = estimated, M = manual, S = simulator, V = invalid
                'timestamp': time.time()
            }
            
            self.last_parsed_data['THS'] = ths_data
            return {'THS': ths_data}
            
        except (ValueError, IndexError) as e:
            print(f"Error parsing THS sentence: {e}")
            return None
    
    def _parse_rmc(self, fields: list) -> Optional[Dict[str, Any]]:
        """Parse RMC sentence - Recommended Minimum Navigation Information."""
        try:
            if len(fields) < 13:
                return None
            
            self.message_counts['RMC'] += 1
            
            # Parse latitude
            lat_str = fields[3]
            lat_dir = fields[4]
            latitude = 0.0
            if lat_str and lat_dir:
                lat_deg = int(lat_str[:2])
                lat_min = float(lat_str[2:])
                latitude = lat_deg + lat_min / 60.0
                if lat_dir == 'S':
                    latitude = -latitude
            
            # Parse longitude
            lon_str = fields[5]
            lon_dir = fields[6]
            longitude = 0.0
            if lon_str and lon_dir:
                lon_deg = int(lon_str[:3])
                lon_min = float(lon_str[3:])
                longitude = lon_deg + lon_min / 60.0
                if lon_dir == 'W':
                    longitude = -longitude
            
            rmc_data = {
                'sentence_type': 'RMC',
                'utc_time': fields[1],
                'status': fields[2],  # A = valid, V = invalid
                'latitude': latitude,
                'longitude': longitude,
                'sog_knots': float(fields[7]) if fields[7] else 0.0,
                'cog_true': float(fields[8]) if fields[8] else 0.0,
                'date': fields[9],
                'magnetic_variation': float(fields[10]) if fields[10] else 0.0,
                'timestamp': time.time()
            }
            
            self.last_parsed_data['RMC'] = rmc_data
            return {'RMC': rmc_data}
            
        except (ValueError, IndexError) as e:
            print(f"Error parsing RMC sentence: {e}")
            return None

    def _parse_vbw(self, fields: list) -> Optional[Dict[str, Any]]:
        """
        Parse VBW sentence - Dual Ground/Water Speed.

        VBW format: $--VBW,x.x,x.x,A,x.x,x.x,A,x.x,A,x.x,A*hh
        Fields:
        0: $--VBW
        1: Longitudinal water speed, knots
        2: Transverse water speed, knots
        3: Status, water speed, A = data valid
        4: Longitudinal ground speed, knots
        5: Transverse ground speed, knots
        6: Status, ground speed, A = data valid
        7: Stern transverse water speed, knots
        8: Status, stern transverse water speed, A = data valid
        9: Stern transverse ground speed, knots
        10: Status, stern transverse ground speed, A = data valid
        """
        try:
            if len(fields) < 11:
                return None

            self.message_counts['VBW'] += 1

            # Parse water speeds
            longitudinal_water_speed = float(fields[1]) if fields[1] else 0.0
            transverse_water_speed = float(fields[2]) if fields[2] else 0.0
            water_speed_status = fields[3] if fields[3] else 'V'

            # Parse ground speeds
            longitudinal_ground_speed = float(fields[4]) if fields[4] else 0.0
            transverse_ground_speed = float(fields[5]) if fields[5] else 0.0
            ground_speed_status = fields[6] if fields[6] else 'V'

            # Parse stern speeds (optional)
            stern_transverse_water_speed = float(fields[7]) if fields[7] else 0.0
            stern_water_status = fields[8] if fields[8] else 'V'
            stern_transverse_ground_speed = float(fields[9]) if fields[9] else 0.0
            stern_ground_status = fields[10] if fields[10] else 'V'

            parsed_data = {
                'type': 'VBW',
                'timestamp': time.time(),
                'longitudinal_water_speed': longitudinal_water_speed,
                'transverse_water_speed': transverse_water_speed,
                'water_speed_status': water_speed_status,
                'longitudinal_ground_speed': longitudinal_ground_speed,
                'transverse_ground_speed': transverse_ground_speed,
                'ground_speed_status': ground_speed_status,
                'stern_transverse_water_speed': stern_transverse_water_speed,
                'stern_water_status': stern_water_status,
                'stern_transverse_ground_speed': stern_transverse_ground_speed,
                'stern_ground_status': stern_ground_status,
                'water_speed_valid': water_speed_status == 'A',
                'ground_speed_valid': ground_speed_status == 'A'
            }

            self.last_parsed_data['VBW'] = parsed_data
            return parsed_data

        except (ValueError, IndexError) as e:
            print(f"Error parsing VBW sentence: {e}")
            return None

    def _parse_hsc(self, fields: list) -> Optional[Dict[str, Any]]:
        """Parse HSC sentence - Heading Steering Command."""
        try:
            if len(fields) < 3:
                return None

            self.message_counts['HSC'] = self.message_counts.get('HSC', 0) + 1

            hsc_data = {
                'sentence_type': 'HSC',
                'heading': float(fields[1]) if fields[1] else 0.0,
                'reference': fields[2],  # T = True, M = Magnetic
                'timestamp': time.time()
            }

            self.last_parsed_data['HSC'] = hsc_data
            return {'HSC': hsc_data}

        except (ValueError, IndexError) as e:
            print(f"Error parsing HSC sentence: {e}")
            return None

    def get_message_counts(self) -> Dict[str, int]:
        """Get message counts for each sentence type."""
        return self.message_counts.copy()
    
    def get_last_parsed_data(self) -> Dict[str, Any]:
        """Get the last parsed data for each sentence type."""
        return self.last_parsed_data.copy()
    
    def reset_parser(self) -> None:
        """Reset parser state."""
        self.message_counts = {key: 0 for key in self.message_counts}
        self.last_parsed_data = {}
