"""
HTC/PAVK message parser for autopilot simulator.
Parses incoming HTC (Heading/Turn Control) and PAVK (Navigation Status) messages.
"""

import json
import re
import time
from typing import Dict, Optional, Any
from dataclasses import dataclass

@dataclass
class HTCData:
    """
    Data class for parsed HTC (Heading/Turn Control) message data.

    HTC message contains autopilot control commands:
    - Turn direction (L/R)
    - Steering mode (M/S/H)
    - Turn mode (N/R/T)
    - Control commands
    - Limits
    """
    turn_dir: str = "R"            # Turn direction: L(Left), R(Right)
    steering_mode: str = "M"       # Steering mode: M(Manual), S(Stand-by), H(Heading)
    turn_mode: str = "N"           # Turn mode: N(Normal), R(Rate), T(Tactical)
    radot_cmd: float = 0.0         # Radius of turn command [n.miles]
    rot_cmd: float = 0.0           # Rate of turn command [deg/min]
    hdg_cmd: float = 0.0           # Heading command [deg]
    rudder_limit: float = 35.0     # Rudder limit [deg] from HTC
    turn_limit: float = 60.0       # Turn rate limit [deg/min] from HTC
    timestamp: str = ""            # Message timestamp

@dataclass
class PAVKData:
    """
    Data class for parsed PAVK (Navigation Status) message data.

    PAVK message contains navigation system status:
    - NAS status (A: normal, V: abnormal)
    """
    nas_status: str = "A"          # Navigation status: A(normal), V(abnormal)
    nas_ready: bool = True         # Navigation system ready flag
    timestamp: str = ""            # Message timestamp

@dataclass
class XTEData:
    """
    Data class for parsed XTE (Cross Track Error) message data.

    XTE message contains cross track error information:
    - Status (A: data valid, V: data invalid)
    - Cross track error distance and direction
    """
    status_1: str = "A"            # Status 1: A(data valid), V(data invalid)
    status_2: str = "A"            # Status 2: A(data valid), V(data invalid)
    cross_track_error: float = 0.0 # Cross track error distance [nautical miles]
    direction: str = "L"           # Direction: L(Left), R(Right)
    units: str = "N"               # Units: N(nautical miles)
    arrival_status: str = "A"      # Arrival circle entered: A(entered), V(not entered)
    timestamp: str = ""            # Message timestamp

class HTCPAVKParser:
    """
    Parser for HTC, PAVK, and XTE messages.

    Handles parsing of:
    - HTC (Heading/Turn Control) messages
    - PAVK (Navigation Status) messages
    - XTE (Cross Track Error) messages
    and extracts relevant control commands for autopilot.
    """

    def __init__(self):
        """Initialize HTC/PAVK/XTE parser."""
        self.last_htc_data = HTCData()
        self.last_pavk_data = PAVKData()
        self.last_xte_data = XTEData()
        self.htc_message_count = 0
        self.pavk_message_count = 0
        self.xte_message_count = 0

        # Data persistence for HTC (5틱 유지)
        self.htc_hold_ticks = 5
        self.last_valid_htc_data = None
        self.ticks_since_last_htc = 0

    def parse_htc_message(self, message: str) -> Optional[HTCData]:
        """
        Parse HTC message and extract control commands.

        Args:
            message: Raw HTC message string

        Returns:
            HTCData object with parsed data, or None if parsing fails
        """
        try:
            self.htc_message_count += 1

            # Parse NMEA format HTC/HTD message
            if message.startswith('$') and ('HTC' in message or 'HTD' in message):
                htc_data = self._parse_htc_nmea_message(message)
                if htc_data:
                    # Store as last valid data and reset tick counter
                    self.last_valid_htc_data = htc_data
                    self.ticks_since_last_htc = 0
                return htc_data

            else:
                print(f"Unknown HTC/HTD message format: {message[:50]}...")
                return None

        except Exception as e:
            print(f"Error parsing HTC message: {e}")
            return None

    def get_htc_data_with_hold(self) -> Optional[HTCData]:
        """
        Get HTC data with hold logic. Returns previous valid data for up to 5 ticks.

        Returns:
            HTCData object or None if no valid data available
        """
        self.ticks_since_last_htc += 1

        if self.last_valid_htc_data is not None and self.ticks_since_last_htc <= self.htc_hold_ticks:
            # Hold previous valid data for specified ticks
            return self.last_valid_htc_data
        elif self.ticks_since_last_htc > self.htc_hold_ticks:
            # Exceeded hold time - return None (invalid)
            return None
        else:
            # No previous data
            return None

    def parse_pavk_message(self, message: str) -> Optional[PAVKData]:
        """
        Parse PAVK message and extract navigation status.

        Args:
            message: Raw PAVK message string

        Returns:
            PAVKData object with parsed data, or None if parsing fails
        """
        try:
            self.pavk_message_count += 1

            # Parse NMEA format PAVK message
            if message.startswith('$') and 'PAVK' in message:
                return self._parse_pavk_nmea_message(message)

            else:
                print(f"Unknown PAVK message format: {message[:50]}...")
                return None

        except Exception as e:
            print(f"Error parsing PAVK message: {e}")
            return None

    def _parse_htc_nmea_message(self, message: str) -> Optional[HTCData]:
        """Parse NMEA format HTC message."""
        try:
            # Remove $ and checksum if present
            clean_message = message.strip()
            if clean_message.startswith('$'):
                clean_message = clean_message[1:]
            if '*' in clean_message:
                clean_message = clean_message.split('*')[0]

            parts = clean_message.split(',')

            if len(parts) < 11:
                print(f"Insufficient data in HTC message: {len(parts)} fields")
                return None

            htc_data = HTCData()

            # Parse HTC message format: $AGHTC,V,rudder_cmd,rudder_dir,steering_mode,turn_mode,rudder_limit,turn_limit,radot_cmd,rot_cmd,hdg_cmd,...
            if len(parts) >= 11:
                htc_data.turn_dir = parts[3] if parts[3] else "R"  # L or R
                htc_data.steering_mode = parts[4] if parts[4] else "M"  # M, S, H
                htc_data.turn_mode = parts[5] if parts[5] else "N"  # N, R, T mode from HTC
                htc_data.rudder_limit = float(parts[6]) if parts[6] else 35.0  # rudder limit [deg]
                htc_data.turn_limit = float(parts[7]) if parts[7] else 60.0  # turn rate limit [deg/min]
                htc_data.radot_cmd = float(parts[8]) if parts[8] else 0.0  # radius of turn cmd [n.miles]
                htc_data.rot_cmd = float(parts[9]) if parts[9] else 0.0  # rate of turn cmd [deg/min]
                htc_data.hdg_cmd = float(parts[10]) if parts[10] else 0.0  # heading cmd [deg]
                htc_data.timestamp = str(time.time())

            self.last_htc_data = htc_data
            return htc_data

        except (ValueError, IndexError) as e:
            print(f"Error parsing HTC NMEA message: {e}")
            return None

    def _parse_pavk_nmea_message(self, message: str) -> Optional[PAVKData]:
        """Parse NMEA format PAVK message."""
        try:
            # Remove $ and checksum if present
            clean_message = message.strip()
            if clean_message.startswith('$'):
                clean_message = clean_message[1:]
            if '*' in clean_message:
                clean_message = clean_message.split('*')[0]

            parts = clean_message.split(',')

            if len(parts) < 2:
                print(f"Insufficient data in PAVK message: {len(parts)} fields")
                return None

            pavk_data = PAVKData()

            # Parse PAVK message format: $PAVK,NAS,status,... or $PAVK,status,...
            if len(parts) >= 3 and parts[1] == "NAS":
                # Format: $PAVK,NAS,A,,,*3D
                pavk_data.nas_status = parts[2] if parts[2] else "V"  # A: normal, V: abnormal
            elif len(parts) >= 2:
                # Format: $PAVK,A,,,*3D (without NAS)
                pavk_data.nas_status = parts[1] if parts[1] else "V"  # A: normal, V: abnormal
            else:
                pavk_data.nas_status = "V"  # Default to invalid

            pavk_data.nas_ready = (pavk_data.nas_status == "A")
            pavk_data.timestamp = str(time.time())

            self.last_pavk_data = pavk_data
            return pavk_data

        except (ValueError, IndexError) as e:
            print(f"Error parsing PAVK NMEA message: {e}")
            return None

    def parse_xte_message(self, message: str) -> Optional[XTEData]:
        """
        Parse XTE (Cross Track Error) message.

        XTE format: $--XTE,A,A,x.x,a,N,A*hh
        Fields:
        0: $--XTE
        1: Status 1, A = data valid, V = data invalid
        2: Status 2, A = data valid, V = data invalid
        3: Cross track error distance
        4: Direction to steer, L or R
        5: Units, N = nautical miles
        6: Arrival circle entered, A = entered, V = not entered

        Args:
            message: Raw XTE message string

        Returns:
            XTEData object with parsed data, or None if parsing fails
        """
        try:
            self.xte_message_count += 1

            # Remove checksum if present
            if '*' in message:
                message = message.split('*')[0]

            # Split into fields
            parts = message.split(',')
            if len(parts) < 7:
                return None

            # Create XTE data object
            xte_data = XTEData()

            # Parse XTE fields
            xte_data.status_1 = parts[1] if parts[1] else "V"
            xte_data.status_2 = parts[2] if parts[2] else "V"
            xte_data.cross_track_error = float(parts[3]) if parts[3] else 0.0
            xte_data.direction = parts[4] if parts[4] else "L"
            xte_data.units = parts[5] if parts[5] else "N"
            xte_data.arrival_status = parts[6] if parts[6] else "V"
            xte_data.timestamp = str(time.time())

            self.last_xte_data = xte_data
            return xte_data

        except (ValueError, IndexError) as e:
            print(f"Error parsing XTE NMEA message: {e}")
            return None

    def get_last_htc_data(self) -> HTCData:
        """Get the last successfully parsed HTC data."""
        return self.last_htc_data

    def get_last_pavk_data(self) -> PAVKData:
        """Get the last successfully parsed PAVK data."""
        return self.last_pavk_data

    def get_last_xte_data(self) -> XTEData:
        """Get the last successfully parsed XTE data."""
        return self.last_xte_data

    def get_htc_message_count(self) -> int:
        """Get the total number of HTC messages processed."""
        return self.htc_message_count

    def get_pavk_message_count(self) -> int:
        """Get the total number of PAVK messages processed."""
        return self.pavk_message_count

    def get_xte_message_count(self) -> int:
        """Get the total number of XTE messages processed."""
        return self.xte_message_count

    def reset_parser(self) -> None:
        """Reset parser state."""
        self.last_htc_data = HTCData()
        self.last_pavk_data = PAVKData()
        self.last_xte_data = XTEData()
        self.htc_message_count = 0
        self.pavk_message_count = 0
        self.xte_message_count = 0
