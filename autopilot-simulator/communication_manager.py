"""
Communication manager for autopilot simulator.
Handles UDP socket communication for:
1. Receiving HTC/PAVK messages from ship simulator
2. Receiving NMEA sentences (GGA, VTG, HDT, etc.) from ship simulator
3. Sending autopilot output sentences (RSA, ROR, TXT, TKM, TKI)
"""

import asyncio
import json
import os
from typing import Dict, Optional
from utils.udp_socket_base import UDPSocketBase

class CommunicationManager:
    """
    Manages UDP network communication for the autopilot simulator.

    Handles:
    - HTC/PAVK message reception from ship simulator
    - NMEA sentence reception from ship simulator (GGA, VTG, HDT, etc.)
    - Autopilot output sentence transmission to ship simulator (RSA, ROR, TXT, TKM, TKI)
    """
    
    def __init__(self, config_file: str = None):
        """
        Initialize communication manager.
        
        Args:
            config_file: Path to communication configuration file
        """
        # Load configuration
        self.config_file = config_file or "config/autopilot_communication.json"
        self.config = self._load_config(self.config_file)
        
        # Extract configuration
        hinas_server_config = self.config.get("hinas_server", {})
        nmea_config = self.config.get("nmea_receiver", {})

        # main socket configuration
        self.hinas_server_ip = hinas_server_config.get("ip", "***********")
        self.hinas_server_port = hinas_server_config.get("port", 4001)

        # NMEA receiver configuration (unicast from ship simulator)
        self.nmea_receiver_ip = nmea_config.get("ip", "***********")
        self.nmea_receiver_port = nmea_config.get("port", 50000)


        # Socket objects
        self.server_socket = None
        self.nmea_receiver = None
        self.output_sender = None


        # Statistics
        self.hinas_messages_received = 0
        self.server_messages_received = 0  # Alias for compatibility
        self.nmea_messages_received = 0
        self.output_messages_sent = 0
        self._sample_shown = set()

        print(f"Autopilot Communication Manager initialized")
        print(f"  Config File: {self.config_file}")
        print(f"  HINAS Server: {self.hinas_server_ip}:{self.hinas_server_port}")
        print(f"  NMEA Receiver: {self.nmea_receiver_ip}:{self.nmea_receiver_port}")

    def _load_config(self, config_file: str) -> dict:
        """Load communication configuration from JSON file."""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ Config file {config_file} not found, using defaults")
            return {}
        except json.JSONDecodeError as e:
            print(f"⚠️ Error parsing {config_file}: {e}, using defaults")
            return {}


    def setup_communication(self) -> None:
        """Setup UDP sockets for message reception and transmission."""
        # Socket for HTC, PAVK messages (unicast) and sending autopilot output
        self.server_socket = UDPSocketBase(
            local_ip="0.0.0.0",  # Bind to all interfaces
            local_port=self.hinas_server_port,
            remote_ip=self.hinas_server_ip,
            remote_port=self.hinas_server_port,
            is_receiver=True,
            is_sender=True,
            is_auto_set=False  # Manual setup to ensure 0.0.0.0 binding
        )
        # Manually setup the socket to ensure proper binding
        self.server_socket.setup_socket()
        # Socket for receiving NMEA messages (unicast)
        try:
            # Try to bind to specific IP first
            self.nmea_receiver = UDPSocketBase(
                local_ip=self.nmea_receiver_ip,
                local_port=self.nmea_receiver_port,
                is_receiver=True,
                is_sender=False,
                is_auto_set=True
            )
            print(f"✅ NMEA receiver setup: {self.nmea_receiver_ip}:{self.nmea_receiver_port}")
        except Exception as e:
            print(f"❌ NMEA receiver setup failed on {self.nmea_receiver_ip}: {e}")
            try:
                # Fallback: bind to all interfaces (0.0.0.0)
                print(f"🔄 Trying fallback: binding to 0.0.0.0:{self.nmea_receiver_port}")
                self.nmea_receiver = UDPSocketBase(
                    local_ip="0.0.0.0",
                    local_port=self.nmea_receiver_port,
                    is_receiver=True,
                    is_sender=False,
                    is_auto_set=True
                )
                print(f"✅ NMEA receiver fallback setup: 0.0.0.0:{self.nmea_receiver_port}")
            except Exception as e2:
                print(f"❌ NMEA receiver fallback also failed: {e2}")
                self.nmea_receiver = None

        print("✅ Autopilot communication setup complete")

    async def receive_nmea_message(self) -> Optional[str]:
        """
        Receive NMEA message from ship simulator (non-blocking).
        Drains buffer to get the latest message to reduce latency.

        Returns:
            Latest NMEA message string, or None if no message available
        """
        if not self.nmea_receiver:
            # Show this error only once
            if not hasattr(self, '_nmea_error_shown'):
                print("❌ NMEA receiver not initialized - no NMEA data will be received")
                self._nmea_error_shown = True
            return None

        try:
            # Non-blocking receive with reasonable timeout
            data = await asyncio.wait_for(
                self.nmea_receiver.receive_data(),
                timeout=0.005  # 5ms timeout for good responsiveness
            )

            if data:
                message = data.decode('utf-8').strip()
                self.nmea_messages_received += 1

                # Show sample NMEA message (once)
                if "nmea_sample" not in self._sample_shown:
                    print(f"📡 NMEA Sample: {message[:100]}...")
                    self._sample_shown.add("nmea_sample")

                return message
            else:
                return None

        except asyncio.TimeoutError:
            # Normal timeout - no message available
            return None
        except Exception as e:
            # Only show error occasionally to avoid spam
            if not hasattr(self, '_nmea_error_count'):
                self._nmea_error_count = 0
            self._nmea_error_count += 1

            if self._nmea_error_count % 100 == 1:  # Show every 100th error
                print(f"❌ Error receiving NMEA message (#{self._nmea_error_count}): {type(e).__name__}: {e}")

        return None

    async def receive_autopilot_command_message(self) -> Optional[str]:
        """
        Receive HTC, PAVK message from ship simulator (non-blocking).

        Returns:
            HTC, PAVK message string, or None if no message available
        """
        if not self.server_socket:
            raise RuntimeError("HTC receiver not initialized")

        try:
            # Non-blocking receive with timeout
            data = await asyncio.wait_for(
                self.server_socket.receive_data(),
                timeout=0.001  # 1ms timeout for non-blocking behavior
            )

            if data:
                message = data.decode('utf-8').strip()
                self.hinas_messages_received += 1
                self.server_messages_received += 1  # For compatibility

                # Show sample HTC message (once)
                if "hinas_sample" not in self._sample_shown:
                    print(f"📡 HTC Sample: {message[:100]}...")
                    self._sample_shown.add("hinas_sample")

                return message

        except asyncio.TimeoutError:
            # No message available (normal for non-blocking)
            pass
        except Exception as e:
            print(f"❌ Error receiving HTC message: {e}")

        return None


    def send_autopilot_sentences(self, nmea_sentences: Dict[str, str]) -> None:
        """
        Send autopilot NMEA sentences to ship simulator.

        Args:
            nmea_sentences: Dictionary of NMEA sentence types and their content
        """
        if not self.server_socket:
            raise RuntimeError("Output sender not initialized")

        try:
            # Send all NMEA sentences
            for sentence_key, sentence in nmea_sentences.items():
                self.server_socket.send_data(sentence.encode('utf-8'))
                self.output_messages_sent += 1

                # Show sample output sentence (once per sentence type)
                sample_key = f"{sentence_key}_sample"
                if sample_key not in self._sample_shown:
                    print(f"{sentence_key} Sample: {sentence.strip()}")
                    self._sample_shown.add(sample_key)

        except Exception as e:
            print(f"Error sending autopilot sentences: {e}")

    def send_single_sentence(self, sentence: str) -> None:
        """
        Send a single NMEA sentence to ship simulator.

        Args:
            sentence: NMEA sentence string to send
        """
        if not self.server_socket:
            raise RuntimeError("Output sender not initialized")

        try:
            self.server_socket.send_data(sentence.encode('utf-8'))
            self.output_messages_sent += 1

        except Exception as e:
            print(f"❌ Error sending single sentence: {e}")

    def get_connection_info(self) -> Dict[str, any]:
        """
        Get connection information and statistics.

        Returns:
            Dictionary containing connection info
        """
        return {
            "hinas_server": {
                "ip": self.hinas_server_ip,
                "port": self.hinas_server_port,
                "messages_received": self.hinas_messages_received
            },
            "nmea_receiver": {
                "ip": self.nmea_receiver_ip,
                "port": self.nmea_receiver_port,
                "messages_received": self.nmea_messages_received
            }
        }

    def close_communication(self) -> None:
        """Close all communication sockets."""
        if self.server_socket:
            self.server_socket.close()
            self.server_socket = None

        if self.nmea_receiver:
            self.nmea_receiver.close()
            self.nmea_receiver = None

        print("🔌 Autopilot communication closed")
