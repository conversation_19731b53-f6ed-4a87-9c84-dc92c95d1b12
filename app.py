from fastapi import FastAPI, Request, Form, UploadFile, File, HTTPException, status
from fastapi.responses import (
    HTMLResponse,
    JSONResponse,
    RedirectResponse,
    PlainTextResponse,
    Response,
)
from fastapi.middleware.cors import CORSMiddleware
from starlette.requests import Request

from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import docker, os, subprocess, re
import requests
import logging
import psutil
import json
from datetime import datetime

# 환경변수로 제품 버전 및 기본 이미지 설정
APP_VERSION = os.getenv("APP_VERSION", "v1.0.0")
DEFAULT_ECDIS_IMAGE = os.getenv(
    "DEFAULT_ECDIS_IMAGE",
    "479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/nas2-ecdis:v0.0.15-furuno",
)
DEFAULT_REDIS_IMAGE = os.getenv(
    "DEFAULT_REDIS_IMAGE",
    "479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/library:redis-7.2.7-alpine",
)
DEFAULT_SHIP_SIMULATOR_IMAGE = os.getenv(
    "DEFAULT_SHIP_SIMULATOR_IMAGE", "ship-simulator:v0.1"
)
DEFAULT_AUTOPILOT_SERVER_IMAGE = os.getenv(
    "DEFAULT_AUTOPILOT_SERVER_IMAGE", "autopilot-simulator:v0.0.1"
)
DEFAULT_AUTOPILOT_UI_IMAGE = os.getenv(
    "DEFAULT_AUTOPILOT_UI_IMAGE", "autopilot-ui:v0.4"
)

app = FastAPI()

logging = logging.getLogger("uvicorn.access")
bms_process = None
bms_config = {}


# 템플릿 디렉토리 설정 (templates 폴더에 HTML 파일들이 있음)
templates = Jinja2Templates(directory="templates")
legacy_templates = Jinja2Templates(directory="templates/legacy")

# (필요하다면 정적파일 경로도 mount 가능)
app.mount("/static", StaticFiles(directory="static"), name="static")

# Docker 클라이언트 초기화
client = docker.DockerClient(
    base_url=os.getenv("DOCKER_CLIENT_PATH", "unix:///var/run/docker.sock")
)

# 컨테이너셋 정의 (불필요한 flash 관련 코드는 제거)
container_sets = {
    "ECDIS": {
        "type": "group",
        "containers": [
            {
                "container_name": "control-core-ecdis",
                "default_image": os.getenv("ECDIS_IMAGE", DEFAULT_ECDIS_IMAGE),
                "env": {},
                "command": ["python3", "main.py"],
                "user": "root",
            },
            {
                "container_name": "redis",
                "default_image": os.getenv("REDIS_IMAGE", DEFAULT_REDIS_IMAGE),
                "command": 'sh -c "redis-server --daemonize yes && sleep 2 && redis-cli set route \'{"route_name": null, "route": null}\' && tail -f /dev/null"',
            },
        ],
        "ports": {},
    },
    "Autopilot Simulator": {
        "type": "group",
        "containers": [
            {
                "container_name": "autopilot-simulator",
                "default_image": os.getenv(
                    "AUTOPILOT_SIMULATOR_SERVER_IMAGE", DEFAULT_AUTOPILOT_SERVER_IMAGE
                ),
                "env": {},
                "ports": {},
            },
            {
                "container_name": "autopilot-ui",
                "default_image": os.getenv(
                    "AUTOPILOT_SIMULATOR_UI_IMAGE", DEFAULT_AUTOPILOT_UI_IMAGE
                ),
                "env": {
                    "REDIS_HOST": "localhost",
                    "REDIS_PORT": "6379",
                    "REDIS_ENABLE_SSL": "false",
                    "DISPLAY": ":0",
                    "QT_X11_NO_MITSHM": "1",
                    "LIBGL_ALWAYS_SOFTWARE": "1",
                    "LIBGL_DRI3_DISABLE": "1",
                    "XDG_RUNTIME_DIR": "/tmp",
                },
                "volumes": [
                    "/tmp/.X11-unix:/tmp/.X11-unix:rw",
                    "/etc/localtime:/etc/localtime:ro",
                ],
                "ports": {},
            },
        ],
        "ports": {},
    },
    "Ship Simulator": {
        "type": "single",
        "container_name": "ship-simulator",
        "default_image": os.getenv(
            "SHIP_SIMULATOR_IMAGE", DEFAULT_SHIP_SIMULATOR_IMAGE
        ),
        "env": {"PYTHONPATH": "/app"},
        "ports": {},
    },
    "BMS": {
        "type": "local",
        "container_name": "BMS_MOCK",
        "default_image": os.getenv("BMS_IMAGE", None),
        "env": {},
        "ports": {},
    },
    "AIS VDM Simulator": {
        "type": "single",
        "container_name": "ais-vdm-simulator",
        "default_image": "ais-vdm:1.0.0",
        "env": {
            "REDIS_HOST": "localhost",
            "REDIS_PORT": "6379",
            "REDIS_PASSWORD": "",
            "REDIS_ENABLE_SSL": "0",
            "TARGET_HOST": "***********",
            "TARGET_PORT": "6503",
            "SEND_PERIOD": "2",
        },
        "ports": {"8000/tcp": 8000},
    },
}


# Autopilot Simulator 설정 파일 관리 함수들
def get_autopilot_config_defaults():
    """Autopilot Simulator 설정 파일의 기본값을 반환"""
    try:
        # 컨테이너 내부에서 실행되는지 확인 (WORKDIR이 /app인 경우)
        if os.getcwd() == "/app":
            # 컨테이너 내부 경로
            comm_path = "/app/autopilot-simulator/config/autopilot_communication.json"
            config_path = "/app/autopilot-simulator/config/autopilot_config.json"
        else:
            # 호스트 경로
            comm_path = "autopilot-simulator/config/autopilot_communication.json"
            config_path = "autopilot-simulator/config/autopilot_config.json"

        # Communication 설정 읽기
        with open(comm_path, "r") as f:
            comm_config = json.load(f)

        # Config 설정 읽기
        with open(config_path, "r") as f:
            autopilot_config = json.load(f)

        return comm_config, autopilot_config
    except Exception as e:
        print(f"설정 파일 읽기 오류: {e}")
        # 기본값 반환
        comm_config = {
            "hinas_server": {"ip": "***********", "port": 4001},
            "nmea_receiver": {"ip": "***********", "port": 50000},
        }
        autopilot_config = {
            "control_parameters": {
                "kp_psi": 3.0,
                "kd_psi": 60.0,
                "kp_rot": 0,
                "ki_rot": 2800,
                "kff_rot": 1.0,
                "rudder_limit": 35,
            },
            "ship_parameters": {
                "ship_type": 2,
                "kff_rot_container_negative": 1.0,
                "kff_rot_container_positive": 0.63,
            },
        }
        return comm_config, autopilot_config


# Ship Simulator 설정 파일 관리 함수들
def get_ship_simulator_config_defaults():
    """Ship Simulator 설정 파일의 기본값을 반환"""
    try:
        # 컨테이너 내부에서 실행되는지 확인
        if os.getcwd() == "/app":
            # 컨테이너 내부 경로
            comm_path = "/app/ship-simulator/config/communication.json"
            nmea_path = "/app/ship-simulator/config/nmea_talkers.json"
            sim_path = "/app/ship-simulator/config/simulation_config.json"
        else:
            # 호스트 경로
            comm_path = "ship-simulator/config/communication.json"
            nmea_path = "ship-simulator/config/nmea_talkers.json"
            sim_path = "ship-simulator/config/simulation_config.json"

        # Communication 설정 읽기
        with open(comm_path, "r") as f:
            comm_config = json.load(f)

        # NMEA Talkers 설정 읽기
        with open(nmea_path, "r") as f:
            nmea_config = json.load(f)

        # Simulation Config 설정 읽기
        with open(sim_path, "r") as f:
            sim_config = json.load(f)

        return comm_config, nmea_config, sim_config
    except Exception as e:
        print(f"Ship Simulator 설정 파일 읽기 오류: {e}")
        # 기본값 반환
        comm_config = {
            "communication_mode": "multicast",
            "multicast": {
                "group_ip": "***********",
                "port": 60001,
                "ttl": 1,
                "interface_ip": "***********",
            },
            "unicast": {"target_ip": "***********", "port": 6501},
            "simulator_server": {"ip": "***********", "port": 59902},
            "autopilot_unicast": {
                "target_ip": "***********",
                "port": 50000,
                "messages": ["GGA", "VTG", "HDT", "ROT", "VBW"],
            },
        }
        nmea_config = {
            "nmea_version": "0450",
            "default_source": "EI0001",
            "talkers": {},
        }
        sim_config = {
            "simulation_set": {
                "ship_type": 2,
                "simulation_speed_up": 1,
                "simulation_mode": "normal",
            },
            "initial": {
                "position": "1°15.935'N 103°57.411'E",
                "speed": 20.0,
                "course": 110.8,
                "lever": 1.0,
            },
            "environment": {
                "wind": {"speed": 5.0, "direction": 10.0, "enabled": True},
                "current": {"speed": 0.2, "direction": 0.3, "enabled": True},
                "wave": {"sea_state": 1, "enabled": True},
            },
        }
        return comm_config, nmea_config, sim_config


def create_ship_simulator_config_files(comm_data, nmea_data, sim_data):
    """Ship Simulator 설정 파일들을 생성 (호스트에서만)"""
    try:
        # 호스트에서만 설정 파일 생성
        # if os.getcwd() == "/app":
        #     print("컨테이너 내부에서는 설정 파일을 생성하지 않습니다.")
        #     return True

        # config 디렉토리 생성
        os.makedirs("ship-simulator/config", exist_ok=True)

        # Communication 설정 파일 생성
        comm_path = "ship-simulator/config/communication.json"
        with open(comm_path, "w", encoding="utf-8") as f:
            json.dump(comm_data, f, indent=2, ensure_ascii=False)

        # NMEA Talkers 설정 파일 생성
        nmea_path = "ship-simulator/config/nmea_talkers.json"
        with open(nmea_path, "w", encoding="utf-8") as f:
            json.dump(nmea_data, f, indent=2, ensure_ascii=False)

        # Simulation Config 설정 파일 생성
        sim_path = "ship-simulator/config/simulation_config.json"
        with open(sim_path, "w", encoding="utf-8") as f:
            json.dump(sim_data, f, indent=2, ensure_ascii=False)

        return True
    except Exception as e:
        print(f"Ship Simulator 설정 파일 생성 오류: {e}")
        return False


def create_autopilot_config_files(comm_data, config_data):
    """Autopilot Simulator 설정 파일들을 생성 (호스트에서만)"""
    try:
        # 호스트에서만 설정 파일 생성 (컨테이너 내부에서는 생성하지 않음)
        # if os.getcwd() == "/app":
        #     print("컨테이너 내부에서는 설정 파일을 생성하지 않습니다.")
        #     return True

        # config 디렉토리 생성
        os.makedirs("autopilot-simulator/config", exist_ok=True)

        # Communication 설정 파일 생성
        comm_path = "autopilot-simulator/config/autopilot_communication.json"
        with open(comm_path, "w") as f:
            json.dump(comm_data, f, indent=2)

        # Config 설정 파일 생성
        config_path = "autopilot-simulator/config/autopilot_config.json"
        with open(config_path, "w") as f:
            json.dump(config_data, f, indent=2)

        return True
    except Exception as e:
        print(f"설정 파일 생성 오류: {e}")
        return False


def get_valid_image_tags(tags):
    valid = []
    for tag in tags:
        try:
            # tag로 이미지를 조회해서 존재하면 valid 리스트에 추가
            client.images.get(tag)
            valid.append(tag)
        except (docker.errors.NotFound, docker.errors.NullResource):
            continue
    return valid


# --- 유틸리티 함수 ---
def get_container_status(cfg):
    ports = cfg.get("ports", {})
    if cfg["type"] == "single":
        container_name = cfg["container_name"]
        default_image = cfg.get("default_image")
        valid_tags = []
        env_vars = {}
        try:
            container = client.containers.get(container_name)
            container.reload()  # 최신 정보 업데이트
            status = container.status.lower().strip()  # 소문자와 공백 제거
            # 실행 중일 때는 실제 사용된 이미지 태그만 표시
            if status == "running":
                # 컨테이너 생성 시 사용된 실제 이미지 태그
                actual_image = container.attrs["Config"]["Image"]
                valid_tags = [actual_image] if actual_image else ["<unknown>"]
            else:
                # 중지 상태일 때는 사용 가능한 모든 이미지 표시
                tags = (
                    container.image.tags
                    if container.image and container.image.tags
                    else []
                )
                valid_tags = get_valid_image_tags(tags)
            # 상태가 "running"이면 실제 환경변수를 조회
            if status == "running":
                env_list = container.attrs["Config"].get("Env", [])
                for item in env_list:
                    if "=" in item:
                        k, v = item.split("=", 1)
                        env_vars[k] = v
            else:
                env_vars = {}
            return status, valid_tags, ports, env_vars
        except docker.errors.NotFound:
            if default_image:
                valid_tags = get_valid_image_tags([default_image])
            return None, valid_tags, ports, {}
    elif cfg["type"] == "group":
        statuses = {}
        image_tags = {}
        env_vars = {}
        for c in cfg["containers"]:
            container_name = c["container_name"]
            default_image = c.get("default_image")
            valid_tag = None
            try:
                container = client.containers.get(container_name)
                container.reload()  # 최신 정보 업데이트
                status = container.status.lower().strip()
                statuses[container_name] = status
                tags = (
                    container.image.tags
                    if container.image and container.image.tags
                    else []
                )
                valid_tags_list = get_valid_image_tags(tags)
                if valid_tags_list:
                    valid_tag = valid_tags_list[0]
                # 실행 중일 때만 각 컨테이너의 환경변수 조회
                if status == "running":
                    env_list = container.attrs["Config"].get("Env", [])
                    env_dict = {}
                    for item in env_list:
                        if "=" in item:
                            k, v = item.split("=", 1)
                            env_dict[k] = v
                    env_vars[container_name] = env_dict
                else:
                    env_vars[container_name] = {}
            except docker.errors.NotFound:
                statuses[container_name] = "Not Created"
                if default_image:
                    valid_tags_list = get_valid_image_tags([default_image])
                    if valid_tags_list:
                        valid_tag = valid_tags_list[0]
                env_vars[container_name] = {}
            image_tags[container_name] = valid_tag
        overall = (
            "running"
            if all(s == "running" for s in statuses.values() if s != "Not Created")
            and len([s for s in statuses.values() if s == "running"]) > 0
            else "Not Created"
        )
        # Autopilot Simulator의 경우 개별 컨테이너 상태를 반환
        return statuses, image_tags, ports, env_vars
    return None, [], {}, {}


def get_all_image_tags():
    images = client.images.list()
    tags = set()
    for img in images:
        for t in img.tags:
            tags.add(t)
    return sorted(list(tags))


def ip_exists(ip_to_check):
    try:
        output = subprocess.check_output(["ip", "addr"]).decode("utf-8")
        ip_list = re.findall(r"inet ([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)/", output)
        return ip_to_check in ip_list
    except Exception:
        return False


def remove_existing_container(name: str):
    try:
        existing = client.containers.get(name)
        existing.stop()
        existing.remove()
    except docker.errors.NotFound:
        pass


def get_bms_processes():
    """ "./bms/bms-simulator"가 포함된 프로세스 목록 반환"""
    processes = []
    for proc in psutil.process_iter(["cmdline"]):
        try:
            cmd = proc.info["cmdline"]
            if cmd and "./bms/bms-simulator" in " ".join(cmd):
                processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return processes


def get_bms_status():
    """BMS가 실행 중이면 'running'을 반환하고, 2개 이상이면 추가 플래그도 함께 반환"""
    procs = get_bms_processes()
    status = "running" if procs else None
    multiple = len(procs) > 1
    return status, multiple


def get_bms_config():
    """실행 중인 BMS 프로세스의 command line에서 IP와 PORT 값을 추출"""
    procs = get_bms_processes()
    if procs:
        try:
            cmd = procs[0].cmdline()
            ip = None
            port = None
            for i, arg in enumerate(cmd):
                if arg == "--ip" and i + 1 < len(cmd):
                    ip = cmd[i + 1]
                elif arg == "--port" and i + 1 < len(cmd):
                    port = cmd[i + 1]
            if ip and port:
                return {"IP": ip, "PORT": port}
        except Exception as e:
            return {}
    return {}


# --- 엔드포인트 ---


@app.get("/", response_class=HTMLResponse)
def index(request: Request, message: str = None):
    sets = {}
    for key, cfg in container_sets.items():
        if key == "BMS":
            status_val, multiple = get_bms_status()
            sets[key] = {
                "type": cfg["type"],
                "container_name": cfg["container_name"],
                "status": status_val,
                "env": get_bms_config() if status_val == "running" else None,
                "image_tags": ["-"],
                "ports": cfg.get("ports", {}),
                "default_image": cfg.get("default_image"),
                "multiple": multiple,
            }
        else:
            status_val, image_tags, ports, env_vars = get_container_status(cfg)
            if cfg["type"] == "single":
                sets[key] = {
                    "type": cfg["type"],
                    "container_name": cfg["container_name"],
                    "status": status_val,
                    "image_tags": image_tags,
                    "ports": ports,
                    "env": env_vars if status_val == "running" else None,
                    "default_image": cfg.get("default_image"),
                }
            else:
                # Autopilot Simulator의 경우 개별 컨테이너 상태 처리
                if key == "Autopilot Simulator":
                    sets[key] = {
                        "type": cfg["type"],
                        "container_name": None,
                        "status": status_val,  # 이미 statuses 딕셔너리
                        "image_tags": image_tags,
                        "ports": ports,
                        "env": env_vars,
                        "containers": cfg["containers"],
                    }
                else:
                    # 기존 ECDIS 등은 overall 상태로 처리
                    overall_status = (
                        "running"
                        if isinstance(status_val, dict)
                        and all(
                            s == "running" for s in status_val.values() if s is not None
                        )
                        and len(status_val)
                        == len([s for s in status_val.values() if s is not None])
                        else "Not Created"
                    )
                    sets[key] = {
                        "type": cfg["type"],
                        "container_name": None,
                        "status": overall_status,
                        "image_tags": image_tags,
                        "ports": ports,
                        "env": env_vars if overall_status == "running" else None,
                        "containers": cfg.get("containers", []),
                    }
    # Autopilot Simulator 설정 기본값 가져오기
    comm_config, autopilot_config = get_autopilot_config_defaults()

    # Ship Simulator 설정 기본값 가져오기
    ship_comm_config, ship_nmea_config, ship_sim_config = (
        get_ship_simulator_config_defaults()
    )

    # JSON 문자열로 변환 (ensure_ascii=False로 Unicode 문자 보존)
    ship_comm_config_json = json.dumps(ship_comm_config, indent=2, ensure_ascii=False)
    ship_nmea_config_json = json.dumps(ship_nmea_config, indent=2, ensure_ascii=False)
    ship_sim_config_json = json.dumps(ship_sim_config, indent=2, ensure_ascii=False)

    # Autopilot Simulator 설정을 JSON 문자열로 변환
    autopilot_comm_config_json = json.dumps(comm_config, indent=2, ensure_ascii=False)
    autopilot_config_json = json.dumps(autopilot_config, indent=2, ensure_ascii=False)

    return templates.TemplateResponse(
        "index.html",
        {
            "request": request,
            "container_sets": sets,
            "all_images": get_all_image_tags(),
            "message": message,
            "autopilot_comm_config": comm_config,
            "autopilot_config": autopilot_config,
            "autopilot_comm_config_json": autopilot_comm_config_json,
            "autopilot_config_json": autopilot_config_json,
            "ship_comm_config": ship_comm_config,
            "ship_nmea_config": ship_nmea_config,
            "ship_sim_config": ship_sim_config,
            "ship_comm_config_json": ship_comm_config_json,
            "ship_nmea_config_json": ship_nmea_config_json,
            "ship_sim_config_json": ship_sim_config_json,
            "app_version": APP_VERSION,
        },
    )


@app.get("/api/simulation_ips", response_class=JSONResponse)
def simulation_ips():
    """
    서버의 네트워크 인터페이스와 IP 정보를 반환합니다.
    예시 반환값:
    [
      { "ip": "**********", "interface": "enp3s0f1" },
      { "ip": "***********", "interface": "eth0" }
    ]
    """
    try:
        output = subprocess.check_output(["ip", "addr"]).decode("utf-8")
        lines = output.splitlines()
        simulation_ips = []
        current_interface = None
        for line in lines:
            # 인터페이스 이름 추출 (예: "2: enp3s0f1:" 등)
            m = re.match(r"\d+:\s+([^:]+):", line)
            if m:
                current_interface = m.group(1)
            else:
                # "inet" 라인을 찾아 IP 주소 추출 (IPv4)
                m_ip = re.search(r"inet\s+(\d+\.\d+\.\d+\.\d+)", line)
                if m_ip and current_interface:
                    ip = m_ip.group(1)
                    simulation_ips.append({"ip": ip, "interface": current_interface})
        return JSONResponse(content=simulation_ips)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/local_ips", response_class=JSONResponse)
def local_ips():
    """
    시스템의 로컬 IPv4 주소 목록을 반환합니다.
    10.0.x.x 대역을 우선으로 정렬하여 반환합니다.
    예시 반환값: ["**********", "***********", ...]
    """
    try:
        output = subprocess.check_output(["ip", "addr"]).decode("utf-8")
        # 정규표현식으로 IPv4 주소 추출
        ips = re.findall(r"inet ([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)", output)
        # localhost (127.0.0.1)는 제외
        ips = [ip for ip in ips if ip != "127.0.0.1"]

        # 10.0.x.x 대역을 우선으로 정렬 (10.0.x.x가 없으면 10.x.x.x 전체 대역 우선)
        def ip_priority(ip):
            if ip.startswith("10.0."):
                return (0, ip)  # 10.0.x.x 대역은 최우선
            elif ip.startswith("10."):
                return (1, ip)  # 10.x.x.x 대역은 두 번째 우선
            else:
                return (2, ip)  # 나머지는 그 다음

        ips.sort(key=ip_priority)
        return JSONResponse(content=ips)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ECDIS (두 컨테이너 실행): 실행 전 기존 컨테이너 제거
@app.post("/start_ecdis")
def start_ecdis(
    request: Request,
    ecdis_image: str = Form(...),
    redis_image: str = Form(...),
    ECDIS_RTZ_RTZ_MULTICAST_GROUP: str = Form(...),
    ECDIS_RTZ_RTZ_MULTICAST_PORT: str = Form(...),
    ECDIS_RTZ_RRT_MULTICAST_GROUP: str = Form(...),
    ECDIS_RTZ_RRT_MULTICAST_PORT: str = Form(...),
    ECDIS_RTZ_ECDIS_SFI: str = Form(...),
    ECDIS_RTZ_HINAS_IP: str = Form(...),
    ECDIS_RTZ_HINAS_SFI: str = Form(...),
    ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT: str = Form(...),
    ECDIS_RTZ_WORK_PERIOD: str = Form(...),
    ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM: str = Form(...),
    ECDIS_RTZ_RTZ_SCHEMA_PATHS: str = Form(...),
    ECDIS_RTZ_APP_PORT: str = Form(...),
    ECDIS_RTZ_RRT_RECEIVE_PORT: str = Form(...),
    ECDIS_RTZ_REDIS_HOST: str = Form(...),
    ECDIS_RTZ_REDIS_PORT: str = Form(...),
):

    # 환경변수 딕셔너리 구성
    envs = {
        "ECDIS_RTZ_RTZ_MULTICAST_GROUP": ECDIS_RTZ_RTZ_MULTICAST_GROUP,
        "ECDIS_RTZ_RTZ_MULTICAST_PORT": ECDIS_RTZ_RTZ_MULTICAST_PORT,
        "ECDIS_RTZ_RRT_MULTICAST_GROUP": ECDIS_RTZ_RRT_MULTICAST_GROUP,
        "ECDIS_RTZ_RRT_MULTICAST_PORT": ECDIS_RTZ_RRT_MULTICAST_PORT,
        "ECDIS_RTZ_ECDIS_SFI": ECDIS_RTZ_ECDIS_SFI,
        "ECDIS_RTZ_HINAS_IP": ECDIS_RTZ_HINAS_IP,
        "ECDIS_RTZ_HINAS_SFI": ECDIS_RTZ_HINAS_SFI,
        "ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT": ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT,
        "ECDIS_RTZ_WORK_PERIOD": ECDIS_RTZ_WORK_PERIOD,
        "ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM": ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM,
        "ECDIS_RTZ_RTZ_SCHEMA_PATHS": ECDIS_RTZ_RTZ_SCHEMA_PATHS,
        "ECDIS_RTZ_APP_PORT": ECDIS_RTZ_APP_PORT,
        "ECDIS_RTZ_RRT_RECEIVE_PORT": ECDIS_RTZ_RRT_RECEIVE_PORT,
        "ECDIS_RTZ_REDIS_HOST": ECDIS_RTZ_REDIS_HOST,
        "ECDIS_RTZ_REDIS_PORT": ECDIS_RTZ_REDIS_PORT,
    }

    errors = []
    try:
        remove_existing_container(
            container_sets["ECDIS"]["containers"][0]["container_name"]
        )
        client.containers.run(
            ecdis_image,
            name=container_sets["ECDIS"]["containers"][0]["container_name"],
            environment=envs,
            network_mode="host",
            user=container_sets["ECDIS"]["containers"][0].get("user", ""),
            command=container_sets["ECDIS"]["containers"][0]["command"],
            restart_policy={"Name": "always"},
            detach=True,
        )
    except Exception as e:
        errors.append(f"ECDIS 실행 오류: {e}")
    try:
        remove_existing_container(
            container_sets["ECDIS"]["containers"][1]["container_name"]
        )
        client.containers.run(
            redis_image,
            name=container_sets["ECDIS"]["containers"][1]["container_name"],
            command=container_sets["ECDIS"]["containers"][1]["command"],
            network_mode="host",
            restart_policy={"Name": "always"},
            detach=True,
        )
    except Exception as e:
        errors.append(f"Redis 실행 오류: {e}")
    msg = " ".join(errors) if errors else "ECDIS 및 Redis 컨테이너가 실행되었습니다."
    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.get("/stop_ecdis")
def stop_ecdis(ajax: bool = False):
    errors = []
    stopped_containers = []

    for c in container_sets["ECDIS"]["containers"]:
        try:
            container = client.containers.get(c["container_name"])
            # 타임아웃 설정 (30초)
            container.stop(timeout=30)
            container.remove()
            stopped_containers.append(c["container_name"])
        except docker.errors.NotFound:
            errors.append(f"{c['container_name']} 컨테이너가 없습니다.")
        except Exception as e:
            errors.append(f"{c['container_name']} 중지 오류: {str(e)}")

    msg = " ".join(errors) if errors else "ECDIS 관련 컨테이너가 중지되었습니다."

    if ajax:
        return JSONResponse(
            {
                "success": len(errors) == 0,
                "message": msg,
                "stopped_containers": stopped_containers,
                "errors": errors,
            }
        )

    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.post("/start_bms")
def start_bms(request: Request, ip: str = Form(...), port: int = Form(504)):
    # 실행 전에 기존 프로세스가 있다면(첫 실행은 추적이 안될 수 있으므로) 건너뛰거나, kill할 수 있음.
    # 여기서는 단순 실행 후 상태를 나중에 조회하도록 함.
    try:
        # subprocess.Popen(["./bms/bms-simulator", ip, str(port)])
        env = os.environ.copy()
        env["LIBGL_ALWAYS_SOFTWARE"] = "1"  # llvmpipe 소프트웨어 렌더러
        env["LIBGL_DRI3_DISABLE"] = "1"  # DRI3 대신 DRI2 사용
        subprocess.Popen(
            ["./bms/bms-simulator", ip, str(port)],
            env=env,
        )
        msg = f"BMS 시뮬레이터 UI가 {ip}:{port}로 실행되었습니다."
    except Exception as e:
        msg = f"BMS 실행 오류: {e}"
    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.get("/stop_bms")
def stop_bms(ajax: bool = False):
    procs = get_bms_processes()
    errors = []
    stopped_processes = []

    if procs:
        for proc in procs:
            try:
                proc.kill()
                stopped_processes.append(f"PID {proc.pid}")
            except Exception as e:
                errors.append(f"PID {proc.pid} 중지 오류: {str(e)}")
        msg = (
            "BMS 시뮬레이터 프로세스가 모두 중지되었습니다."
            if not errors
            else "중지 오류: " + "; ".join(errors)
        )
    else:
        msg = "BMS 시뮬레이터가 실행 중이 아닙니다."

    if ajax:
        return JSONResponse(
            {
                "success": len(errors) == 0 and len(procs) > 0,
                "message": msg,
                "stopped_processes": stopped_processes,
                "errors": errors,
            }
        )

    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.post("/update_bms")
async def update_bms(request: Request, bms_file: UploadFile = File(...)):
    # 기본 실행 파일 경로
    target_path = os.path.join("bms", "bms-simulator")
    try:
        # 새 파일을 target_path 위치에 저장 (덮어쓰기)
        with open(target_path, "wb") as f:
            content = await bms_file.read()
            f.write(content)
        # 실행 권한 부여 (리눅스 환경)
        os.chmod(target_path, 0o755)
        msg = "BMS 실행 파일이 성공적으로 업데이트되었습니다."
    except Exception as e:
        msg = f"BMS 실행 파일 업데이트 오류: {e}"
    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


# @app.post("/start_bms")
# def start_bms(request: Request, image: str = Form(...)):
#     try:
#         remove_existing_container(container_sets["BMS"]["container_name"])
#         client.containers.run(
#             image,
#             name=container_sets["BMS"]["container_name"],
#             environment=container_sets["BMS"].get("env", {}),
#             restart_policy={"Name": "always"},
#             detach=True
#         )
#         msg = "BMS 컨테이너가 실행되었습니다."
#     except Exception as e:
#         msg = f"BMS 실행 오류: {e}"
#     return RedirectResponse(url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER)

# @app.get("/stop_bms")
# def stop_bms():
#     try:
#         container = client.containers.get(container_sets["BMS"]["container_name"])
#         container.stop()
#         container.remove()
#         msg = "BMS 컨테이너가 중지되었습니다."
#     except docker.errors.NotFound:
#         msg = "BMS 컨테이너가 존재하지 않습니다."
#     return RedirectResponse(url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER)


@app.post("/start_ais_vdm_simulator")
def start_ais_vdm_simulator(
    request: Request,
    image: str = Form(...),
    redis_host: str = Form("localhost"),
    redis_port: str = Form("6379"),
    redis_password: str = Form(""),
    redis_enable_ssl: str = Form("0"),
    target_host: str = Form("***********"),
    target_port: str = Form("6503"),
    send_period: str = Form("2"),
):
    try:
        remove_existing_container(container_sets["AIS VDM Simulator"]["container_name"])

        # 환경변수 설정
        env_vars = {
            "REDIS_HOST": redis_host,
            "REDIS_PORT": redis_port,
            "REDIS_PASSWORD": redis_password,
            "REDIS_ENABLE_SSL": redis_enable_ssl,
            "TARGET_HOST": target_host,
            "TARGET_PORT": target_port,
            "SEND_PERIOD": send_period,
        }

        # hostNetwork 모드로 컨테이너 실행 (k8s의 hostNetwork: true와 동일)
        client.containers.run(
            image,
            name=container_sets["AIS VDM Simulator"]["container_name"],
            environment=env_vars,
            network_mode="host",  # hostNetwork: true와 동일
            # hostNetwork 모드에서는 ports 매개변수 불필요 (호스트 네트워크 직접 사용)
            restart_policy={"Name": "unless-stopped"},
            detach=True,
        )
        msg = "AIS VDM Simulator 컨테이너가 실행되었습니다. http://localhost:8000 에서 접근 가능합니다."
    except Exception as e:
        msg = f"AIS VDM Simulator 실행 오류: {e}"
    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.get("/stop_ais_vdm_simulator")
def stop_ais_vdm_simulator(ajax: bool = False):
    container_name = container_sets["AIS VDM Simulator"]["container_name"]
    errors = []
    stopped_containers = []

    try:
        container = client.containers.get(container_name)
        # 타임아웃 설정 (30초)
        container.stop(timeout=30)
        container.remove()
        stopped_containers.append(container_name)
        msg = "AIS VDM Simulator 컨테이너가 중지되었습니다."
    except docker.errors.NotFound:
        msg = "AIS VDM Simulator 컨테이너가 존재하지 않습니다."
        errors.append(msg)
    except Exception as e:
        msg = f"AIS VDM Simulator 중지 오류: {e}"
        errors.append(msg)

    if ajax:
        return JSONResponse(
            {
                "success": len(errors) == 0,
                "message": msg,
                "stopped_containers": stopped_containers,
                "errors": errors,
            }
        )

    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.post("/start_autopilot_simulator")
def start_autopilot_simulator(
    request: Request,
    server_image: str = Form(...),
    ui_image: str = Form(...),
    # Communication 설정
    hinas_server_ip: str = Form("***********"),
    hinas_server_port: int = Form(...),
    nmea_receiver_ip: str = Form(...),
    nmea_receiver_port: int = Form(...),
    redis_host_server: str = Form("localhost"),
    redis_port_server: str = Form("6379"),
    # Control Parameters
    kp_psi: float = Form(...),
    kd_psi: float = Form(...),
    kp_rot: float = Form(...),
    ki_rot: float = Form(...),
    kff_rot: float = Form(...),
    rudder_limit: int = Form(...),
    # Ship Parameters
    ship_type: int = Form(...),
    kff_rot_container_negative: float = Form(...),
    kff_rot_container_positive: float = Form(...),
    # UI 설정
    redis_host: str = Form("localhost"),
    redis_port: str = Form("6379"),
    redis_enable_ssl: str = Form("false"),
    display: str = Form(":0"),
    qt_x11_no_mitshm: str = Form("1"),
    libgl_always_software: str = Form("1"),
    libgl_dri3_disable: str = Form("1"),
    xdg_runtime_dir: str = Form("/tmp"),
):
    errors = []

    # 설정 파일 생성
    comm_data = {
        "hinas_server": {"ip": hinas_server_ip, "port": hinas_server_port},
        "nmea_receiver": {"ip": nmea_receiver_ip, "port": nmea_receiver_port},
    }

    config_data = {
        "control_parameters": {
            "kp_psi": kp_psi,
            "kd_psi": kd_psi,
            "kp_rot": kp_rot,
            "ki_rot": ki_rot,
            "kff_rot": kff_rot,
            "rudder_limit": rudder_limit,
        },
        "ship_parameters": {
            "ship_type": ship_type,
            "kff_rot_container_negative": kff_rot_container_negative,
            "kff_rot_container_positive": kff_rot_container_positive,
        },
    }

    # 설정 파일 생성
    if not create_autopilot_config_files(comm_data, config_data):
        errors.append("설정 파일 생성 실패")

    # Server 컨테이너 먼저 실행
    if not errors:
        try:
            remove_existing_container(
                container_sets["Autopilot Simulator"]["containers"][0]["container_name"]
            )

            # 현재 작업 디렉토리의 절대 경로 가져오기
            current_dir = os.path.abspath(".")
            config_volume = f"{current_dir}/autopilot-simulator/config:/app/config"

            client.containers.run(
                server_image,
                name=container_sets["Autopilot Simulator"]["containers"][0][
                    "container_name"
                ],
                network_mode="host",
                volumes=[config_volume],
                environment={
                    "PYTHONPATH": "/app",
                    "REDIS_HOST": redis_host_server,
                    "REDIS_PORT": redis_port_server,
                    "REDIS_PASSWORD": "",
                    "REDIS_SSL": "false",
                    "REDIS_AUTO_CONN": "true",
                    "REDIS_AUTO_UTC_SET": "false",
                },
                restart_policy={"Name": "always"},
                detach=True,
            )
        except Exception as e:
            errors.append(f"Autopilot Simulator Server 실행 오류: {e}")

    # Server 실행 후 UI 컨테이너 실행
    if not errors:  # Server가 성공적으로 실행된 경우에만 UI 실행
        try:
            import time

            time.sleep(2)  # Server 시작 대기

            ui_env_vars = {
                "REDIS_HOST": redis_host,
                "REDIS_PORT": redis_port,
                "REDIS_ENABLE_SSL": redis_enable_ssl,
                "DISPLAY": display,
                "QT_X11_NO_MITSHM": qt_x11_no_mitshm,
                "LIBGL_ALWAYS_SOFTWARE": libgl_always_software,
                "LIBGL_DRI3_DISABLE": libgl_dri3_disable,
                "XDG_RUNTIME_DIR": xdg_runtime_dir,
            }

            remove_existing_container(
                container_sets["Autopilot Simulator"]["containers"][1]["container_name"]
            )
            client.containers.run(
                ui_image,
                name=container_sets["Autopilot Simulator"]["containers"][1][
                    "container_name"
                ],
                environment=ui_env_vars,
                network_mode="host",
                volumes=[
                    "/tmp/.X11-unix:/tmp/.X11-unix:rw",
                    "/etc/localtime:/etc/localtime:ro",
                ],
                restart_policy={"Name": "always"},
                detach=True,
            )
        except Exception as e:
            errors.append(f"Autopilot Simulator UI 실행 오류: {e}")

    msg = (
        " ".join(errors)
        if errors
        else "Autopilot Simulator (Server + UI) 컨테이너가 실행되었습니다."
    )
    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.post("/start_ship_simulator")
def start_ship_simulator(
    request: Request,
    # Communication 설정
    communication_mode: str = Form(...),
    ecdis_interface_ip: str = Form(...),
    simulator_server_ip: str = Form(...),
    autopilot_target_ip: str = Form(...),
    multicast_group_ip: str = Form(...),
    multicast_port: int = Form(...),
    multicast_ttl: int = Form(...),
    unicast_target_ip: str = Form(...),
    unicast_port: int = Form(...),
    simulator_server_port: int = Form(...),
    autopilot_unicast_port: int = Form(...),
    # Simulation 설정
    ship_type: int = Form(...),
    simulation_speed_up: int = Form(...),
    simulation_mode: str = Form(...),
    # Position 설정
    lat_deg: int = Form(...),
    lat_min: float = Form(...),
    lat_dir: str = Form(...),
    lon_deg: int = Form(...),
    lon_min: float = Form(...),
    lon_dir: str = Form(...),
    # Other initial settings
    initial_speed: float = Form(...),
    initial_course: float = Form(...),
    initial_lever: float = Form(...),
    # Environment settings
    wind_speed: float = Form(...),
    wind_direction: float = Form(...),
    wind_enabled: bool = Form(False),
    current_speed: float = Form(...),
    current_direction: float = Form(...),
    current_enabled: bool = Form(False),
    sea_state: int = Form(...),
    wave_enabled: bool = Form(False),
    # Image
    image: str = Form(...),
):
    try:
        # Position 문자열 생성
        position_str = (
            f"{lat_deg}°{lat_min:.3f}'{lat_dir} {lon_deg}°{lon_min:.3f}'{lon_dir}"
        )

        # Communication 설정 생성
        comm_data = {
            "communication_mode": communication_mode,
            "multicast": {
                "group_ip": multicast_group_ip,
                "port": multicast_port,
                "ttl": multicast_ttl,
                "interface_ip": ecdis_interface_ip,
            },
            "unicast": {"target_ip": unicast_target_ip, "port": unicast_port},
            "simulator_server": {
                "ip": simulator_server_ip,
                "port": simulator_server_port,
            },
            "autopilot_unicast": {
                "target_ip": autopilot_target_ip,
                "port": autopilot_unicast_port,
                "messages": ["GGA", "VTG", "HDT", "ROT", "VBW"],
            },
        }

        # Simulation 설정 생성
        simulation_set = {
            "ship_type": ship_type,
            "simulation_speed_up": simulation_speed_up,
            "simulation_mode": simulation_mode,
        }

        # scenario 모드일 때 기존 marzip_name 유지
        if simulation_mode == "scenario":
            try:
                config_path = "ship-simulator/config/simulation_config.json"
                if os.path.exists(config_path):
                    with open(config_path, "r", encoding="utf-8") as f:
                        existing_config = json.load(f)
                    existing_marzip_name = existing_config.get(
                        "simulation_set", {}
                    ).get("marzip_name", "")
                    if existing_marzip_name:
                        simulation_set["marzip_name"] = existing_marzip_name
            except Exception as e:
                print(f"Warning: Could not load existing marzip_name: {e}")

        sim_data = {
            "simulation_set": simulation_set,
            "initial": {
                "position": position_str,
                "speed": initial_speed,
                "course": initial_course,
                "lever": initial_lever,
            },
            "environment": {
                "wind": {
                    "speed": wind_speed,
                    "direction": wind_direction,
                    "enabled": wind_enabled,
                },
                "current": {
                    "speed": current_speed,
                    "direction": current_direction,
                    "enabled": current_enabled,
                },
                "wave": {"sea_state": sea_state, "enabled": wave_enabled},
            },
        }

        # NMEA 설정은 기존 파일에서 로드
        _, nmea_data, _ = get_ship_simulator_config_defaults()

        # 설정 파일 생성
        create_ship_simulator_config_files(comm_data, nmea_data, sim_data)

        # 기존 컨테이너 제거
        remove_existing_container(container_sets["Ship Simulator"]["container_name"])

        # 볼륨 마운트 설정
        current_dir = os.getcwd()
        config_volume = f"{current_dir}/ship-simulator/config:/app/config"
        logging.info(f"now config_volume : {config_volume}")

        # 컨테이너 실행
        client.containers.run(
            image,
            name=container_sets["Ship Simulator"]["container_name"],
            environment=container_sets["Ship Simulator"]["env"],
            volumes=[config_volume],
            network_mode="host",
            privileged=True,
            cap_add=["NET_ADMIN", "NET_RAW"],
            restart_policy={"Name": "always"},
            detach=True,
        )

        msg = "Ship Simulator 컨테이너가 실행되었습니다."
    except Exception as e:
        msg = f"Ship Simulator 실행 오류: {e}"

    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.post("/api/upload_marzip")
async def upload_marzip(marzip_file: UploadFile = File(...)):
    """Upload and parse marzip file for scenario mode"""
    try:
        # Validate file extension
        if not marzip_file.filename or not marzip_file.filename.endswith(".marzip"):
            return {"success": False, "error": "File must be a .marzip file"}

        # Validate file size (max 50MB)
        content = await marzip_file.read()
        if len(content) > 50 * 1024 * 1024:  # 50MB
            return {"success": False, "error": "File size too large (max 50MB)"}

        if len(content) == 0:
            return {"success": False, "error": "File is empty"}

        print(f"File info: name={marzip_file.filename}, size={len(content)} bytes")

        # Create scenarios directory if it doesn't exist
        scenarios_dir = "ship-simulator/config/scenarios"
        os.makedirs(scenarios_dir, exist_ok=True)

        # Save uploaded file
        file_path = os.path.join(scenarios_dir, marzip_file.filename)
        print(f"Saving file to: {file_path}")

        with open(file_path, "wb") as buffer:
            buffer.write(content)

        # Verify file was saved correctly
        if not os.path.exists(file_path):
            return {"success": False, "error": "Failed to save uploaded file"}

        saved_size = os.path.getsize(file_path)
        print(f"File saved successfully: {saved_size} bytes")

        # Parse marzip file using scenario_manager
        import sys

        # Add current directory to Python path for scenario_manager import
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.append(current_dir)

        try:
            from scenario_manager import ScenarioManager
        except ImportError as e:
            print(f"Error importing scenario_manager: {e}")
            return {"success": False, "error": "scenario_manager module not found"}

        manager = ScenarioManager(
            marzip_file=file_path,
            config_path="ship-simulator/config/simulation_config.json",
        )

        # Update configuration with detailed error handling
        try:
            print(f"Attempting to parse marzip file: {file_path}")
            if not manager.update_config(verbose=True):
                print("ERROR: manager.update_config() returned False")
                return {
                    "success": False,
                    "error": "Failed to parse marzip file. The file may be corrupted or in an invalid format.",
                }
        except Exception as parse_error:
            print(f"ERROR during marzip parsing: {parse_error}")
            return {
                "success": False,
                "error": f"Marzip parsing error: {str(parse_error)}",
            }

        # Set mode to scenario and update marzip_name
        try:
            if not manager.set_mode("scenario", verbose=True):
                print("ERROR: manager.set_mode() returned False")
                return {"success": False, "error": "Failed to set scenario mode"}
        except Exception as mode_error:
            print(f"ERROR setting scenario mode: {mode_error}")
            return {"success": False, "error": f"Mode setting error: {str(mode_error)}"}

        # Update marzip_name in config
        config_path = "ship-simulator/config/simulation_config.json"
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)

        # Update marzip_name to relative path
        relative_path = f"scenarios/{marzip_file.filename}"
        config["simulation_set"]["marzip_name"] = relative_path

        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        # Return parsed scenario data with all necessary info for form filling
        scenario_data = {
            "initial": config.get("initial", {}),
            "environment": config.get("environment", {}),
            "scenario_display": config.get("scenario_display", {}),
            "simulation_set": config.get("simulation_set", {}),
        }

        return {
            "success": True,
            "filename": marzip_file.filename,
            "scenario_data": scenario_data,
        }

    except Exception as e:
        import traceback

        error_details = traceback.format_exc()
        print(f"Error uploading marzip file: {e}")
        print(f"Full traceback: {error_details}")

        # Return more specific error message
        error_msg = str(e)
        if "boundary" in error_msg.lower():
            error_msg = (
                "File upload boundary error. Please try uploading the file again."
            )
        elif "permission" in error_msg.lower():
            error_msg = "Permission denied. Please check file permissions."
        elif "space" in error_msg.lower():
            error_msg = "Insufficient disk space."

        return {"success": False, "error": error_msg, "details": str(e)}


@app.get("/api/current_scenario_data")
def get_current_scenario_data():
    """Get current scenario data for scenario mode"""
    try:
        config_path = "ship-simulator/config/simulation_config.json"
        if not os.path.exists(config_path):
            return {"success": False, "error": "Config file not found"}

        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)

        simulation_set = config.get("simulation_set", {})
        simulation_mode = simulation_set.get("simulation_mode", "normal")

        if simulation_mode != "scenario":
            return {"success": False, "error": "Not in scenario mode"}

        scenario_data = {
            "initial": config.get("initial", {}),
            "environment": config.get("environment", {}),
            "scenario_display": config.get("scenario_display", {}),
        }

        marzip_name = simulation_set.get("marzip_name", "")
        if marzip_name.startswith("scenarios/"):
            marzip_name = marzip_name.replace("scenarios/", "")

        return {
            "success": True,
            "scenario_data": scenario_data,
            "marzip_name": marzip_name,
        }

    except Exception as e:
        print(f"Error getting current scenario data: {e}")
        return {"success": False, "error": str(e)}


@app.get("/stop_ship_simulator")
def stop_ship_simulator(ajax: bool = False):
    container_name = container_sets["Ship Simulator"]["container_name"]
    errors = []
    stopped_containers = []

    try:
        container = client.containers.get(container_name)
        # 타임아웃 설정 (30초)
        container.stop(timeout=30)
        container.remove()
        stopped_containers.append(container_name)
        msg = "Ship Simulator 컨테이너가 중지되었습니다."
    except docker.errors.NotFound:
        msg = "Ship Simulator 컨테이너가 존재하지 않습니다."
        errors.append(msg)
    except Exception as e:
        msg = f"Ship Simulator 중지 오류: {e}"
        errors.append(msg)

    if ajax:
        return JSONResponse(
            {
                "success": len(errors) == 0,
                "message": msg,
                "stopped_containers": stopped_containers,
                "errors": errors,
            }
        )

    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.get("/stop_autopilot_simulator")
def stop_autopilot_simulator(ajax: bool = False):
    errors = []
    stopped_containers = []

    for c in container_sets["Autopilot Simulator"]["containers"]:
        try:
            container = client.containers.get(c["container_name"])
            # 타임아웃 설정 (30초)
            container.stop(timeout=30)
            container.remove()
            stopped_containers.append(c["container_name"])
        except docker.errors.NotFound:
            errors.append(f"{c['container_name']} 컨테이너가 없습니다.")
        except Exception as e:
            errors.append(f"{c['container_name']} 중지 오류: {str(e)}")

    msg = (
        " ".join(errors)
        if errors
        else "Autopilot Simulator 관련 컨테이너가 중지되었습니다."
    )

    if ajax:
        return JSONResponse(
            {
                "success": len(errors) == 0,
                "message": msg,
                "stopped_containers": stopped_containers,
                "errors": errors,
            }
        )

    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.post("/api/save_nmea_config")
async def save_nmea_config(request: Request):
    """NMEA 설정을 저장하는 API"""
    try:
        nmea_data = await request.json()

        # 기존 설정 로드
        comm_config, _, sim_config = get_ship_simulator_config_defaults()

        # NMEA 설정 파일 생성
        create_ship_simulator_config_files(comm_config, nmea_data, sim_config)

        return {"status": "success", "message": "NMEA 설정이 저장되었습니다."}
    except Exception as e:
        return {"status": "error", "message": f"NMEA 설정 저장 오류: {e}"}


# Docker 이미지 tar 파일 업로드 및 로드
@app.post("/load_image")
async def load_image(request: Request, image_tar: UploadFile = File(...)):
    os.makedirs("uploads", exist_ok=True)
    file_path = os.path.join("uploads", image_tar.filename)
    with open(file_path, "wb") as f:
        content = await image_tar.read()
        f.write(content)
    with open(file_path, "rb") as f:
        images_loaded = client.images.load(f.read())
    loaded_tags = []
    for image in images_loaded:
        loaded_tags.extend(image.tags)
    msg = "이미지가 성공적으로 로드되었습니다: " + ", ".join(loaded_tags)
    return RedirectResponse(
        url=f"/?message={msg}", status_code=status.HTTP_303_SEE_OTHER
    )


@app.get("/load_image", response_class=HTMLResponse)
def load_image_form(request: Request):
    return templates.TemplateResponse("load_image.html", {"request": request})


@app.post("/send_ecdis")
def send_ecdis(file: UploadFile = File(...), current_waypoint_id: int = Form(1)):
    # 파일 확장자 검증
    if not file.filename or not file.filename.lower().endswith(".rtz"):
        raise HTTPException(status_code=400, detail="RTZ 파일만 업로드 가능합니다.")

    try:
        # 업로드된 파일 내용 읽기
        file_content = file.file.read()

        # ECDIS API로 파일 전송
        files = {"file": (file.filename, file_content, "application/octet-stream")}
        url = f"http://localhost:8005/nas-ecdis/send-route-to-ecdis/rtz?current_waypoint_id={current_waypoint_id}"
        headers = {
            "accept": "application/json",
        }

        response = requests.post(url, headers=headers, files=files)

        if response.status_code == 200:
            return {
                "message": f"RTZ 파일 '{file.filename}'로 HiNAS Control에 Route가 성공적으로 송신되었습니다.",
                "response": response.json(),
            }
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Route 송신 실패: {response.text}",
            )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Route 송신 중 오류 발생: {str(e)}"
        )
    finally:
        # 파일 포인터 초기화
        file.file.seek(0)


# 이미지 목록 조회
@app.get("/images", response_class=HTMLResponse)
def images(request: Request, q: str = ""):
    all_images = client.images.list()
    # 태그가 존재하는 이미지만 필터링
    filtered_images = []
    for image in all_images:
        if not image.tags:
            continue  # 태그가 없는 이미지는 건너뜁니다.
        if q:
            if any(q.lower() in tag.lower() for tag in image.tags):
                filtered_images.append(image)
        else:
            filtered_images.append(image)
    return templates.TemplateResponse(
        "images.html", {"request": request, "images": filtered_images, "query": q}
    )


# 로그 조회 페이지
@app.get("/logs", response_class=HTMLResponse)
def logs_page(request: Request, container: str = None):
    # 실행 중인 컨테이너 목록 가져오기
    containers = []
    try:
        for container_obj in client.containers.list():
            containers.append(container_obj.name)
    except Exception as e:
        print(f"컨테이너 목록 조회 오류: {e}")

    return templates.TemplateResponse(
        "logs.html",
        {
            "request": request,
            "containers": containers,
            "selected_container": container,
        },
    )


# 컨테이너 로그 조회 (기존 호환성)
@app.get("/logs/{container_name}")
def logs_legacy(container_name: str):
    try:
        container = client.containers.get(container_name)
        logs_text = container.logs(tail=50).decode("utf-8")
        return PlainTextResponse(logs_text)
    except docker.errors.NotFound:
        raise HTTPException(status_code=404, detail="Container not found")


# 향상된 로그 API
@app.get("/api/logs")
def api_logs(
    container: str = None,
    lines: int = 100,
    level: str = None,
    search: str = None,
    since: str = None,
):
    try:
        logs_data = []

        if container:
            # 특정 컨테이너 로그
            try:
                container_obj = client.containers.get(container)
                logs_text = container_obj.logs(tail=lines, since=since).decode(
                    "utf-8", errors="ignore"
                )

                for line in logs_text.strip().split("\n"):
                    if line.strip():
                        # 로그 레벨 필터링
                        if level and level.upper() not in line.upper():
                            continue

                        # 검색 필터링
                        if search and search.lower() not in line.lower():
                            continue

                        logs_data.append(
                            {
                                "container": container,
                                "message": line,
                                "timestamp": datetime.now().isoformat(),
                            }
                        )
            except docker.errors.NotFound:
                pass
        else:
            # 모든 컨테이너 로그
            for container_obj in client.containers.list():
                try:
                    logs_text = container_obj.logs(
                        tail=lines // len(client.containers.list()) or 10, since=since
                    ).decode("utf-8", errors="ignore")

                    for line in logs_text.strip().split("\n"):
                        if line.strip():
                            # 로그 레벨 필터링
                            if level and level.upper() not in line.upper():
                                continue

                            # 검색 필터링
                            if search and search.lower() not in line.lower():
                                continue

                            logs_data.append(
                                {
                                    "container": container_obj.name,
                                    "message": line,
                                    "timestamp": datetime.now().isoformat(),
                                }
                            )
                except Exception as e:
                    print(f"컨테이너 {container_obj.name} 로그 조회 오류: {e}")

        # 최신 로그부터 정렬
        logs_data.sort(key=lambda x: x["timestamp"], reverse=True)

        return logs_data[:lines]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"로그 조회 오류: {str(e)}")


# 로그 다운로드
@app.get("/api/logs/download")
def download_logs(container: str = None, lines: int = 1000):
    try:
        logs_content = ""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if container:
            # 특정 컨테이너 로그
            try:
                container_obj = client.containers.get(container)
                logs_content = container_obj.logs(tail=lines).decode(
                    "utf-8", errors="ignore"
                )
                filename = f"{container}_logs_{timestamp}.txt"
            except docker.errors.NotFound:
                raise HTTPException(status_code=404, detail="Container not found")
        else:
            # 모든 컨테이너 로그
            for container_obj in client.containers.list():
                try:
                    container_logs = container_obj.logs(
                        tail=lines // len(client.containers.list()) or 50
                    ).decode("utf-8", errors="ignore")
                    logs_content += f"\n{'='*50}\n"
                    logs_content += f"Container: {container_obj.name}\n"
                    logs_content += f"{'='*50}\n"
                    logs_content += container_logs + "\n"
                except Exception as e:
                    logs_content += f"\n[ERROR] {container_obj.name}: {str(e)}\n"

            filename = f"all_containers_logs_{timestamp}.txt"

        return Response(
            content=logs_content,
            media_type="text/plain",
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"로그 다운로드 오류: {str(e)}")


# Legacy routes - 기존 UI를 /legacy 경로로 이동
@app.get("/legacy", response_class=HTMLResponse)
def legacy_index(request: Request, message: str = None):
    sets = {}
    for key, cfg in container_sets.items():
        if key == "BMS":
            status_val, multiple = get_bms_status()
            sets[key] = {
                "type": cfg["type"],
                "container_name": cfg["container_name"],
                "status": status_val,
                "env": get_bms_config() if status_val == "running" else None,
                "image_tags": ["-"],
                "ports": cfg.get("ports", {}),
                "default_image": cfg.get("default_image"),
                "multiple": multiple,
            }
        else:
            status_val, image_tags, ports, env_vars = get_container_status(cfg)
            if cfg["type"] == "single":
                sets[key] = {
                    "type": cfg["type"],
                    "container_name": cfg["container_name"],
                    "status": status_val,
                    "image_tags": image_tags,
                    "ports": ports,
                    "env": env_vars if status_val == "running" else None,
                    "default_image": cfg.get("default_image"),
                }
            else:
                sets[key] = {
                    "type": cfg["type"],
                    "container_name": None,
                    "status": status_val,
                    "image_tags": image_tags,
                    "ports": ports,
                    "env": env_vars if status_val == "running" else None,
                    "containers": cfg["containers"],
                }
    return legacy_templates.TemplateResponse(
        "index.html",
        {
            "request": request,
            "container_sets": sets,
            "all_images": get_all_image_tags(),
            "message": message,
        },
    )


@app.get("/legacy/images", response_class=HTMLResponse)
def legacy_images(request: Request, q: str = ""):
    all_images = client.images.list()
    # 태그가 존재하는 이미지만 필터링
    filtered_images = []
    for image in all_images:
        if not image.tags:
            continue  # 태그가 없는 이미지는 건너뜁니다.
        if q:
            if any(q.lower() in tag.lower() for tag in image.tags):
                filtered_images.append(image)
        else:
            filtered_images.append(image)
    return legacy_templates.TemplateResponse(
        "images.html", {"request": request, "images": filtered_images, "query": q}
    )


@app.get("/legacy/load_image", response_class=HTMLResponse)
def legacy_load_image_form(request: Request):
    return legacy_templates.TemplateResponse("load_image.html", {"request": request})


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app:app", host="0.0.0.0", port=7778, reload=True)
