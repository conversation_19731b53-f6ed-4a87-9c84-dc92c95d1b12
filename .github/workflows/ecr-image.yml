name: Docker Image Build & Push
on:
  push:
    tags:
      - "v*.*.*"
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source code
        uses: actions/checkout@v2

      - name: Set output
        id: vars
        run: echo ::set-output name=tag::${GITHUB_REF#refs/*/}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ap-northeast-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build and push hinas-control-mock-simulator (main)
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: hinas-control-mock-simulator
          COMMIT_TAG: ${{ steps.vars.outputs.tag }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_TAG-hinas-control-mock-simulator .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_TAG-hinas-control-mock-simulator

      - name: Build and push ship-simulator
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: hinas-control-mock-simulator
          COMMIT_TAG: ${{ steps.vars.outputs.tag }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_TAG-ship-simulator -f ship-simulator/Dockerfile ./ship-simulator
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_TAG-ship-simulator

      - name: Build and push autopilot-simulator
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: hinas-control-mock-simulator
          COMMIT_TAG: ${{ steps.vars.outputs.tag }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_TAG-autopilot-simulator -f autopilot-simulator/Dockerfile ./autopilot-simulator
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_TAG-autopilot-simulator

      - name: Build and push autopilot-simulator-ui
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: hinas-control-mock-simulator
          COMMIT_TAG: ${{ steps.vars.outputs.tag }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_TAG-autopilot-simulator-ui -f autopilot-simulator/ui_app/Dockerfile ./autopilot-simulator/ui_app
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$COMMIT_TAG-autopilot-simulator-ui