FROM 479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/hinas-control-mock-simulator:base
COPY . .

# Autopilot Simulator config 파일들을 기본값으로 복사
COPY autopilot-simulator/config/ /app/autopilot-simulator/config/

# Ship Simulator config 파일들을 기본값으로 복사
COPY ship-simulator/config/ /app/ship-simulator/config/

WORKDIR /app/bms-simulator
RUN mkdir build
WORKDIR /app/bms-simulator/build
RUN cmake .. && make -j$(nproc)
RUN mv bms-simulator /app/bms/bms-simulator
WORKDIR /app


EXPOSE 7778

CMD ["gunicorn", "app:app", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:7778", "--workers", "4", "--access-logfile", "-", "--error-logfile", "-", "--log-level", "debug"]
