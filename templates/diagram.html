<!-- diagram.html -->
<div class="modal fade" id="diagramModal" tabindex="-1" role="dialog" aria-labelledby="diagramModalLabel" aria-hidden="true">
    <!-- 모달 크기를 화면 폭 90%로 확장 -->
    <div class="modal-dialog modal-dialog-centered" role="document" style="max-width: 90%;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="diagramModalLabel">Network Diagram</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <!-- 다이어그램 표시 영역 -->
          <div id="diagramContainer" 
               style="background-color: #f8f9fa; 
                      padding: 10px; 
                      border-radius: 4px; 
                      overflow: auto; 
                      max-height: 80vh;">
          </div>
          <p class="mt-3">
            <strong>IP 설명</strong><br>
            - **********: ECDIS/Ship Simulator<br>
            - **********: BMS MOCK<br>
            - **********: Autopilot MOCK
          </p>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // 클러스터 내부에서만 rank=same를 적용하여 Mock Simulator 그룹을 명확하게 묶음
    const dotDiagram = `
      digraph G {
        rankdir=TB;
        graph [
          fontsize="8",
          dpi=72,
          nodesep="0.3",
          ranksep="0.4",
          labelloc="t",
          labeljust="left",
          compound=true
        ];
        node [shape=box, style=rounded, fontsize=8];
  
        // 기본 장비
        Control       [label="Control\\n10.0.100.20"];
        Switch_INT    [label="Network Switch (INT)\\n10.0.100.12"];
        Firewall_60F  [label="Firewall (60F)\\n10.0.100.1"];
        Switch_EXT    [label="Network Switch (EXT)\\n10.0.50.8"];
        Serial_9      [label="Serial-to-LAN\\n10.0.50.9"];
        Serial_254    [label="Serial-to-LAN\\n10.0.70.254"];
  
        // 기본 연결 (INT -> Firewall 순서)
        Control     -> Switch_INT   [dir=both];
        Switch_INT  -> Firewall_60F [dir=both];
        Firewall_60F -> Switch_EXT  [dir=both];
        Switch_EXT  -> Serial_9     [dir=both];
        Serial_9    -> Serial_254   [label="Serial", style=dashed, dir=both];
  
        // Mock Simulator (1 PC에 3 NIC) as a cluster
        subgraph cluster_Mock {
          label="Mock Simulator (1 PC, 3 NIC)";
          labelloc="top";
          style=rounded;
          color=gray;
          penwidth=2;
          node [style=filled, fillcolor=white];
          Mock_99 [label="Autopilot\\n(**********)"];
          Mock_12 [label="BMS\\n(**********)"];
          Mock_60 [label="ECDIS/Sensor\\n(**********)"];
          { rank=same; Mock_99; Mock_12; Mock_60; }
        }
  
        // Mock Simulator와 기존 장비 간 연결
        // 1) Autopilot(**********) ↔ Serial-to-LAN(10.0.70.254)
        Mock_99 -> Serial_254 [color=red, penwidth=2, dir=both, label="LAN"];
        // 2) ECDIS(**********) ↔ Firewall(60F)(10.0.100.1)
        Mock_60 -> Firewall_60F [color=green, penwidth=2, dir=both, label="LAN"];
        // 3) BMS(**********) ↔ Network Switch(EXT)(10.0.50.8)
        Mock_12 -> Switch_EXT [color=blue, penwidth=2, dir=both, label="LAN"];
  
        // 외부 노드와의 rank 연결 제거하여 클러스터가 독립적으로 묶이도록 함.
      }
    `;
  
    function renderDiagram() {
      const viz = new Viz();
      viz.renderSVGElement(dotDiagram)
         .then(function(element) {
           const container = document.getElementById('diagramContainer');
           container.innerHTML = "";
           container.appendChild(element);
         })
         .catch(error => {
           console.error(error);
         });
    }
  

  </script>
  