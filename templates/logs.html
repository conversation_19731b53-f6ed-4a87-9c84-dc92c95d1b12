<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HiNAS Mock Simulator - 로그 조회</title>
    <link rel="stylesheet" href="{{ url_for('static', path='bootstrap.min.css') }}">
    <style>
        .log-container {
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 15px;
            border-radius: 5px;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .log-line {
            margin-bottom: 2px;
            padding: 2px 0;
        }
        
        .log-error {
            color: #ff6b6b;
            background-color: rgba(255, 107, 107, 0.1);
        }
        
        .log-warning {
            color: #ffd93d;
            background-color: rgba(255, 217, 61, 0.1);
        }
        
        .log-info {
            color: #74c0fc;
        }
        
        .log-debug {
            color: #95a5a6;
        }
        
        .container-tab {
            margin-bottom: 20px;
        }
        
        .log-controls {
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .auto-scroll {
            position: sticky;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .log-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .stat-error {
            background-color: #ffe6e6;
            color: #d63031;
        }
        
        .stat-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .stat-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2>🔍 로그 조회</h2>
                    <div>
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">← 메인으로</a>
                        <button id="refreshBtn" class="btn btn-primary">새로고침</button>
                        <button id="autoRefreshBtn" class="btn btn-success">자동 새로고침 ON</button>
                    </div>
                </div>
                
                <!-- 로그 제어 패널 -->
                <div class="log-controls">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="containerSelect">컨테이너 선택:</label>
                            <select id="containerSelect" class="form-control">
                                <option value="">전체 컨테이너</option>
                                {% for container_name in containers %}
                                <option value="{{ container_name }}" {% if container_name == selected_container %}selected{% endif %}>
                                    {{ container_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="linesSelect">라인 수:</label>
                            <select id="linesSelect" class="form-control">
                                <option value="50">50줄</option>
                                <option value="100" selected>100줄</option>
                                <option value="200">200줄</option>
                                <option value="500">500줄</option>
                                <option value="1000">1000줄</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="levelFilter">로그 레벨:</label>
                            <select id="levelFilter" class="form-control">
                                <option value="">전체</option>
                                <option value="ERROR">ERROR</option>
                                <option value="WARN">WARNING</option>
                                <option value="INFO">INFO</option>
                                <option value="DEBUG">DEBUG</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="searchInput">검색:</label>
                            <input type="text" id="searchInput" class="form-control" placeholder="키워드 검색...">
                        </div>
                        <div class="col-md-2">
                            <label>&nbsp;</label>
                            <div>
                                <button id="downloadBtn" class="btn btn-info btn-sm">다운로드</button>
                                <button id="clearBtn" class="btn btn-warning btn-sm">지우기</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 로그 통계 -->
                <div class="log-stats">
                    <div class="stat-item stat-error">
                        <span>ERROR: </span><span id="errorCount">0</span>
                    </div>
                    <div class="stat-item stat-warning">
                        <span>WARNING: </span><span id="warningCount">0</span>
                    </div>
                    <div class="stat-item stat-info">
                        <span>INFO: </span><span id="infoCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span>총 라인: </span><span id="totalLines">0</span>
                    </div>
                </div>
                
                <!-- 로그 컨테이너 -->
                <div class="log-container" id="logContainer">
                    <div class="text-center text-muted">
                        로그를 불러오는 중...
                    </div>
                </div>
                
                <!-- 자동 스크롤 표시 -->
                <div class="auto-scroll" id="autoScrollIndicator" style="display: none;">
                    자동 스크롤 활성화됨
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', path='jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', path='bootstrap.min.js') }}"></script>
    <script>
        let autoRefreshInterval = null;
        let autoRefreshEnabled = false;
        let autoScroll = true;
        
        // 로그 레벨별 클래스 매핑
        const logLevelClasses = {
            'ERROR': 'log-error',
            'WARN': 'log-warning',
            'WARNING': 'log-warning',
            'INFO': 'log-info',
            'DEBUG': 'log-debug'
        };
        
        // 로그 로드 함수
        function loadLogs() {
            const container = $('#containerSelect').val();
            const lines = $('#linesSelect').val();
            const level = $('#levelFilter').val();
            const search = $('#searchInput').val();
            
            let url = '/api/logs';
            const params = new URLSearchParams();
            
            if (container) params.append('container', container);
            if (lines) params.append('lines', lines);
            if (level) params.append('level', level);
            if (search) params.append('search', search);
            
            if (params.toString()) {
                url += '?' + params.toString();
            }
            
            $.ajax({
                url: url,
                method: 'GET',
                success: function(data) {
                    displayLogs(data);
                    updateStats(data);
                },
                error: function(xhr, status, error) {
                    $('#logContainer').html('<div class="text-danger">로그 로드 실패: ' + error + '</div>');
                }
            });
        }
        
        // 로그 표시 함수
        function displayLogs(logs) {
            const container = $('#logContainer');
            const wasAtBottom = isScrolledToBottom();
            
            if (Array.isArray(logs)) {
                let html = '';
                logs.forEach(function(log) {
                    const logClass = getLogClass(log.message);
                    const timestamp = new Date(log.timestamp).toLocaleString();
                    html += `<div class="log-line ${logClass}">`;
                    html += `<span class="text-muted">[${timestamp}]</span> `;
                    if (log.container) {
                        html += `<span class="badge badge-secondary">${log.container}</span> `;
                    }
                    html += escapeHtml(log.message);
                    html += '</div>';
                });
                container.html(html);
            } else {
                // 단순 텍스트 로그
                const lines = logs.split('\n');
                let html = '';
                lines.forEach(function(line) {
                    if (line.trim()) {
                        const logClass = getLogClass(line);
                        html += `<div class="log-line ${logClass}">${escapeHtml(line)}</div>`;
                    }
                });
                container.html(html);
            }
            
            // 자동 스크롤
            if (autoScroll && (wasAtBottom || container.children().length < 10)) {
                scrollToBottom();
            }
        }
        
        // 로그 레벨 클래스 결정
        function getLogClass(message) {
            const upperMessage = message.toUpperCase();
            for (const [level, className] of Object.entries(logLevelClasses)) {
                if (upperMessage.includes(level)) {
                    return className;
                }
            }
            return '';
        }
        
        // HTML 이스케이프
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // 스크롤 위치 확인
        function isScrolledToBottom() {
            const container = $('#logContainer')[0];
            return container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
        }
        
        // 맨 아래로 스크롤
        function scrollToBottom() {
            const container = $('#logContainer')[0];
            container.scrollTop = container.scrollHeight;
        }
        
        // 통계 업데이트
        function updateStats(logs) {
            let errorCount = 0;
            let warningCount = 0;
            let infoCount = 0;
            let totalLines = 0;
            
            if (Array.isArray(logs)) {
                totalLines = logs.length;
                logs.forEach(function(log) {
                    const message = log.message.toUpperCase();
                    if (message.includes('ERROR')) errorCount++;
                    else if (message.includes('WARN')) warningCount++;
                    else if (message.includes('INFO')) infoCount++;
                });
            } else {
                const lines = logs.split('\n').filter(line => line.trim());
                totalLines = lines.length;
                lines.forEach(function(line) {
                    const upperLine = line.toUpperCase();
                    if (upperLine.includes('ERROR')) errorCount++;
                    else if (upperLine.includes('WARN')) warningCount++;
                    else if (upperLine.includes('INFO')) infoCount++;
                });
            }
            
            $('#errorCount').text(errorCount);
            $('#warningCount').text(warningCount);
            $('#infoCount').text(infoCount);
            $('#totalLines').text(totalLines);
        }
        
        // 자동 새로고침 토글
        function toggleAutoRefresh() {
            if (autoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                autoRefreshEnabled = false;
                $('#autoRefreshBtn').text('자동 새로고침 OFF').removeClass('btn-success').addClass('btn-outline-success');
                $('#autoScrollIndicator').hide();
            } else {
                autoRefreshInterval = setInterval(loadLogs, 3000); // 3초마다
                autoRefreshEnabled = true;
                $('#autoRefreshBtn').text('자동 새로고침 ON').removeClass('btn-outline-success').addClass('btn-success');
                $('#autoScrollIndicator').show();
            }
        }
        
        // 로그 다운로드
        function downloadLogs() {
            const container = $('#containerSelect').val();
            const lines = $('#linesSelect').val();
            
            let url = '/api/logs/download';
            const params = new URLSearchParams();
            
            if (container) params.append('container', container);
            if (lines) params.append('lines', lines);
            
            if (params.toString()) {
                url += '?' + params.toString();
            }
            
            window.open(url, '_blank');
        }
        
        // 이벤트 리스너
        $(document).ready(function() {
            // 초기 로그 로드
            loadLogs();
            
            // 컨트롤 변경 시 로그 새로고침
            $('#containerSelect, #linesSelect, #levelFilter').change(loadLogs);
            $('#searchInput').on('input', debounce(loadLogs, 500));
            
            // 버튼 이벤트
            $('#refreshBtn').click(loadLogs);
            $('#autoRefreshBtn').click(toggleAutoRefresh);
            $('#downloadBtn').click(downloadLogs);
            $('#clearBtn').click(function() {
                $('#logContainer').html('<div class="text-muted">로그가 지워졌습니다.</div>');
                updateStats([]);
            });
            
            // 스크롤 이벤트
            $('#logContainer').scroll(function() {
                autoScroll = isScrolledToBottom();
            });
        });
        
        // 디바운스 함수
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
