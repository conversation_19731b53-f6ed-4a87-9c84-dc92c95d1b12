<!DOCTYPE html>
<html>
<head>
    <title>HiNAS Control Mock Simulator</title>
    <!-- Bootstrap 4 CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='bootstrap.min.css') }}">
    <!-- jQuery를 head에 미리 로드 -->
    <script src="{{ url_for('static', path='jquery-3.6.0.min.js') }}"></script>
    <link rel="icon" href="{{ url_for('static', path='favicon.ico') }}">
    <style>
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
        }
        .clickable {
            cursor: pointer;
            text-decoration: underline;
        }
        .modal-xl {
            max-width: 95%;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="top-bar">
        <h1>
        <a href="{{ url_for('legacy_index') }}" style="color: inherit; text-decoration: none;">
          HiNAS Control Mock Simulator (Legacy)
        </a>
        </h1>

        <div>
            <a class="btn btn-info" href="{{ url_for('legacy_images') }}">이미지 목록</a>
            <button type="button" class="btn btn-info" data-toggle="modal" data-target="#diagramModal">
                Diagram
            </button>
        </div>
    </div>

    <!-- Flash 메시지 -->
    {% if message %}
    <div class="alert alert-info">
      <p>{{ message }}</p>
    </div>
    {% endif %}
  

    <!-- 메인 테이블 -->
    <table class="table table-bordered">
        <thead class="thead-light">
            <tr>
              <th style="width:15%;">Mock</th>
              <th style="width:15%;">상태</th>
              <th style="width:20%;">이미지 태그</th>
              <th style="width:20%;">액션</th>
              <th style="width:10%;">로그</th>
            </tr>
          </thead>
        <tbody>
            {% for key, cfg in container_sets.items() %}
            <tr>
                <!-- 컨테이너 이름 -->
                <td class="clickable" data-toggle="modal" data-target="#envModal_{{ key|replace(' ', '_') }}">
                    {{ key }}
                </td>
                <td>{{ cfg.status if cfg.status else "Not Created" }}</td>
                <td>
                    {% if key == 'BMS' %}
                        -
                    {% elif key == 'ECDIS' %}
                        {% for container_name, tag in cfg.image_tags.items() %}
                            <strong>{{ container_name }}</strong>: 
                            {% if tag %}
                                {{ tag }}
                            {% else %}
                                Missing Image
                            {% endif %}
                            <br>
                        {% endfor %}
                    {% elif cfg.image_tags %}
                        {% for tag in cfg.image_tags %}
                            {{ tag }}<br>
                        {% endfor %}
                    {% endif %}
                </td>                
                <td>
                    {% if key == 'ECDIS' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_ecdis" data-status="{{ cfg.status }}">실행</button>
                          <a class="btn btn-danger btn-sm stop-btn" href="{{ url_for('stop_ecdis') }}">중지</a>
                          <a class="btn btn-info btn-sm" target="_blank" href="http://localhost:8005/nas-ecdis/docs">Docs</a>
                          <button class="btn btn-primary btn-sm" onclick="sendEcdis()">Send</button>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_ecdis" data-status="{{ cfg.status }}">실행</button>
                          <a class="btn btn-danger btn-sm stop-btn" href="{{ url_for('stop_ecdis') }}">중지</a>
                          <button class="btn btn-info btn-sm" disabled>Docs</button>
                          <button class="btn btn-primary btn-sm" disabled>Send</button>
                        {% endif %}
                    {% elif key == 'Sensor Network' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_sensor" data-status="{{ cfg.status }}">실행</button>
                          <a class="btn btn-danger btn-sm stop-btn" href="{{ url_for('stop_sensor') }}">중지</a>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_sensor" data-status="{{ cfg.status }}">실행</button>
                          <a class="btn btn-danger btn-sm stop-btn" href="{{ url_for('stop_sensor') }}">중지</a>
                        {% endif %}
                    {% elif key == 'BMS' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_bms" data-status="{{ cfg.status }}">실행</button>
                          <a class="btn btn-danger btn-sm stop-btn" href="{{ url_for('stop_bms') }}">중지</a>
                          <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#modal_update_bms">업로드</button>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_bms" data-status="{{ cfg.status }}">실행</button>
                          <a class="btn btn-danger btn-sm stop-btn" href="{{ url_for('stop_bms') }}">중지</a>
                          <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#modal_update_bms">업로드</button>
                        {% endif %}                                
                    {% elif key == 'Autopilot MOCK' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_autopilot" data-status="{{ cfg.status }}">실행</button>
                          <a class="btn btn-danger btn-sm stop-btn" href="{{ url_for('stop_autopilot') }}">중지</a>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_autopilot" data-status="{{ cfg.status }}">실행</button>
                          <a class="btn btn-danger btn-sm stop-btn" href="{{ url_for('stop_autopilot') }}">중지</a>
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    {% if key == 'BMS' %}
                        <button class="btn btn-warning btn-sm" disabled>로그</button>
                    {% else %}
                        <button class="btn btn-warning btn-sm log-btn" 
                          data-container="{% if cfg.container_name %}{{ cfg.container_name }}{% else %}{{ cfg.containers[0].container_name }}{% endif %}" 
                          data-status="{{ cfg.status }}">로그</button>
                    {% endif %}
                </td>      
            </tr>

        <!-- 환경변수 Modal (컨테이너 이름 클릭 시) -->
        <div class="modal fade" id="envModal_{{ key|replace(' ', '_') }}" tabindex="-1" role="dialog" aria-labelledby="envModalLabel_{{ key|replace(' ', '_') }}" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title" id="envModalLabel_{{ key|replace(' ', '_') }}">환경 변수: {{ key }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
                    <span aria-hidden="true">&times;</span>
                </button>
                </div>
                <div class="modal-body">
                {% if cfg.env is not none %}
                    {% if cfg.type == 'group' %}
                    {% for container_name, env in cfg.env.items() %}
                        <h6>{{ container_name }}</h6>
                        {% if env %}
                        <ul>
                            {% for k, v in env.items() %}
                            <li><strong>{{ k }}</strong>: {{ v }}</li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <p>해당 컨테이너의 환경변수를 조회할 수 없습니다.</p>
                        {% endif %}
                    {% endfor %}
                    {% else %}
                    {% if cfg.env %}
                        <ul>
                        {% for k, v in cfg.env.items() %}
                            <li><strong>{{ k }}</strong>: {{ v }}</li>
                        {% endfor %}
                        </ul>
                    {% else %}
                        <p>환경변수가 없습니다.</p>
                    {% endif %}
                    {% endif %}
                {% else %}
                    <p>컨테이너가 실행 중이지 않아 환경변수를 조회할 수 없습니다.</p>
                {% endif %}
                </div>
            </div>
            </div>
        </div>
            {% endfor %}
        </tbody>
    </table>

    <a class="btn btn-primary" href="{{ url_for('load_image') }}">이미지 tar 파일 로드</a>
</div>

<!-- 실행용 모달들 -->

<!-- Sensor Network Modal (내 IP 선택 포함) -->
<div class="modal fade" id="modal_sensor" tabindex="-1" role="dialog" aria-labelledby="modalSensorLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
      <form method="post" action="{{ url_for('start_sensor') }}">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalSensorLabel">Sensor Network 실행</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        <div class="modal-body">
            <!-- 내 IP 입력 대신 select box -->
            <div class="form-group">
                <label for="local_ip_select">내 IP (INTERFACE)</label>
                <select class="form-control" id="local_ip_select" name="interface" required>
                <!-- 옵션은 AJAX로 채워집니다. -->
                </select>
            </div>
          <!-- 이미지 선택 (기본: simulator:test 등) -->
          <div class="form-group">
            <label for="image_sensor">실행할 이미지</label>
            <select class="form-control" id="image_sensor" name="image">
                {% for img in all_images %}
                    <option value="{{ img }}" {% if img == container_sets['Sensor Network'].default_image %}selected{% endif %}>{{ img }}</option>
                {% endfor %}
            </select>
            <small class="form-text text-muted">
              기본 이미지: {{ container_sets['Sensor Network'].default_image if container_sets['Sensor Network'].default_image else '없음' }}
            </small>
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-success">실행</button>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- ECDIS Modal (두 이미지 선택 및 환경변수 입력) -->
<div class="modal fade" id="modal_ecdis" tabindex="-1" role="dialog" aria-labelledby="modalEcdisLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
      <form method="post" action="{{ url_for('start_ecdis') }}">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalEcdisLabel">ECDIS 실행</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <!-- ECDIS 이미지 선택 -->
            <div class="form-group">
              <label for="ecdis_image">ECDIS 이미지</label>
              <select class="form-control" id="ecdis_image" name="ecdis_image">
                {% for img in all_images %}
                  <option value="{{ img }}" {% if img == container_sets['ECDIS'].containers[0].default_image %}selected{% endif %}>{{ img }}</option>
                {% endfor %}
              </select>
              <small class="form-text text-muted">
                기본 이미지: {{ container_sets['ECDIS'].containers[0].default_image }}
              </small>
            </div>
            <!-- Redis 이미지 선택 -->
            <div class="form-group">
              <label for="redis_image">Redis 이미지</label>
              <select class="form-control" id="redis_image" name="redis_image">
                {% for img in all_images %}
                  <option value="{{ img }}" {% if img == container_sets['ECDIS'].containers[1].default_image %}selected{% endif %}>{{ img }}</option>
                {% endfor %}
              </select>
              <small class="form-text text-muted">
                기본 이미지: {{ container_sets['ECDIS'].containers[1].default_image }}
              </small>
            </div>
            <hr>
            <!-- ECDIS 환경변수 입력 필드 -->
            <div class="form-group">
              <label for="ECDIS_RTZ_HINAS_IP">Mock Simulator ECDIS Local IP</label>
              <select class="form-control" name="ECDIS_RTZ_HINAS_IP" id="ECDIS_RTZ_HINAS_IP" required>
                <!-- AJAX로 현재 PC에 설정된 IP 목록이 채워집니다 -->
              </select>
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RTZ_MULTICAST_GROUP">RTZ Multicast Group</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RTZ_MULTICAST_GROUP" id="ECDIS_RTZ_RTZ_MULTICAST_GROUP" value="************">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RTZ_MULTICAST_PORT">RTZ Multicast Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RTZ_MULTICAST_PORT" id="ECDIS_RTZ_RTZ_MULTICAST_PORT" value="60025">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RRT_MULTICAST_GROUP">RRT Multicast Group</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RRT_MULTICAST_GROUP" id="ECDIS_RTZ_RRT_MULTICAST_GROUP" value="***********">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RRT_MULTICAST_PORT">RRT Multicast Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RRT_MULTICAST_PORT" id="ECDIS_RTZ_RRT_MULTICAST_PORT" value="60001">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_ECDIS_SFI">ECDIS SFI</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_ECDIS_SFI" id="ECDIS_RTZ_ECDIS_SFI" value="II0001">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_HINAS_SFI">HiNAS SFI</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_HINAS_SFI" id="ECDIS_RTZ_HINAS_SFI" value="EI0001">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT">Route Transfer Timeout</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT" id="ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT" value="0.1">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_WORK_PERIOD">Work Period</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_WORK_PERIOD" id="ECDIS_RTZ_WORK_PERIOD" value="0.6">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM">Position Threshold (NM)</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM" id="ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM" value="0.01">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RTZ_SCHEMA_PATHS">RTZ Schema Paths (JSON List)</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RTZ_SCHEMA_PATHS" id="ECDIS_RTZ_RTZ_SCHEMA_PATHS" value='["RTZ_Schema_version_1_2.xsd","RTZ_Schema_version_1_0.xsd"]'>
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_APP_PORT">App Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_APP_PORT" id="ECDIS_RTZ_APP_PORT" value="8005">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RRT_RECEIVE_PORT">RRT Receive Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RRT_RECEIVE_PORT" id="ECDIS_RTZ_RRT_RECEIVE_PORT" value="8006">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_REDIS_HOST">Redis Host</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_REDIS_HOST" id="ECDIS_RTZ_REDIS_HOST" value="localhost">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_REDIS_PORT">Redis Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_REDIS_PORT" id="ECDIS_RTZ_REDIS_PORT" value="6379">
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-success">실행</button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
          </div>
        </div>
      </form>
    </div>
  </div>
  

<!-- BMS Modal -->
<div class="modal fade" id="modal_bms" tabindex="-1" role="dialog" aria-labelledby="modalBmsLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <form method="post" action="{{ url_for('start_bms') }}">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" id="modalBmsLabel">BMS 실행 안내</h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="닫기">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="alert alert-warning">
            <strong>주의:</strong> Host PC에 설정된 IP:PORT로 BMS 시뮬레이터가 실행됩니다.<br>
            HiNAS Control 설정과 일치해야 하오니, 올바른 IP와 Port를 선택해주세요. (예: **********:504)
          </div>
          <div class="form-group">
            <label for="bms_ip_select">실행할 IP 선택</label>
            <select class="form-control" id="bms_ip_select" name="ip" required>
              <!-- AJAX로 현재 PC에 설정된 IP 목록이 채워집니다 -->
            </select>
          </div>
          <div class="form-group">
            <label for="bms_port">Port 번호</label>
            <input type="number" class="form-control" id="bms_port" name="port" value="504" required>
          </div>
          <p>실행 버튼을 누르면 잠시 후 BMS 시뮬레이터 UI가 선택된 IP와 Port로 실행됩니다.</p>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-success">실행</button>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
        </div>
      </div>
    </form>
  </div>
</div>


<!-- BMS 실행 파일 업데이트 모달 -->
<div class="modal fade" id="modal_update_bms" tabindex="-1" role="dialog" aria-labelledby="modalUpdateBmsLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <form method="post" action="{{ url_for('update_bms') }}" enctype="multipart/form-data">
      <div class="modal-content">
        <div class="modal-header bg-info text-white">
          <h5 class="modal-title" id="modalUpdateBmsLabel">BMS 실행 파일 업데이트</h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="닫기">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="bms_file">새 BMS 실행 파일 선택</label>
            <input type="file" class="form-control-file" id="bms_file" name="bms_file" required>
          </div>
          <p class="text-muted">업로드 후 기존 파일은 새 파일로 교체되며, 실행 권한이 자동으로 부여됩니다.</p>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">업데이트</button>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
        </div>
      </div>
    </form>
  </div>
</div>



<!-- Autopilot MOCK Modal -->
<div class="modal fade" id="modal_autopilot" tabindex="-1" role="dialog" aria-labelledby="modalAutopilotLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
      <form method="post" action="{{ url_for('start_autopilot') }}">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalAutopilotLabel">Autopilot MOCK 실행</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <!-- Autopilot On: True/False 선택 -->
            <div class="form-group">
              <label for="autopilot_on">Autopilot On (Autopilot 장비 연동 여부(Mock: True / LAN: False)</label>
              <select class="form-control" id="autopilot_on" name="autopilot_on">
                <option value="True" selected>True</option>
                <option value="False">False</option>
              </select>
            </div>
            <!-- NAS IP -->
            <div class="form-group">
              <label for="nas_ip">NAS IP (Mock: *********** / LAN: ***********)</label>
              <input type="text" class="form-control" id="nas_ip" name="nas_ip" value="***********" required>
            </div>
            <!-- NAS Autopilot Port -->
            <div class="form-group">
              <label for="nas_autopilot_port">NAS Autopilot Port</label>
              <input type="number" class="form-control" id="nas_autopilot_port" name="nas_autopilot_port" value="4001" required>
            </div>
            <!-- Simulation IP 셀렉트 박스: API 호출로 채움 -->
            <div class="form-group">
              <label for="simulation_ip_select">Simulation IP (Simulator)</label>
              <select class="form-control" id="simulation_ip_select" name="simulation_ip" required>
                <!-- 옵션은 API 응답으로 채워집니다. -->
              </select>
            </div>
            <!-- Interface Name: 선택한 Simulation IP에 따라 자동 업데이트 (읽기 전용) -->
            <div class="form-group">
              <label for="interface_name">Interface Name</label>
              <input type="text" class="form-control" id="interface_name" name="interface_name" value="" readonly required>
            </div>
            <!-- 실행할 이미지 선택 (옵션) -->
            <div class="form-group">
              <label for="image_autopilot">실행할 이미지</label>
              <select class="form-control" id="image_autopilot" name="image">
                  {% for img in all_images %}
                      <option value="{{ img }}" {% if img == container_sets['Autopilot MOCK'].default_image %}selected{% endif %}>{{ img }}</option>
                  {% endfor %}
              </select>
              <small class="form-text text-muted">
                기본 이미지: {{ container_sets['Autopilot MOCK'].default_image }}
              </small>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-success">실행</button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
          </div>
        </div>
      </form>
    </div>
  </div>
    

<!-- 공통 로그 모달 (크게 표시) -->
<div class="modal fade" id="modal_logs" tabindex="-1" role="dialog" aria-labelledby="modalLogsLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalLogsLabel">로그</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
            <pre id="logs_container" style="height:600px; overflow:auto; background-color:#000; color:#0f0; padding:10px;"></pre>
        </div>
      </div>
    </div>
  </div>

<!-- Diagram Modal (diagram.html) -->
{% include "diagram.html" %}

<!-- jQuery, Popper, Bootstrap JS (jQuery full 버전 사용) -->
<script src="{{ url_for('static', path='popper.min.js') }}"></script>
<script src="{{ url_for('static', path='bootstrap.min.js') }}"></script>

<!-- Viz.js + full.render.js (Graphviz) -->
<script src="{{ url_for('static', path='viz.js') }}"></script>
<script src="{{ url_for('static', path='full.render.js') }}"></script>
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/viz.js/2.1.2/viz.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/viz.js/2.1.2/full.render.js"></script> -->

<script>

    $(document).on('submit', '.modal form', function(e) {
        var $modal = $(this).closest('.modal');
        var status = $modal.data('status');  // 모달에 저장된 실행 상태
        if(status === "running"){
            alert("컨테이너가 이미 실행 중입니다. 실행 요청을 보낼 수 없습니다.");
            e.preventDefault();
            return false;
        }
        // 제출 버튼에 스피너 처리 (중복 제출 방지)
        var $submitButton = $(this).find('button[type="submit"]');
        if ($submitButton.prop('disabled')) return false;
        $submitButton.prop('disabled', true);
        var originalText = $submitButton.html();
        $submitButton.html('<span class="spinner-border spinner-border-sm"></span> ' + originalText);
    });

    $(document).on('click', '.start-btn', function(e) {
        var status = $(this).data('status');
        e.preventDefault();
        e.stopImmediatePropagation();
        if (status && status.toLowerCase() === "running") {
            alert("이미 실행 중입니다.");
            return false;
        }
    });



    $(document).on('click', '.start-btn, .stop-btn', function(e) {
        var $btn = $(this);
        if ($btn.hasClass('disabled')) return false;
        
        var $row = $btn.closest('tr');
        // 같은 행의 모든 버튼과 링크를 비활성화
        $row.find('button').prop('disabled', true).addClass('disabled');
        $row.find('a').addClass('disabled');
        
        // 중지 버튼일 경우에만 스피너 아이콘 추가
        if ($btn.hasClass('stop-btn')) {
            var originalText = $btn.html();
            $btn.html('<span class="spinner-border spinner-border-sm"></span> ' + originalText);
        }
    });


  
    // 로그 버튼 클릭 시 (변경 없음)
    $('.log-btn').on('click', function(e) {
        var status = $(this).data('status');
        var container = $(this).data('container');
        if(status === "Not Created") {
            alert("컨테이너가 생성되지 않았습니다. 먼저 실행하세요.");
            e.preventDefault();
            return false;
        }
        $('#modalLogsLabel').text("로그 (" + container + ")");
        $('#modal_logs').data('container', container);
        $('#modal_logs').modal('show');
    });
  
    // 로그 모달 열리면 2초마다 로그 갱신
    $('#modal_logs').on('shown.bs.modal', function () {
        var container = $(this).data('container');
        var logInterval = setInterval(function(){
            $.ajax({
                url: "{{ url_for('logs', container_name='dummy') }}".replace('dummy', container),
                success: function(data) {
                    $('#logs_container').text(data);
                    $('#logs_container').scrollTop($('#logs_container')[0].scrollHeight);
                }
            });
        }, 2000);
        // 모달 닫힐 때 로그 폴링 중단
        $('#modal_logs').on('hide.bs.modal', function () {
            clearInterval(logInterval);
        });
    });

    // 센서네트워크 모달이 열릴 때 로컬 IP 목록을 가져와 select box에 채우기
    $('#modal_sensor').on('show.bs.modal', function() {
        var $select = $('#local_ip_select');  // 위 select box의 id
        $select.empty(); // 기존 옵션 비우기
        $.ajax({
            url: "/api/local_ips",
            method: "GET",
            dataType: "json",
            success: function(data) {
                // 반환된 data는 IP 주소의 배열입니다.
                $.each(data, function(index, ip) {
                    $select.append($('<option>', {
                        value: ip,
                        text: ip
                    }));
                });
            },
            error: function() {
                alert("로컬 IP 정보를 불러오는데 실패했습니다.");
            }
        });
    });
  
    // 시뮬레이션 IP 관련 코드 (모달 Autopilot 부분)
    var simulationData = [];
    $('#modal_autopilot').on('show.bs.modal', function() {
        var $simSelect = $('#simulation_ip_select');
        $simSelect.empty();
        $.ajax({
            url: "/api/simulation_ips",
            method: "GET",
            dataType: "json",
            success: function(data) {
                simulationData = data;
                $.each(data, function(index, item) {
                    $simSelect.append($('<option>', {
                        value: item.ip,
                        text: item.ip
                    }));
                });
                $simSelect.change();
            },
            error: function() {
                alert("Simulation IP 데이터를 불러오는데 실패했습니다.");
            }
        });
    });
    // BMS 모달이 열릴 때 현재 PC의 IP 목록을 가져와 셀렉박스에 채우기
    $('#modal_bms').on('show.bs.modal', function() {
        var $select = $('#bms_ip_select');
        $select.empty();
        $.ajax({
            url: "/api/local_ips",
            method: "GET",
            dataType: "json",
            success: function(data) {
                if(data.length > 0){
                    $.each(data, function(index, ip) {
                        $select.append($('<option>', {
                            value: ip,
                            text: ip
                        }));
                    });
                } else {
                    $select.append($('<option>', {
                        value: "",
                        text: "설정된 IP가 없습니다."
                    }));
                }
            },
            error: function() {
                $select.append($('<option>', {
                    value: "",
                    text: "IP 정보를 불러오는데 실패했습니다."
                }));
            }
        });
    });

    // ECDIS 모달이 열릴 때 현재 PC의 IP 목록을 가져와 HiNAS IP 셀렉박스에 채우기
    $('#modal_ecdis').on('show.bs.modal', function() {
        var $select = $('#ECDIS_RTZ_HINAS_IP');
        $select.empty();
        $.ajax({
            url: "/api/local_ips",
            method: "GET",
            dataType: "json",
            success: function(data) {
                if(data.length > 0){
                    var defaultSelected = false;
                    $.each(data, function(index, ip) {
                        var isSelected = (ip === "127.0.0.1" && !defaultSelected);
                        if (isSelected) defaultSelected = true;
                        $select.append($('<option>', {
                            value: ip,
                            text: ip,
                            selected: isSelected
                        }));
                    });
                    // 127.0.0.1이 목록에 없으면 첫 번째 IP를 선택
                    if (!defaultSelected && data.length > 0) {
                        $select.find('option:first').prop('selected', true);
                    }
                } else {
                    $select.append($('<option>', {
                        value: "",
                        text: "설정된 IP가 없습니다."
                    }));
                }
            },
            error: function() {
                $select.append($('<option>', {
                    value: "",
                    text: "IP 정보를 불러오는데 실패했습니다."
                }));
            }
        });
    });



    $('#simulation_ip_select').on('change', function() {
        var selectedIP = $(this).val();
        var selectedItem = simulationData.find(function(item) {
            return item.ip === selectedIP;
        });
        if (selectedItem) {
            $('#interface_name').val(selectedItem.interface);
        } else {
            $('#interface_name').val('');
        }
    });
    
    // Diagram 렌더링 (모달 Diagram 부분)
    $(document).ready(function(){
        $('#diagramModal').on('shown.bs.modal', function() {
            renderDiagram();
        });
    });
    
    function sendEcdis(){
        $.post("{{ url_for('send_ecdis') }}")
        .done(function(data){
            alert(data.message);
        })
        .fail(function(xhr){
            alert("Send 명령 전달 실패: " + xhr.responseText);
        });
    }
  </script>
</body>
</html>
