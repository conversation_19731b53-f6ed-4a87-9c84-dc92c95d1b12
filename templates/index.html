<!DOCTYPE html>
<html>
<head>
    <title>HiNAS Control Mock Simulator</title>
    <!-- Bootstrap 4 CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='bootstrap.min.css') }}">
    <!-- jQuery를 head에 미리 로드 -->
    <script src="{{ url_for('static', path='jquery-3.6.0.min.js') }}"></script>
    <link rel="icon" href="{{ url_for('static', path='favicon.ico') }}">
    <style>
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
        }
        .clickable {
            cursor: pointer;
            text-decoration: underline;
        }
        .modal-xl {
            max-width: 95%;
        }

        /* RTZ 파일 업로드 드래그 앤 드롭 스타일 */
        .drop-zone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .drop-zone:hover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }

        .drop-zone.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
            transform: scale(1.02);
        }

        .drop-zone-content {
            pointer-events: none;
        }

        .custom-file-label::after {
            content: "찾기";
        }
    </style>
</head>
<body>
<div class="container">
    <div class="top-bar">
        <h2>
        <a href="{{ url_for('index') }}" style="color: inherit; text-decoration: none;">
          HiNAS Control Mock Simulator
        </a>
        </h2>
      
        <div>
            <a class="btn btn-info" href="{{ url_for('images') }}">이미지 목록</a>
            <button type="button" class="btn btn-info" data-toggle="modal" data-target="#diagramModal">
                Diagram
            </button>
        </div>
    </div>

    <!-- Flash 메시지 -->
    {% if message %}
    <div class="alert alert-info">
      <p>{{ message }}</p>
    </div>
    {% endif %}
  

    <!-- 메인 테이블 -->
    <table class="table table-bordered">
        <thead class="thead-light">
            <tr>
              <th style="width:15%;">Mock</th>
              <th style="width:15%;">상태</th>
              <th style="width:32%;">이미지 태그</th>
              <th style="width:20%;">액션</th>
              <th style="width:10%;">로그</th>
            </tr>
          </thead>
        <tbody>
            {% for key, cfg in container_sets.items() %}
            <tr>
                <!-- 컨테이너 이름 -->
                <td class="clickable" data-toggle="modal" data-target="#envModal_{{ key|replace(' ', '_') }}">
                    {{ key }}
                </td>
                <td>
                    {% if key == 'Autopilot Simulator' %}
                        {% if cfg.status is mapping %}
                            {% for container_name, status in cfg.status.items() %}
                                <strong>{{ 'Server' if container_name == 'autopilot-simulator' else 'UI' }}</strong>: {{ status }}<br>
                            {% endfor %}
                        {% else %}
                            <strong>Server</strong>: Not Created<br>
                            <strong>UI</strong>: Not Created
                        {% endif %}
                    {% else %}
                        {{ cfg.status if cfg.status else "Not Created" }}
                    {% endif %}
                </td>
                <td>
                    {% if key == 'BMS' %}
                        -
                    {% elif key == 'AIS VDM Simulator' %}
                        {% if cfg.image_tags %}
                            {% for tag in cfg.image_tags %}
                                {{ tag }}<br>
                            {% endfor %}
                        {% else %}
                            -
                        {% endif %}
                    {% elif key == 'Autopilot Simulator' %}
                        {% if cfg.image_tags %}
                            {% for container_name, tag in cfg.image_tags.items() %}
                                <strong>{{ 'Server' if container_name == 'autopilot-simulator' else 'UI' }}</strong>:
                                {% if tag %}
                                    {{ tag.replace('479435310497.dkr.ecr.ap-northeast-2.amazonaws.com', 'ecr') }}
                                {% else %}
                                    Missing Image
                                {% endif %}
                                <br>
                            {% endfor %}
                        {% else %}
                            <strong>Server</strong>: {{ container_sets['Autopilot Simulator'].containers[0].default_image }}<br>
                            <strong>UI</strong>: {{ container_sets['Autopilot Simulator'].containers[1].default_image }}
                        {% endif %}
                    {% elif key == 'ECDIS' %}
                        {% for container_name, tag in cfg.image_tags.items() %}
                            <strong>{{ container_name }}</strong>:
                            {% if tag %}
                                {{ tag.replace('479435310497.dkr.ecr.ap-northeast-2.amazonaws.com', 'ecr') }}
                            {% else %}
                                Missing Image
                            {% endif %}
                            <br>
                        {% endfor %}
                    {% elif cfg.image_tags %}
                        {% for tag in cfg.image_tags %}
                            {{ tag.replace('479435310497.dkr.ecr.ap-northeast-2.amazonaws.com', 'ecr') }}<br>
                        {% endfor %}
                    {% endif %}
                </td>
                <td>
                    {% if key == 'ECDIS' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_ecdis" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="ECDIS" data-stop-url="{{ url_for('stop_ecdis') }}">중지</button>
                          <a class="btn btn-info btn-sm" target="_blank" href="http://localhost:8005/nas-ecdis/docs">API</a>
                          <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modal_send_ecdis">Send</button>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_ecdis" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="ECDIS" data-stop-url="{{ url_for('stop_ecdis') }}">중지</button>
                          <button class="btn btn-info btn-sm" disabled>API</button>
                          <button class="btn btn-primary btn-sm" disabled>Send</button>
                        {% endif %}
                    {% elif key == 'Ship Simulator' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_ship_simulator" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="Ship Simulator" data-stop-url="{{ url_for('stop_ship_simulator') }}">중지</button>
                          <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#modal_nmea_management">센서</button>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_ship_simulator" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="Ship Simulator" data-stop-url="{{ url_for('stop_ship_simulator') }}">중지</button>
                          <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#modal_nmea_management">센서</button>
                        {% endif %}
                    {% elif key == 'BMS' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_bms" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="BMS" data-stop-url="{{ url_for('stop_bms') }}">중지</button>
                          <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#modal_update_bms">업로드</button>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_bms" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="BMS" data-stop-url="{{ url_for('stop_bms') }}">중지</button>
                          <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#modal_update_bms">업로드</button>
                        {% endif %}

                    {% elif key == 'AIS VDM Simulator' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_ais_vdm_simulator" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="AIS VDM Simulator" data-stop-url="{{ url_for('stop_ais_vdm_simulator') }}">중지</button>
                          <button class="btn btn-primary btn-sm web-btn" onclick="window.open('http://localhost:8000', '_blank')">WEB</button>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_ais_vdm_simulator" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="AIS VDM Simulator" data-stop-url="{{ url_for('stop_ais_vdm_simulator') }}">중지</button>
                        {% endif %}

                    {% elif key == 'Autopilot Simulator' %}
                        {% if cfg.status == "running" %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_autopilot_simulator" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="Autopilot Simulator" data-stop-url="{{ url_for('stop_autopilot_simulator') }}">중지</button>
                        {% else %}
                          <button class="btn btn-success btn-sm start-btn" data-toggle="modal" data-target="#modal_autopilot_simulator" data-status="{{ cfg.status }}">실행</button>
                          <button class="btn btn-danger btn-sm stop-btn" data-toggle="modal" data-target="#modal_stop_confirm" data-container-name="Autopilot Simulator" data-stop-url="{{ url_for('stop_autopilot_simulator') }}">중지</button>
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    {% if key == 'BMS' %}
                        <button class="btn btn-warning btn-sm" disabled>로그</button>
                    {% elif key == 'AIS VDM Simulator' %}
                        <button class="btn btn-warning btn-sm log-btn"
                          data-container="ais-vdm-simulator"
                          data-status="{{ cfg.status }}">로그</button>
                    {% elif key == 'Autopilot Simulator' %}
                        <button class="btn btn-warning btn-sm log-btn"
                          data-container="autopilot-simulator"
                          data-status="{{ cfg.status }}">Server 로그</button><br>
                        <button class="btn btn-warning btn-sm log-btn mt-1"
                          data-container="autopilot-ui"
                          data-status="{{ cfg.status }}">UI 로그</button>
                    {% elif key == 'Ship Simulator' %}
                        <button class="btn btn-warning btn-sm log-btn"
                          data-container="ship-simulator"
                          data-status="{{ cfg.status }}">로그</button>
                    {% else %}
                        <button class="btn btn-warning btn-sm log-btn"
                          data-container="{% if cfg.container_name %}{{ cfg.container_name }}{% else %}{{ cfg.containers[0].container_name }}{% endif %}"
                          data-status="{{ cfg.status }}">로그</button>
                    {% endif %}
                </td>
            </tr>

        <!-- 환경변수 Modal (컨테이너 이름 클릭 시) -->
        <div class="modal fade" id="envModal_{{ key|replace(' ', '_') }}" tabindex="-1" role="dialog" aria-labelledby="envModalLabel_{{ key|replace(' ', '_') }}" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title" id="envModalLabel_{{ key|replace(' ', '_') }}">환경 변수: {{ key }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
                    <span aria-hidden="true">&times;</span>
                </button>
                </div>
                <div class="modal-body">
                {% if key == 'Ship Simulator' %}
                    <!-- Ship Simulator의 경우 환경변수 + Config 파일 정보 표시 -->
                    <h6>환경변수</h6>
                    {% if cfg.env %}
                        <ul>
                        {% for k, v in cfg.env.items() %}
                            <li><strong>{{ k }}</strong>: {{ v }}</li>
                        {% endfor %}
                        </ul>
                    {% else %}
                        <p>환경변수가 없습니다.</p>
                    {% endif %}

                    <hr>

                    <h6>Configuration Files</h6>
                    <div class="accordion" id="configAccordion_{{ key|replace(' ', '_') }}">
                        <!-- Communication Config -->
                        <div class="card">
                            <div class="card-header" id="commHeading">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#commCollapse" aria-expanded="true" aria-controls="commCollapse">
                                        Communication Config
                                    </button>
                                </h6>
                            </div>
                            <div id="commCollapse" class="collapse" aria-labelledby="commHeading" data-parent="#configAccordion_{{ key|replace(' ', '_') }}">
                                <div class="card-body">
                                    <pre><code>{{ ship_comm_config_json | safe }}</code></pre>
                                </div>
                            </div>
                        </div>

                        <!-- NMEA Talkers Config -->
                        <div class="card">
                            <div class="card-header" id="nmeaHeading">
                                <h6 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#nmeaCollapse" aria-expanded="false" aria-controls="nmeaCollapse">
                                        NMEA Talkers Config
                                    </button>
                                </h6>
                            </div>
                            <div id="nmeaCollapse" class="collapse" aria-labelledby="nmeaHeading" data-parent="#configAccordion_{{ key|replace(' ', '_') }}">
                                <div class="card-body">
                                    <pre><code>{{ ship_nmea_config_json | safe }}</code></pre>
                                </div>
                            </div>
                        </div>

                        <!-- Simulation Config -->
                        <div class="card">
                            <div class="card-header" id="simHeading">
                                <h6 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#simCollapse" aria-expanded="false" aria-controls="simCollapse">
                                        Simulation Config
                                    </button>
                                </h6>
                            </div>
                            <div id="simCollapse" class="collapse" aria-labelledby="simHeading" data-parent="#configAccordion_{{ key|replace(' ', '_') }}">
                                <div class="card-body">
                                    <pre><code>{{ ship_sim_config_json | safe }}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                {% elif key == 'Autopilot Simulator' %}
                    <!-- Autopilot Simulator의 경우 환경변수 + Config 파일 정보 표시 -->
                    <h6>환경변수</h6>
                    {% if cfg.env is not none %}
                        {% if cfg.type == 'group' %}
                        {% for container_name, env in cfg.env.items() %}
                            <h6>{{ 'Server' if container_name == 'autopilot-simulator' else 'UI' }}</h6>
                            {% if env %}
                            <ul>
                                {% for k, v in env.items() %}
                                <li><strong>{{ k }}</strong>: {{ v }}</li>
                                {% endfor %}
                            </ul>
                            {% else %}
                            <p>해당 컨테이너의 환경변수를 조회할 수 없습니다.</p>
                            {% endif %}
                        {% endfor %}
                        {% endif %}
                    {% else %}
                        <p>컨테이너가 실행 중이지 않아 환경변수를 조회할 수 없습니다.</p>
                    {% endif %}

                    <hr>

                    <h6>Configuration Files</h6>
                    <div class="accordion" id="configAccordion_{{ key|replace(' ', '_') }}">
                        <!-- Communication Config -->
                        <div class="card">
                            <div class="card-header" id="autopilotCommHeading">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#autopilotCommCollapse" aria-expanded="true" aria-controls="autopilotCommCollapse">
                                        Communication Config
                                    </button>
                                </h6>
                            </div>
                            <div id="autopilotCommCollapse" class="collapse" aria-labelledby="autopilotCommHeading" data-parent="#configAccordion_{{ key|replace(' ', '_') }}">
                                <div class="card-body">
                                    <pre><code>{{ autopilot_comm_config_json | safe }}</code></pre>
                                </div>
                            </div>
                        </div>

                        <!-- Autopilot Config -->
                        <div class="card">
                            <div class="card-header" id="autopilotConfigHeading">
                                <h6 class="mb-0">
                                    <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#autopilotConfigCollapse" aria-expanded="false" aria-controls="autopilotConfigCollapse">
                                        Autopilot Config
                                    </button>
                                </h6>
                            </div>
                            <div id="autopilotConfigCollapse" class="collapse" aria-labelledby="autopilotConfigHeading" data-parent="#configAccordion_{{ key|replace(' ', '_') }}">
                                <div class="card-body">
                                    <pre><code>{{ autopilot_config_json | safe }}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <!-- 다른 컨테이너들의 기존 환경변수 표시 -->
                    {% if cfg.env is not none %}
                        {% if cfg.type == 'group' %}
                        {% for container_name, env in cfg.env.items() %}
                            <h6>{{ container_name }}</h6>
                            {% if env %}
                            <ul>
                                {% for k, v in env.items() %}
                                <li><strong>{{ k }}</strong>: {{ v }}</li>
                                {% endfor %}
                            </ul>
                            {% else %}
                            <p>해당 컨테이너의 환경변수를 조회할 수 없습니다.</p>
                            {% endif %}
                        {% endfor %}
                        {% else %}
                        {% if cfg.env %}
                            <ul>
                            {% for k, v in cfg.env.items() %}
                                <li><strong>{{ k }}</strong>: {{ v }}</li>
                            {% endfor %}
                            </ul>
                        {% else %}
                            <p>환경변수가 없습니다.</p>
                        {% endif %}
                        {% endif %}
                    {% else %}
                        <p>컨테이너가 실행 중이지 않아 환경변수를 조회할 수 없습니다.</p>
                    {% endif %}
                {% endif %}
                </div>
            </div>
            </div>
        </div>
            {% endfor %}
        </tbody>
    </table>

    <div class="mb-3">
        <a class="btn btn-primary" href="{{ url_for('load_image') }}">이미지 tar 파일 로드</a>
        <a class="btn btn-info ml-2" href="{{ url_for('logs_page') }}">🔍 로그 조회</a>
    </div>
</div>

<!-- 실행용 모달들 -->

<!-- Send ECDIS Modal (RTZ 파일 업로드) -->
<div class="modal fade" id="modal_send_ecdis" tabindex="-1" role="dialog" aria-labelledby="modalSendEcdisLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalSendEcdisLabel">HiNAS Control로 Route 송신</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="sendEcdisForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>안내:</strong> RTZ 파일로 HiNAS Control로 Route를 송신합니다.
                    </div>

                    <!-- 파일 업로드 영역 -->
                    <div class="form-group">
                        <label for="rtzFile">RTZ 파일 선택</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="rtzFile" name="file" accept=".rtz" required>
                            <label class="custom-file-label" for="rtzFile">RTZ 파일을 선택하세요...</label>
                        </div>
                        <small class="form-text text-muted">*.rtz 파일만 업로드 가능합니다.</small>
                    </div>

                    <!-- 드래그 앤 드롭 영역 -->
                    <div class="drop-zone" id="dropZone">
                        <div class="drop-zone-content">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">RTZ 파일을 여기로 드래그하거나 클릭하여 선택하세요</p>
                            <p class="text-muted small">HiNAS Control로 Route를 송신합니다</p>
                        </div>
                    </div>

                    <!-- Current Waypoint ID 설정 -->
                    <div class="form-group mt-3">
                        <label for="currentWaypointId">Current Waypoint ID</label>
                        <input type="number" class="form-control" id="currentWaypointId" name="current_waypoint_id" value="1" min="1">
                        <small class="form-text text-muted">Route 송신 시 시작할 웨이포인트 ID를 설정하세요.</small>
                    </div>

                    <!-- 선택된 파일 정보 -->
                    <div id="fileInfo" class="mt-3" style="display: none;">
                        <div class="alert alert-success">
                            <strong>선택된 파일:</strong> <span id="fileName"></span><br>
                            <strong>파일 크기:</strong> <span id="fileSize"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
                    <button type="submit" class="btn btn-primary" id="sendBtn" disabled>
                        <span class="spinner-border spinner-border-sm" id="sendSpinner" style="display: none;"></span>
                        송신
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- ECDIS Modal (두 이미지 선택 및 환경변수 입력) -->
<div class="modal fade" id="modal_ecdis" tabindex="-1" role="dialog" aria-labelledby="modalEcdisLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
      <form method="post" action="{{ url_for('start_ecdis') }}" style="min-width: 100% !important;">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalEcdisLabel">ECDIS 실행</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <!-- ECDIS 이미지 선택 -->
            <div class="form-group">
              <label for="ecdis_image">ECDIS 이미지</label>
              <select class="form-control" id="ecdis_image" name="ecdis_image">
                {% for img in all_images %}
                  <option value="{{ img }}" {% if img == container_sets['ECDIS'].containers[0].default_image %}selected{% endif %}>{{ img }}</option>
                {% endfor %}
              </select>
              <small class="form-text text-muted">
                기본 이미지: {{ container_sets['ECDIS'].containers[0].default_image }}
              </small>
            </div>
            <!-- Redis 이미지 선택 -->
            <div class="form-group">
              <label for="redis_image">Redis 이미지</label>
              <select class="form-control" id="redis_image" name="redis_image">
                {% for img in all_images %}
                  <option value="{{ img }}" {% if img == container_sets['ECDIS'].containers[1].default_image %}selected{% endif %}>{{ img }}</option>
                {% endfor %}
              </select>
              <small class="form-text text-muted">
                기본 이미지: {{ container_sets['ECDIS'].containers[1].default_image }}
              </small>
            </div>
            <hr>
            <!-- ECDIS 환경변수 입력 필드 -->
            <div class="form-group">
              <label for="ECDIS_RTZ_HINAS_IP">Mock Simulator ECDIS Local IP</label>
              <select class="form-control" name="ECDIS_RTZ_HINAS_IP" id="ECDIS_RTZ_HINAS_IP" required>
                <!-- AJAX로 현재 PC에 설정된 IP 목록이 채워집니다 -->
              </select>
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RTZ_MULTICAST_GROUP">RTZ Multicast Group</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RTZ_MULTICAST_GROUP" id="ECDIS_RTZ_RTZ_MULTICAST_GROUP" value="************">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RTZ_MULTICAST_PORT">RTZ Multicast Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RTZ_MULTICAST_PORT" id="ECDIS_RTZ_RTZ_MULTICAST_PORT" value="60025">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RRT_MULTICAST_GROUP">RRT Multicast Group</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RRT_MULTICAST_GROUP" id="ECDIS_RTZ_RRT_MULTICAST_GROUP" value="***********">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RRT_MULTICAST_PORT">RRT Multicast Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RRT_MULTICAST_PORT" id="ECDIS_RTZ_RRT_MULTICAST_PORT" value="60001">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_ECDIS_SFI">ECDIS SFI</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_ECDIS_SFI" id="ECDIS_RTZ_ECDIS_SFI" value="II0001">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_HINAS_SFI">HiNAS SFI</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_HINAS_SFI" id="ECDIS_RTZ_HINAS_SFI" value="EI0001">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT">Route Transfer Timeout</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT" id="ECDIS_RTZ_ROUTE_TRANSFER_TIMEOUT" value="0.1">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_WORK_PERIOD">Work Period</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_WORK_PERIOD" id="ECDIS_RTZ_WORK_PERIOD" value="0.6">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM">Position Threshold (NM)</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM" id="ECDIS_RTZ_POSITION_DIFFERENCE_THRESHOLD_NM" value="0.01">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RTZ_SCHEMA_PATHS">RTZ Schema Paths (JSON List)</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RTZ_SCHEMA_PATHS" id="ECDIS_RTZ_RTZ_SCHEMA_PATHS" value='["RTZ_Schema_version_1_2.xsd","RTZ_Schema_version_1_0.xsd"]'>
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_APP_PORT">App Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_APP_PORT" id="ECDIS_RTZ_APP_PORT" value="8005">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_RRT_RECEIVE_PORT">RRT Receive Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_RRT_RECEIVE_PORT" id="ECDIS_RTZ_RRT_RECEIVE_PORT" value="8006">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_REDIS_HOST">Redis Host</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_REDIS_HOST" id="ECDIS_RTZ_REDIS_HOST" value="localhost">
            </div>
            <div class="form-group">
              <label for="ECDIS_RTZ_REDIS_PORT">Redis Port</label>
              <input type="text" class="form-control" name="ECDIS_RTZ_REDIS_PORT" id="ECDIS_RTZ_REDIS_PORT" value="6379">
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-success">실행</button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
          </div>
        </div>
      </form>
    </div>
  </div>
  

<!-- BMS Modal -->
<div class="modal fade" id="modal_bms" tabindex="-1" role="dialog" aria-labelledby="modalBmsLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <form method="post" action="{{ url_for('start_bms') }}">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" id="modalBmsLabel">BMS 실행 안내</h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="닫기">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="alert alert-warning">
            <strong>주의:</strong> Host PC에 설정된 IP:PORT로 BMS 시뮬레이터가 실행됩니다.<br>
            HiNAS Control 설정과 일치해야 하오니, 올바른 IP와 Port를 선택해주세요. (예: **********:504)
          </div>
          <div class="form-group">
            <label for="bms_ip_select">실행할 IP 선택</label>
            <select class="form-control" id="bms_ip_select" name="ip" required>
              <!-- AJAX로 현재 PC에 설정된 IP 목록이 채워집니다 -->
            </select>
          </div>
          <div class="form-group">
            <label for="bms_port">Port 번호</label>
            <input type="number" class="form-control" id="bms_port" name="port" value="504" required>
          </div>
          <p>실행 버튼을 누르면 잠시 후 BMS 시뮬레이터 UI가 선택된 IP와 Port로 실행됩니다.</p>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-success">실행</button>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
        </div>
      </div>
    </form>
  </div>
</div>


<!-- BMS 실행 파일 업데이트 모달 -->
<div class="modal fade" id="modal_update_bms" tabindex="-1" role="dialog" aria-labelledby="modalUpdateBmsLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <form method="post" action="{{ url_for('update_bms') }}" enctype="multipart/form-data">
      <div class="modal-content">
        <div class="modal-header bg-info text-white">
          <h5 class="modal-title" id="modalUpdateBmsLabel">BMS 실행 파일 업데이트</h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="닫기">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="bms_file">새 BMS 실행 파일 선택</label>
            <input type="file" class="form-control-file" id="bms_file" name="bms_file" required>
          </div>
          <p class="text-muted">업로드 후 기존 파일은 새 파일로 교체되며, 실행 권한이 자동으로 부여됩니다.</p>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">업데이트</button>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
        </div>
      </div>
    </form>
  </div>
</div>


<!-- AIS VDM Simulator Modal -->
<div class="modal fade" id="modal_ais_vdm_simulator" tabindex="-1" role="dialog" aria-labelledby="modalAisVdmSimulatorLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <form method="post" action="{{ url_for('start_ais_vdm_simulator') }}">
      <div class="modal-content">
        <div class="modal-header bg-success text-white">
          <h5 class="modal-title" id="modalAisVdmSimulatorLabel">AIS VDM Simulator 실행</h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="닫기">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="alert alert-info">
            <strong>정보:</strong> AIS VDM Simulator가 실행되면 http://localhost:8000 에서 웹 인터페이스에 접근할 수 있습니다.
          </div>

          <!-- 이미지 선택 -->
          <div class="form-group">
            <label for="ais_vdm_image">Docker 이미지</label>
            <select class="form-control" id="ais_vdm_image" name="image" required>
              {% if container_sets['AIS VDM Simulator'].image_tags %}
                {% for tag in container_sets['AIS VDM Simulator'].image_tags %}
                  <option value="{{ tag }}">{{ tag }}</option>
                {% endfor %}
              {% else %}
                <option value="ais-vdm:1.0.0">ais-vdm:1.0.0</option>
              {% endif %}
            </select>
          </div>

          <!-- 환경변수 설정 -->
          <h6>환경변수 설정</h6>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="redis_host">Redis Host</label>
                <input type="text" class="form-control" id="redis_host" name="redis_host" value="localhost" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="redis_port">Redis Port</label>
                <input type="text" class="form-control" id="redis_port" name="redis_port" value="6379" required>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="redis_password">Redis Password</label>
                <input type="password" class="form-control" id="redis_password" name="redis_password" value="" placeholder="비어있으면 패스워드 없음">
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="redis_enable_ssl">Redis Enable SSL</label>
                <select class="form-control" id="redis_enable_ssl" name="redis_enable_ssl" required>
                  <option value="0" selected>0 (비활성화)</option>
                  <option value="1">1 (활성화)</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="target_host">Target Host</label>
                <input type="text" class="form-control" id="target_host" name="target_host" value="***********" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="target_port">Target Port</label>
                <input type="text" class="form-control" id="target_port" name="target_port" value="6503" required>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="send_period">Send Period (초)</label>
            <input type="text" class="form-control" id="send_period" name="send_period" value="2" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-success">실행</button>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
        </div>
      </div>
    </form>
  </div>
</div>






<!-- 컨테이너 중지 확인 모달 -->
<div class="modal fade" id="modal_stop_confirm" tabindex="-1" role="dialog" aria-labelledby="modalStopConfirmLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalStopConfirmLabel">컨테이너 중지 확인</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong id="stop_container_name"></strong>을(를) 정말 중지하시겠습니까?
                </div>
                <p class="text-muted">중지된 컨테이너는 다시 실행할 수 있지만, 현재 작업 중인 데이터가 손실될 수 있습니다.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
                <a id="confirm_stop_btn" class="btn btn-danger" href="#">중지</a>
            </div>
        </div>
    </div>
</div>


<!-- Ship Simulator Modal -->
<div class="modal fade" id="modal_ship_simulator" tabindex="-1" role="dialog" aria-labelledby="modalShipSimulatorLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
      <form method="post" action="{{ url_for('start_ship_simulator') }}">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalShipSimulatorLabel">Ship Simulator 실행</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <!-- 탭 네비게이션 -->
            <ul class="nav nav-tabs" id="shipSimulatorTabs" role="tablist">
              <li class="nav-item">
                <a class="nav-link active" id="communication-tab" data-toggle="tab" href="#communication" role="tab">Communication</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" id="simulation-tab" data-toggle="tab" href="#simulation" role="tab">Simulation</a>
              </li>
            </ul>

            <!-- 탭 내용 -->
            <div class="tab-content" id="shipSimulatorTabContent">
              <!-- Communication 탭 -->
              <div class="tab-pane fade show active" id="communication" role="tabpanel">
                <div class="mt-2">
                  <!-- Communication 기본 설정 (한 줄로) -->
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="communication_mode" class="small">Communication Mode</label>
                        <select class="form-control form-control-sm" id="communication_mode" name="communication_mode" required>
                          <option value="multicast" {% if ship_comm_config.communication_mode == 'multicast' %}selected{% endif %}>Multicast</option>
                          <option value="unicast" {% if ship_comm_config.communication_mode == 'unicast' %}selected{% endif %}>Unicast</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <!-- IP 설정 -->
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="ecdis_interface_ip" class="small">ECDIS Interface IP</label>
                        <select class="form-control form-control-sm" id="ecdis_interface_ip" name="ecdis_interface_ip" required>
                          <!-- 옵션은 API 응답으로 채워집니다. -->
                        </select>
                        <small class="form-text text-muted">Multicast Interface IP</small>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="simulator_server_ip" class="small">Simulator Server IP</label>
                        <select class="form-control form-control-sm" id="simulator_server_ip" name="simulator_server_ip" required>
                          <!-- 옵션은 API 응답으로 채워집니다. -->
                        </select>
                        <small class="form-text text-muted">Server IP</small>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="autopilot_target_ip" class="small">Autopilot Target IP</label>
                        <input type="text" class="form-control form-control-sm" id="autopilot_target_ip" name="autopilot_target_ip"
                               value="**********" required>
                        <small class="form-text text-muted">Autopilot Target IP</small>
                      </div>
                    </div>
                  </div>

                  <!-- Multicast 설정 -->
                  <div id="multicast_settings">
                    <h6 class="small">Multicast Settings</h6>
                    <div class="row">
                      <div class="col-md-4">
                        <div class="form-group">
                          <label for="multicast_group_ip" class="small">Group IP</label>
                          <input type="text" class="form-control form-control-sm" id="multicast_group_ip" name="multicast_group_ip"
                                 value="{{ ship_comm_config.multicast.group_ip }}" required>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group">
                          <label for="multicast_port" class="small">Port</label>
                          <input type="number" class="form-control form-control-sm" id="multicast_port" name="multicast_port"
                                 value="{{ ship_comm_config.multicast.port }}" required>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group">
                          <label for="multicast_ttl" class="small">TTL</label>
                          <input type="number" class="form-control form-control-sm" id="multicast_ttl" name="multicast_ttl"
                                 value="{{ ship_comm_config.multicast.ttl }}" required>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Unicast 설정 -->
                  <div id="unicast_settings">
                    <h6 class="small">Unicast Settings</h6>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="form-group">
                          <label for="unicast_target_ip" class="small">Target IP</label>
                          <input type="text" class="form-control form-control-sm" id="unicast_target_ip" name="unicast_target_ip"
                                 value="{{ ship_comm_config.unicast.target_ip }}" required>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="form-group">
                          <label for="unicast_port" class="small">Port</label>
                          <input type="number" class="form-control form-control-sm" id="unicast_port" name="unicast_port"
                                 value="{{ ship_comm_config.unicast.port }}" required>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Server Ports (한 줄로) -->
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="simulator_server_port" class="small">Simulator Server Port</label>
                        <input type="number" class="form-control form-control-sm" id="simulator_server_port" name="simulator_server_port"
                               value="{{ ship_comm_config.simulator_server.port }}" required>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="autopilot_unicast_port" class="small">Autopilot Unicast Port</label>
                        <input type="number" class="form-control form-control-sm" id="autopilot_unicast_port" name="autopilot_unicast_port"
                               value="{{ ship_comm_config.autopilot_unicast.port }}" required>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Simulation 탭 -->
              <div class="tab-pane fade" id="simulation" role="tabpanel">
                <div class="mt-3">
                  <!-- Simulation 기본 설정 (한 줄로) -->
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="ship_type" class="small">Ship Type</label>
                        <select class="form-control form-control-sm" id="ship_type" name="ship_type" required>
                          <option value="1" {% if ship_sim_config.simulation_set.ship_type == 1 %}selected{% endif %}>Type 1</option>
                          <option value="2" {% if ship_sim_config.simulation_set.ship_type == 2 %}selected{% endif %}>Type 2</option>
                          <option value="3" {% if ship_sim_config.simulation_set.ship_type == 3 %}selected{% endif %}>Type 3</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="simulation_speed_up" class="small">Speed Up</label>
                        <input type="number" class="form-control form-control-sm" id="simulation_speed_up" name="simulation_speed_up"
                               value="{{ ship_sim_config.simulation_set.simulation_speed_up }}" min="1" required>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="simulation_mode" class="small">Mode</label>
                        <select class="form-control form-control-sm" id="simulation_mode" name="simulation_mode" required>
                          <option value="normal" {% if ship_sim_config.simulation_set.simulation_mode == 'normal' %}selected{% endif %}>Normal</option>
                          <option value="fast" {% if ship_sim_config.simulation_set.simulation_mode == 'fast' %}selected{% endif %}>Fast</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <!-- Initial Position (컴팩트하게) -->
                  <div class="form-group">
                    <label for="position_input" class="small">Initial Position <span id="position_preview" class="text-primary font-weight-bold">(1°15.935'N 103°57.411'E)</span></label>
                    <div class="row">
                      <div class="col-2">
                        <input type="number" class="form-control form-control-sm" id="lat_deg" name="lat_deg" min="0" max="90" value="1" required>
                        <small class="text-muted">Lat °</small>
                      </div>
                      <div class="col-2">
                        <input type="number" class="form-control form-control-sm" id="lat_min" name="lat_min" min="0" max="59.999" step="0.001" value="15.935" required>
                        <small class="text-muted">Lat '</small>
                      </div>
                      <div class="col-1">
                        <select class="form-control form-control-sm text-center" id="lat_dir" name="lat_dir" required style="font-weight: bold; width: unset;">
                          <option value="N" selected>N</option>
                          <option value="S">S</option>
                        </select>
                      </div>
                      <div class="col-2">
                        <input type="number" class="form-control form-control-sm" id="lon_deg" name="lon_deg" min="0" max="180" value="103" required>
                        <small class="text-muted">Lon °</small>
                      </div>
                      <div class="col-2">
                        <input type="number" class="form-control form-control-sm" id="lon_min" name="lon_min" min="0" max="59.999" step="0.001" value="57.411" required>
                        <small class="text-muted">Lon '</small>
                      </div>
                      <div class="col-1">
                        <select class="form-control form-control-sm text-center" id="lon_dir" name="lon_dir" required style="font-weight: bold; width: unset;">
                          <option value="E" selected>E</option>
                          <option value="W">W</option>
                        </select>
                      </div>
                      <!-- <div class="col-2"> -->
                        <!-- <small class="text-muted">103°57.411'E</small> -->
                      <!-- </div> -->
                    </div>
                  </div>

                  <!-- Other Initial Settings -->
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="initial_speed" class="small">Speed (knots)</label>
                        <input type="number" class="form-control form-control-sm" id="initial_speed" name="initial_speed"
                               value="{{ ship_sim_config.initial.speed }}" step="0.1" required>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="initial_course" class="small">Course (degrees)</label>
                        <input type="number" class="form-control form-control-sm" id="initial_course" name="initial_course"
                               value="{{ ship_sim_config.initial.course }}" min="0" max="360" step="0.1" required>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="initial_lever" class="small">Lever</label>
                        <input type="number" class="form-control form-control-sm" id="initial_lever" name="initial_lever"
                               value="{{ ship_sim_config.initial.lever }}" step="0.1" required>
                      </div>
                    </div>
                  </div>

                  <!-- Environment Settings -->
                  <h6>Environment</h6>
                  <div class="table-responsive">
                    <table class="table table-sm">
                      <thead>
                        <tr>
                          <th>Type</th>
                          <th>Speed/State</th>
                          <th>Direction</th>
                          <th>Enabled</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td><strong>Wind</strong></td>
                          <td>
                            <input type="number" class="form-control form-control-sm" id="wind_speed" name="wind_speed"
                                   value="{{ ship_sim_config.environment.wind.speed }}" step="0.1" required>
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm" id="wind_direction" name="wind_direction"
                                   value="{{ ship_sim_config.environment.wind.direction }}" min="0" max="360" step="0.1" required>
                          </td>
                          <td>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="wind_enabled" name="wind_enabled"
                                     {% if ship_sim_config.environment.wind.enabled %}checked{% endif %}>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Current</strong></td>
                          <td>
                            <input type="number" class="form-control form-control-sm" id="current_speed" name="current_speed"
                                   value="{{ ship_sim_config.environment.current.speed }}" step="0.1" required>
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm" id="current_direction" name="current_direction"
                                   value="{{ ship_sim_config.environment.current.direction }}" min="0" max="360" step="0.1" required>
                          </td>
                          <td>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="current_enabled" name="current_enabled"
                                     {% if ship_sim_config.environment.current.enabled %}checked{% endif %}>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Wave</strong></td>
                          <td>
                            <select class="form-control form-control-sm" id="sea_state" name="sea_state" required>
                              <option value="0" {% if ship_sim_config.environment.wave.sea_state == 0 %}selected{% endif %}>0 - Calm</option>
                              <option value="1" {% if ship_sim_config.environment.wave.sea_state == 1 %}selected{% endif %}>1 - Light</option>
                              <option value="2" {% if ship_sim_config.environment.wave.sea_state == 2 %}selected{% endif %}>2 - Moderate</option>
                              <option value="3" {% if ship_sim_config.environment.wave.sea_state == 3 %}selected{% endif %}>3 - Rough</option>
                              <option value="4" {% if ship_sim_config.environment.wave.sea_state == 4 %}selected{% endif %}>4 - Very Rough</option>
                              <option value="5" {% if ship_sim_config.environment.wave.sea_state == 5 %}selected{% endif %}>5 - High</option>
                            </select>
                          </td>
                          <td>-</td>
                          <td>
                            <div class="form-check">
                              <input class="form-check-input" type="checkbox" id="wave_enabled" name="wave_enabled"
                                     {% if ship_sim_config.environment.wave.enabled %}checked{% endif %}>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <!-- 실행할 이미지 선택 -->
            <div class="form-group mt-3">
              <label for="image_ship_simulator">실행할 이미지</label>
              <select class="form-control" id="image_ship_simulator" name="image">
                  {% for img in all_images %}
                      <option value="{{ img }}" {% if img == container_sets['Ship Simulator'].default_image %}selected{% endif %}>{{ img }}</option>
                  {% endfor %}
              </select>
              <small class="form-text text-muted">
                기본 이미지: {{ container_sets['Ship Simulator'].default_image }}
              </small>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-success">실행</button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
          </div>
        </div>
      </form>
    </div>
  </div>


<!-- 센서 모달 -->
<div class="modal fade" id="modal_nmea_management" tabindex="-1" role="dialog" aria-labelledby="modalNmeaManagementLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalNmeaManagementLabel">NMEA Talkers 관리</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- NMEA 기본 설정 -->
                <div class="row mb-2">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nmea_version" class="small">NMEA Version</label>
                            <input type="text" class="form-control form-control-sm" id="nmea_version" value="{{ ship_nmea_config.nmea_version }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="default_source" class="small">Default Source</label>
                            <input type="text" class="form-control form-control-sm" id="default_source" value="{{ ship_nmea_config.default_source }}">
                        </div>
                    </div>
                </div>

                <!-- NMEA Talkers 목록 -->
                <h6>NMEA Talkers</h6>
                <div id="nmea_talkers_container">
                    <!-- JavaScript로 동적 생성 -->
                </div>

                <!-- 새 Talker 추가 -->
                <div class="mt-2">
                    <h6 class="small mb-2">새 Talker 추가</h6>
                    <div class="row">
                        <div class="col-3">
                            <select class="form-control form-control-sm" id="new_message_type" onchange="toggleCustomMessageType()">
                                <option value="">메시지 타입</option>
                                <option value="GGA">GGA</option>
                                <option value="RMC">RMC</option>
                                <option value="VTG">VTG</option>
                                <option value="THS">THS</option>
                                <option value="HDT">HDT</option>
                                <option value="ROT">ROT</option>
                                <option value="VBW">VBW</option>
                                <option value="ZDA">ZDA</option>
                                <option value="MWV">MWV</option>
                                <option value="DPT">DPT</option>
                                <option value="RSA">RSA</option>
                                <option value="CUSTOM">Custom...</option>
                            </select>
                            <input type="text" class="form-control form-control-sm mt-1" id="custom_message_type" placeholder="Custom Type" style="display: none;">
                        </div>
                        <div class="col-3">
                            <input type="text" class="form-control form-control-sm" id="new_talker_id" placeholder="Talker ID" value="GP">
                        </div>
                        <div class="col-3">
                            <input type="text" class="form-control form-control-sm" id="new_source" placeholder="Source" value="GP0001">
                        </div>
                        <div class="col-3">
                            <button type="button" class="btn btn-success btn-sm btn-block" onclick="addNewTalker()">추가</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-success btn-sm" onclick="saveNmeaConfig()">저장</button>
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">취소</button>
            </div>
        </div>
    </div>
</div>


<!-- Autopilot Simulator Modal -->
<div class="modal fade" id="modal_autopilot_simulator" tabindex="-1" role="dialog" aria-labelledby="modalAutopilotSimulatorLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
      <form method="post" action="{{ url_for('start_autopilot_simulator') }}">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalAutopilotSimulatorLabel">Autopilot Simulator 실행</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <!-- 이미지 선택 섹션 -->
            <h6 class="text-primary">이미지 설정</h6>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="server_image_sim">Server 이미지</label>
                  <select class="form-control" id="server_image_sim" name="server_image">
                      {% for img in all_images %}
                          <option value="{{ img }}" {% if img == container_sets['Autopilot Simulator'].containers[0].default_image %}selected{% endif %}>{{ img }}</option>
                      {% endfor %}
                  </select>
                  <small class="form-text text-muted">기본: {{ container_sets['Autopilot Simulator'].containers[0].default_image }}</small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="ui_image_sim">UI 이미지</label>
                  <select class="form-control" id="ui_image_sim" name="ui_image">
                      {% for img in all_images %}
                          <option value="{{ img }}" {% if img == container_sets['Autopilot Simulator'].containers[1].default_image %}selected{% endif %}>{{ img }}</option>
                      {% endfor %}
                  </select>
                  <small class="form-text text-muted">기본: {{ container_sets['Autopilot Simulator'].containers[1].default_image }}</small>
                </div>
              </div>
            </div>

            <hr>

            <!-- Communication 설정 -->
            <h6 class="text-primary">Communication 설정</h6>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="hinas_server_ip">HiNAS Server IP</label>
                  <input type="text" class="form-control" id="hinas_server_ip" name="hinas_server_ip"
                         value="***********" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="hinas_server_port">HiNAS Server Port</label>
                  <input type="number" class="form-control" id="hinas_server_port" name="hinas_server_port"
                         value="{{ autopilot_comm_config.hinas_server.port }}" required>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="nmea_receiver_ip">NMEA Receiver IP</label>
                  <select class="form-control" id="nmea_receiver_ip" name="nmea_receiver_ip" required>
                    <!-- 옵션은 API 응답으로 채워집니다. -->
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="nmea_receiver_port">NMEA Receiver Port</label>
                  <input type="number" class="form-control" id="nmea_receiver_port" name="nmea_receiver_port"
                         value="{{ autopilot_comm_config.nmea_receiver.port }}" required>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="redis_host_server">Redis Host (Server)</label>
                  <input type="text" class="form-control" id="redis_host_server" name="redis_host_server" value="localhost" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="redis_port_server">Redis Port (Server)</label>
                  <input type="number" class="form-control" id="redis_port_server" name="redis_port_server" value="6379" required>
                </div>
              </div>
            </div>

            <hr>

            <!-- Control Parameters -->
            <h6 class="text-primary">Control Parameters</h6>
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="kp_psi">KP PSI</label>
                  <input type="number" step="0.1" class="form-control" id="kp_psi" name="kp_psi"
                         value="{{ autopilot_config.control_parameters.kp_psi }}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="kd_psi">KD PSI</label>
                  <input type="number" step="0.1" class="form-control" id="kd_psi" name="kd_psi"
                         value="{{ autopilot_config.control_parameters.kd_psi }}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="kp_rot">KP ROT</label>
                  <input type="number" step="0.1" class="form-control" id="kp_rot" name="kp_rot"
                         value="{{ autopilot_config.control_parameters.kp_rot }}" required>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="ki_rot">KI ROT</label>
                  <input type="number" step="0.1" class="form-control" id="ki_rot" name="ki_rot"
                         value="{{ autopilot_config.control_parameters.ki_rot }}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="kff_rot">KFF ROT</label>
                  <input type="number" step="0.1" class="form-control" id="kff_rot" name="kff_rot"
                         value="{{ autopilot_config.control_parameters.kff_rot }}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="rudder_limit">Rudder Limit</label>
                  <input type="number" class="form-control" id="rudder_limit" name="rudder_limit"
                         value="{{ autopilot_config.control_parameters.rudder_limit }}" required>
                </div>
              </div>
            </div>

            <hr>

            <!-- Ship Parameters -->
            <h6 class="text-primary">Ship Parameters</h6>
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="ship_type">Ship Type</label>
                  <input type="number" class="form-control" id="ship_type" name="ship_type"
                         value="{{ autopilot_config.ship_parameters.ship_type }}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="kff_rot_container_negative">KFF ROT Container Negative</label>
                  <input type="number" step="0.01" class="form-control" id="kff_rot_container_negative" name="kff_rot_container_negative"
                         value="{{ autopilot_config.ship_parameters.kff_rot_container_negative }}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="kff_rot_container_positive">KFF ROT Container Positive</label>
                  <input type="number" step="0.01" class="form-control" id="kff_rot_container_positive" name="kff_rot_container_positive"
                         value="{{ autopilot_config.ship_parameters.kff_rot_container_positive }}" required>
                </div>
              </div>
            </div>

            <hr>

            <!-- UI 설정 -->
            <h6 class="text-primary">UI 설정</h6>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="redis_host_sim">Redis Host</label>
                  <input type="text" class="form-control" id="redis_host_sim" name="redis_host" value="localhost" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="redis_port_sim">Redis Port</label>
                  <input type="text" class="form-control" id="redis_port_sim" name="redis_port" value="6379" required>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="display_sim">Display</label>
                  <input type="text" class="form-control" id="display_sim" name="display" value=":0" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="redis_enable_ssl_sim">Redis Enable SSL</label>
                  <select class="form-control" id="redis_enable_ssl_sim" name="redis_enable_ssl">
                    <option value="false" selected>false</option>
                    <option value="true">true</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="libgl_always_software_sim">LIBGL_ALWAYS_SOFTWARE</label>
                  <select class="form-control" id="libgl_always_software_sim" name="libgl_always_software">
                    <option value="1" selected>1 (소프트웨어 렌더링)</option>
                    <option value="0">0 (하드웨어 렌더링)</option>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="libgl_dri3_disable_sim">LIBGL_DRI3_DISABLE</label>
                  <select class="form-control" id="libgl_dri3_disable_sim" name="libgl_dri3_disable">
                    <option value="1" selected>1 (DRI3 비활성화)</option>
                    <option value="0">0 (DRI3 활성화)</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="qt_x11_no_mitshm_sim">QT_X11_NO_MITSHM</label>
                  <input type="text" class="form-control" id="qt_x11_no_mitshm_sim" name="qt_x11_no_mitshm" value="1" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="xdg_runtime_dir_sim">XDG_RUNTIME_DIR</label>
                  <input type="text" class="form-control" id="xdg_runtime_dir_sim" name="xdg_runtime_dir" value="/tmp" required>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-success">실행</button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
          </div>
        </div>
      </form>
    </div>
  </div>
  </div>
    

<!-- 공통 로그 모달 (크게 표시) -->
<div class="modal fade" id="modal_logs" tabindex="-1" role="dialog" aria-labelledby="modalLogsLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalLogsLabel">로그</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="닫기">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
            <pre id="logs_container" style="height:600px; overflow:auto; background-color:#000; color:#0f0; padding:10px;"></pre>
        </div>
      </div>
    </div>
  </div>

<!-- Diagram Modal (diagram.html) -->
{% include "diagram.html" %}

<!-- jQuery, Popper, Bootstrap JS (jQuery full 버전 사용) -->
<script src="{{ url_for('static', path='popper.min.js') }}"></script>
<script src="{{ url_for('static', path='bootstrap.min.js') }}"></script>

<!-- Viz.js + full.render.js (Graphviz) -->
<script src="{{ url_for('static', path='viz.js') }}"></script>
<script src="{{ url_for('static', path='full.render.js') }}"></script>
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/viz.js/2.1.2/viz.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/viz.js/2.1.2/full.render.js"></script> -->

<script>

    $(document).on('submit', '.modal form', function(e) {
        var $modal = $(this).closest('.modal');
        var status = $modal.data('status');  // 모달에 저장된 실행 상태
        if(status === "running"){
            alert("컨테이너가 이미 실행 중입니다. 실행 요청을 보낼 수 없습니다.");
            e.preventDefault();
            return false;
        }
        // 제출 버튼에 스피너 처리 (중복 제출 방지)
        var $submitButton = $(this).find('button[type="submit"]');
        if ($submitButton.prop('disabled')) return false;
        $submitButton.prop('disabled', true);
        var originalText = $submitButton.html();
        $submitButton.html('<span class="spinner-border spinner-border-sm"></span> ' + originalText);
    });

    $(document).on('click', '.start-btn', function(e) {
        var status = $(this).data('status');
        e.preventDefault();
        e.stopImmediatePropagation();
        if (status && status.toLowerCase() === "running") {
            alert("이미 실행 중입니다.");
            return false;
        }
    });



    $(document).on('click', '.start-btn', function(e) {
        var $btn = $(this);
        if ($btn.hasClass('disabled')) return false;

        var $row = $btn.closest('tr');
        // 같은 행의 모든 버튼과 링크를 비활성화
        $row.find('button').prop('disabled', true).addClass('disabled');
        $row.find('a').addClass('disabled');
    });

    // 중지 버튼은 모달을 열기만 하므로 스피너를 시작하지 않음
    $(document).on('click', '.stop-btn', function(e) {
        // 모달만 열고 스피너는 시작하지 않음
        // 실제 중지는 모달에서 확인 후 처리
    });


  
    // 로그 버튼 클릭 시 (변경 없음)
    $('.log-btn').on('click', function(e) {
        var status = $(this).data('status');
        var container = $(this).data('container');
        if(status === "Not Created") {
            alert("컨테이너가 생성되지 않았습니다. 먼저 실행하세요.");
            e.preventDefault();
            return false;
        }
        $('#modalLogsLabel').text("로그 (" + container + ")");
        $('#modal_logs').data('container', container);
        $('#modal_logs').modal('show');
    });
  
    // 로그 모달 열리면 2초마다 로그 갱신
    $('#modal_logs').on('shown.bs.modal', function () {
        var container = $(this).data('container');

        // 최초 모달 열릴 때 기존 로그 비우기
        $('#logs_container').text('로그를 불러오는 중...');

        var logInterval = setInterval(function(){
            $.ajax({
                url: "{{ url_for('logs_legacy', container_name='dummy') }}".replace('dummy', container),
                success: function(data) {
                    $('#logs_container').text(data);
                    $('#logs_container').scrollTop($('#logs_container')[0].scrollHeight);
                }
            });
        }, 2000);

        // 즉시 첫 번째 로그 로드
        $.ajax({
            url: "{{ url_for('logs_legacy', container_name='dummy') }}".replace('dummy', container),
            success: function(data) {
                $('#logs_container').text(data);
                $('#logs_container').scrollTop($('#logs_container')[0].scrollHeight);
            }
        });

        // 모달 닫힐 때 로그 폴링 중단
        $('#modal_logs').on('hide.bs.modal', function () {
            clearInterval(logInterval);
        });
    });


  

    // IP 우선순위 선택 함수
    function selectPreferredIp(data, preferredSubnet, defaultValue) {
        var preferredIp = null;
        var defaultIp = null;

        // 우선순위 IP 찾기 (예: 10.0.70.x)
        $.each(data, function(index, ip) {
            if (ip.startsWith(preferredSubnet)) {
                preferredIp = ip;
                return false; // 첫 번째 매칭 IP에서 중단
            }
            if (ip === defaultValue) {
                defaultIp = ip;
            }
        });

        // 우선순위: 1) 선호 대역 IP, 2) 기본값, 3) 첫 번째 IP
        return preferredIp || defaultIp || (data.length > 0 ? data[0] : null);
    }

    // BMS 모달이 열릴 때 현재 PC의 IP 목록을 가져와 셀렉박스에 채우기
    $('#modal_bms').on('show.bs.modal', function() {
        console.log("BMS 모달이 열렸습니다. IP 목록을 로드합니다.");
        var $select = $('#bms_ip_select');
        $select.empty();

        // 로딩 표시
        $select.append($('<option>', {
            value: "",
            text: "IP 목록을 불러오는 중..."
        }));

        $.ajax({
            url: "/api/local_ips",
            method: "GET",
            dataType: "json",
            cache: false,  // 캐시 방지
            timeout: 5000, // 5초 타임아웃
            success: function(data) {
                $select.empty(); // 로딩 메시지 제거
                console.log("BMS IP 목록 로드 성공:", data);

                if(data && data.length > 0){
                    // 10.0.50.x 대역 우선 선택
                    var selectedIp = selectPreferredIp(data, '10.0.50.', null);

                    $.each(data, function(index, ip) {
                        var isSelected = (ip === selectedIp);
                        $select.append($('<option>', {
                            value: ip,
                            text: ip,
                            selected: isSelected
                        }));
                    });
                } else {
                    $select.append($('<option>', {
                        value: "",
                        text: "설정된 IP가 없습니다."
                    }));
                }
            },
            error: function(xhr, status, error) {
                $select.empty(); // 로딩 메시지 제거
                console.error("BMS IP 목록 로드 실패:", status, error);
                $select.append($('<option>', {
                    value: "",
                    text: "IP 정보를 불러오는데 실패했습니다."
                }));
            }
        });
    });

    // ECDIS 모달이 열릴 때 현재 PC의 IP 목록을 가져와 HiNAS IP 셀렉박스에 채우기
    $('#modal_ecdis').on('show.bs.modal', function() {
        var $select = $('#ECDIS_RTZ_HINAS_IP');
        $select.empty();
        $.ajax({
            url: "/api/local_ips",
            method: "GET",
            dataType: "json",
            success: function(data) {
                if(data.length > 0){
                    // 10.0.60.x 대역 우선 선택 (Ship Simulator와 동일)
                    var selectedIp = selectPreferredIp(data, '10.0.60.', '127.0.0.1');

                    $.each(data, function(index, ip) {
                        var isSelected = (ip === selectedIp);
                        $select.append($('<option>', {
                            value: ip,
                            text: ip,
                            selected: isSelected
                        }));
                    });

                    // 선택된 항목이 없으면 첫 번째 IP를 선택
                    if ($select.find('option:selected').length === 0 && data.length > 0) {
                        $select.find('option:first').prop('selected', true);
                    }
                } else {
                    $select.append($('<option>', {
                        value: "",
                        text: "설정된 IP가 없습니다."
                    }));
                }
            },
            error: function() {
                $select.append($('<option>', {
                    value: "",
                    text: "IP 정보를 불러오는데 실패했습니다."
                }));
            }
        });
    });



    $('#simulation_ip_select').on('change', function() {
        var selectedIP = $(this).val();
        var selectedItem = simulationData.find(function(item) {
            return item.ip === selectedIP;
        });
        if (selectedItem) {
            $('#interface_name').val(selectedItem.interface);
        } else {
            $('#interface_name').val('');
        }
    });
    
    // Diagram 렌더링 (모달 Diagram 부분)
    $(document).ready(function(){
        $('#diagramModal').on('shown.bs.modal', function() {
            renderDiagram();
        });
    });
    
    // RTZ 파일 업로드 및 전송 기능
    $(document).ready(function() {
        let selectedFile = null;

        // 파일 선택 시 처리
        $('#rtzFile').change(function() {
            handleFileSelect(this.files[0]);
        });

        // 커스텀 파일 라벨 업데이트
        $('#rtzFile').change(function() {
            const fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').text(fileName || 'RTZ 파일을 선택하세요...');
        });

        // 드래그 앤 드롭 기능
        const dropZone = $('#dropZone');

        dropZone.click(function() {
            $('#rtzFile').click();
        });

        dropZone.on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });

        dropZone.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });

        dropZone.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');

            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.name.toLowerCase().endsWith('.rtz')) {
                    $('#rtzFile')[0].files = files;
                    $('#rtzFile').trigger('change');
                    handleFileSelect(file);
                } else {
                    alert('RTZ 파일만 업로드 가능합니다.');
                }
            }
        });

        // 파일 선택 처리 함수
        function handleFileSelect(file) {
            if (file && file.name.toLowerCase().endsWith('.rtz')) {
                selectedFile = file;
                $('#fileName').text(file.name);
                $('#fileSize').text((file.size / 1024).toFixed(2) + ' KB');
                $('#fileInfo').show();
                $('#sendBtn').prop('disabled', false);
            } else {
                selectedFile = null;
                $('#fileInfo').hide();
                $('#sendBtn').prop('disabled', true);
                if (file) {
                    alert('RTZ 파일만 업로드 가능합니다.');
                }
            }
        }

        // 폼 제출 처리
        $('#sendEcdisForm').submit(function(e) {
            e.preventDefault();

            if (!selectedFile) {
                alert('RTZ 파일을 선택해주세요.');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('current_waypoint_id', $('#currentWaypointId').val());

            // 로딩 상태 표시
            $('#sendBtn').prop('disabled', true);
            $('#sendSpinner').show();

            $.ajax({
                url: "{{ url_for('send_ecdis') }}",
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                    alert(data.message);
                    $('#modal_send_ecdis').modal('hide');
                    // 폼 리셋
                    resetForm();
                },
                error: function(xhr) {
                    let errorMsg = 'Route 송신 실패';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMsg = response.detail || errorMsg;
                    } catch (e) {
                        errorMsg = xhr.responseText || errorMsg;
                    }
                    alert(errorMsg);
                },
                complete: function() {
                    // 로딩 상태 해제
                    $('#sendBtn').prop('disabled', false);
                    $('#sendSpinner').hide();
                }
            });
        });

        // 모달 닫힐 때 폼 리셋
        $('#modal_send_ecdis').on('hidden.bs.modal', function() {
            resetForm();
        });

        // 폼 리셋 함수
        function resetForm() {
            selectedFile = null;
            $('#sendEcdisForm')[0].reset();
            $('#rtzFile').next('.custom-file-label').text('RTZ 파일을 선택하세요...');
            $('#fileInfo').hide();
            $('#sendBtn').prop('disabled', true);
            $('#currentWaypointId').val(1);
        }
    });

    // Autopilot Simulator 모달이 열릴 때 로컬 IP 목록 가져오기
    $('#modal_autopilot_simulator').on('show.bs.modal', function() {
        console.log('Autopilot Simulator modal opening...');

        // NMEA Receiver IP select box 채우기
        var $nmeaSelect = $('#nmea_receiver_ip');
        var defaultValue = '{{ autopilot_comm_config.nmea_receiver.ip }}';

        $nmeaSelect.empty(); // 기존 옵션 비우기
        $.ajax({
            url: "/api/local_ips",
            method: "GET",
            dataType: "json",
            success: function(data) {
                // 10.0.70.x 대역 우선 선택
                var selectedIp = selectPreferredIp(data, '10.0.70.', defaultValue);

                $.each(data, function(index, ip) {
                    var isSelected = (ip === selectedIp);
                    $nmeaSelect.append($('<option>', {
                        value: ip,
                        text: ip,
                        selected: isSelected
                    }));
                });

                // 선택된 항목이 없으면 첫 번째 항목 선택
                if ($nmeaSelect.find('option:selected').length === 0 && data.length > 0) {
                    $nmeaSelect.find('option:first').prop('selected', true);
                }
            },
            error: function() {
                console.error("로컬 IP 목록을 불러오는데 실패했습니다.");
                // 실패 시 기본값으로 옵션 추가
                $nmeaSelect.append($('<option>', {
                    value: defaultValue,
                    text: defaultValue,
                    selected: true
                }));
            }
        });
    });

    // 중지 확인 모달이 열릴 때 컨테이너 이름과 URL 설정
    $('#modal_stop_confirm').on('show.bs.modal', function(event) {
        var button = $(event.relatedTarget); // 모달을 연 버튼
        var containerName = button.data('container-name'); // 컨테이너 이름
        var stopUrl = button.data('stop-url'); // 중지 URL

        // 모달 내용 업데이트
        $('#stop_container_name').text(containerName);
        $('#confirm_stop_btn').attr('href', stopUrl);

        // 모달에 원래 버튼 정보 저장 (스피너 제어용)
        $('#modal_stop_confirm').data('original-button', button);
    });

    // 모달이 닫힐 때 (취소 시) 아무것도 하지 않음 (스피너가 시작되지 않았으므로)
    $('#modal_stop_confirm').on('hidden.bs.modal', function() {
        // 취소 시에는 아무것도 하지 않음
    });

    // 개선된 중지 기능 - AJAX 방식
    $('#confirm_stop_btn').on('click', function(e) {
        e.preventDefault();

        var stopUrl = $(this).attr('href');
        var $originalBtn = $('#modal_stop_confirm').data('original-button');
        var $row = $originalBtn.closest('tr');
        var containerName = $('#stop_container_name').text();

        // 모달 즉시 닫기
        $('#modal_stop_confirm').modal('hide');

        // 해당 행의 버튼들만 비활성화 (개별 관리)
        var $rowButtons = $row.find('button, a');
        $rowButtons.prop('disabled', true).addClass('disabled');

        // 스피너 시작
        var originalText = $originalBtn.html();
        $originalBtn.html('<span class="spinner-border spinner-border-sm"></span> 중지 중...');

        // AJAX로 중지 요청
        $.ajax({
            url: stopUrl + '?ajax=true',
            method: 'GET',
            timeout: 45000, // 45초 타임아웃 (서버 타임아웃 30초 + 여유시간)
            success: function(response) {
                if (response.success) {
                    // 성공 시 페이지 새로고침으로 상태 업데이트
                    showMessage(response.message, 'success');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    // 실패 시 버튼 상태 복구
                    restoreButtonState($originalBtn, $rowButtons, originalText);
                    showMessage(response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                // 에러 시 버튼 상태 복구
                restoreButtonState($originalBtn, $rowButtons, originalText);

                var errorMsg = '중지 요청 실패';
                if (status === 'timeout') {
                    errorMsg = `${containerName} 중지 시간 초과 (45초). 컨테이너 상태를 확인해주세요.`;
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                } else if (error) {
                    errorMsg = `중지 요청 실패: ${error}`;
                }

                showMessage(errorMsg, 'error');
            }
        });
    });

    // 버튼 상태 복구 함수
    function restoreButtonState($originalBtn, $rowButtons, originalText) {
        $originalBtn.html(originalText);
        $rowButtons.prop('disabled', false).removeClass('disabled');
    }

    // 메시지 표시 함수
    function showMessage(message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        // 기존 알림 제거 후 새 알림 추가
        $('.alert').remove();
        $('body').prepend(alertHtml);

        // 5초 후 자동 제거
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Ship Simulator 모달이 열릴 때 로컬 IP 목록 가져오기 및 위치 파싱
    $('#modal_ship_simulator').on('show.bs.modal', function() {
        console.log('Ship Simulator modal opening...');

        // IP select box들 채우기
        var $ecdisInterfaceIpSelect = $('#ecdis_interface_ip');
        var $simulatorServerIpSelect = $('#simulator_server_ip');
        var $autopilotTargetIpInput = $('#autopilot_target_ip');

        var ecdisDefaultValue = '{{ ship_comm_config.multicast.interface_ip }}';
        var simulatorDefaultValue = '{{ ship_comm_config.simulator_server.ip }}';
        var autopilotDefaultValue = '{{ ship_comm_config.autopilot_unicast.target_ip }}';

        // ECDIS Interface IP와 Simulator Server IP는 로컬 IP 목록에서 선택
        function populateLocalIpSelect($select, defaultValue) {
            $select.empty();
            $.ajax({
                url: "/api/local_ips",
                method: "GET",
                dataType: "json",
                success: function(data) {
                    // 10.0.60.x 대역 우선 선택
                    var selectedIp = selectPreferredIp(data, '10.0.60.', defaultValue);

                    $.each(data, function(index, ip) {
                        var isSelected = (ip === selectedIp);
                        $select.append($('<option>', {
                            value: ip,
                            text: ip,
                            selected: isSelected
                        }));
                    });

                    if ($select.find('option:selected').length === 0 && data.length > 0) {
                        $select.find('option:first').prop('selected', true);
                    }
                },
                error: function() {
                    console.error("로컬 IP 목록을 불러오는데 실패했습니다.");
                    $select.append($('<option>', {
                        value: defaultValue,
                        text: defaultValue,
                        selected: true
                    }));
                }
            });
        }

        // 각 IP 필드 초기화
        populateLocalIpSelect($ecdisInterfaceIpSelect, ecdisDefaultValue);
        populateLocalIpSelect($simulatorServerIpSelect, simulatorDefaultValue);

        // Autopilot Target IP는 강제로 **********로 초기화
        $autopilotTargetIpInput.val('**********');

        // 초기 위치 파싱 (예: "1°15.935'N 103°57.411'E")
        var positionStr = '{{ ship_sim_config.initial.position }}';
        parseAndSetPosition(positionStr);

        // Communication mode에 따른 설정 표시/숨김
        toggleCommunicationSettings();
    });

    // Communication mode 변경 시 설정 표시/숨김
    $('#communication_mode').on('change', function() {
        toggleCommunicationSettings();
    });

    function toggleCommunicationSettings() {
        var mode = $('#communication_mode').val();
        if (mode === 'multicast') {
            $('#multicast_settings').show();
            $('#unicast_settings').hide();
        } else {
            $('#multicast_settings').hide();
            $('#unicast_settings').show();
        }
    }

    function parseAndSetPosition(positionStr) {
        // 정규식으로 위치 파싱: "1°15.935'N 103°57.411'E"
        var regex = /(\d+)°(\d+\.?\d*)'([NS])\s+(\d+)°(\d+\.?\d*)'([EW])/;
        var match = positionStr.match(regex);

        if (match) {
            $('#lat_deg').val(parseInt(match[1]));
            $('#lat_min').val(parseFloat(match[2]));
            $('#lat_dir').val(match[3]);
            $('#lon_deg').val(parseInt(match[4]));
            $('#lon_min').val(parseFloat(match[5]));
            $('#lon_dir').val(match[6]);
        }

        // 실시간 position 업데이트
        updatePositionPreview();
    }

    // Position 필드 변경 시 실시간 업데이트
    $('#lat_deg, #lat_min, #lat_dir, #lon_deg, #lon_min, #lon_dir').on('input change', function() {
        updatePositionPreview();
    });

    function updatePositionPreview() {
        var latDeg = $('#lat_deg').val() || '0';
        var latMin = $('#lat_min').val() || '0.000';
        var latDir = $('#lat_dir').val() || 'N';
        var lonDeg = $('#lon_deg').val() || '0';
        var lonMin = $('#lon_min').val() || '0.000';
        var lonDir = $('#lon_dir').val() || 'E';

        var positionStr = latDeg + '°' + parseFloat(latMin).toFixed(3) + "'" + latDir + ' ' +
                         lonDeg + '°' + parseFloat(lonMin).toFixed(3) + "'" + lonDir;

        $('#position_preview').text('(' + positionStr + ')');
    }

    // NMEA 관리 모달 관련 변수
    var nmeaConfig = {{ ship_nmea_config_json | safe }};

    // NMEA 관리 모달이 열릴 때
    $('#modal_nmea_management').on('show.bs.modal', function() {
        loadNmeaTalkers();
    });

    function loadNmeaTalkers() {
        var container = $('#nmea_talkers_container');
        container.empty();

        for (var messageType in nmeaConfig.talkers) {
            var talkers = nmeaConfig.talkers[messageType];
            var section = $('<div class="card mb-2">');
            var header = $('<div class="card-header py-1">').html(
                '<small class="mb-0"><strong>' + messageType + '</strong> <button type="button" class="btn btn-success btn-sm float-right" style="padding: 2px 8px; font-size: 12px;" onclick="addTalkerToMessage(\'' + messageType + '\')">+</button></small>'
            );
            var body = $('<div class="card-body py-1">');

            talkers.forEach(function(talker, index) {
                var row = $('<div class="row mb-1 align-items-center">');
                row.html(
                    '<div class="col-3">' +
                        '<input type="text" class="form-control form-control-sm talker-id" value="' + talker.talker_id + '" data-message="' + messageType + '" data-index="' + index + '">' +
                    '</div>' +
                    '<div class="col-4">' +
                        '<input type="text" class="form-control form-control-sm talker-source" value="' + talker.source + '" data-message="' + messageType + '" data-index="' + index + '">' +
                    '</div>' +
                    '<div class="col-3">' +
                        '<div class="form-check form-check-inline">' +
                            '<input class="form-check-input talker-enabled" type="checkbox" ' + (talker.enabled ? 'checked' : '') + ' data-message="' + messageType + '" data-index="' + index + '">' +
                            '<label class="form-check-label small">On</label>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-2">' +
                        '<button type="button" class="btn btn-danger btn-sm" style="padding: 1px 6px; font-size: 11px;" onclick="removeTalker(\'' + messageType + '\', ' + index + ')">×</button>' +
                    '</div>'
                );
                body.append(row);
            });

            section.append(header).append(body);
            container.append(section);
        }
    }

    function addTalkerToMessage(messageType) {
        if (!nmeaConfig.talkers[messageType]) {
            nmeaConfig.talkers[messageType] = [];
        }

        nmeaConfig.talkers[messageType].push({
            talker_id: "GP",
            source: "GP0001",
            enabled: true
        });

        loadNmeaTalkers();
    }

    function toggleCustomMessageType() {
        var selectedType = $('#new_message_type').val();
        if (selectedType === 'CUSTOM') {
            $('#custom_message_type').show();
        } else {
            $('#custom_message_type').hide();
        }
    }

    function addNewTalker() {
        var messageType = $('#new_message_type').val();
        var customType = $('#custom_message_type').val();
        var talkerId = $('#new_talker_id').val();
        var source = $('#new_source').val();

        // Custom 타입인 경우 custom input 값 사용
        if (messageType === 'CUSTOM') {
            if (!customType) {
                alert('Custom 메시지 타입을 입력해주세요.');
                return;
            }
            messageType = customType.toUpperCase();
        }

        if (!messageType || !talkerId || !source) {
            alert('모든 필드를 입력해주세요.');
            return;
        }

        if (!nmeaConfig.talkers[messageType]) {
            nmeaConfig.talkers[messageType] = [];
        }

        nmeaConfig.talkers[messageType].push({
            talker_id: talkerId,
            source: source,
            enabled: true
        });

        // 입력 필드 초기화
        $('#new_message_type').val('');
        $('#custom_message_type').val('').hide();
        $('#new_talker_id').val('GP');
        $('#new_source').val('GP0001');

        loadNmeaTalkers();
    }

    function removeTalker(messageType, index) {
        if (confirm(messageType + ' 타입의 talker를 삭제하시겠습니까?')) {
            nmeaConfig.talkers[messageType].splice(index, 1);

            // 빈 배열이면 메시지 타입 자체를 삭제
            if (nmeaConfig.talkers[messageType].length === 0) {
                delete nmeaConfig.talkers[messageType];
            }

            loadNmeaTalkers();
        }
    }

    // 실시간 업데이트를 위한 이벤트 리스너
    $(document).on('input', '.talker-id, .talker-source', function() {
        var messageType = $(this).data('message');
        var index = $(this).data('index');
        var field = $(this).hasClass('talker-id') ? 'talker_id' : 'source';

        nmeaConfig.talkers[messageType][index][field] = $(this).val();
    });

    $(document).on('change', '.talker-enabled', function() {
        var messageType = $(this).data('message');
        var index = $(this).data('index');

        nmeaConfig.talkers[messageType][index].enabled = $(this).is(':checked');
    });

    function saveNmeaConfig() {
        // default_source 업데이트
        nmeaConfig.default_source = $('#default_source').val();

        // 서버에 저장 요청
        $.ajax({
            url: '/api/save_nmea_config',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(nmeaConfig),
            success: function(response) {
                alert('NMEA 설정이 저장되었습니다.');
                $('#modal_nmea_management').modal('hide');
            },
            error: function() {
                alert('NMEA 설정 저장에 실패했습니다.');
            }
        });
    }

  </script>

  <!-- 버전 정보 -->
  <footer class="text-center mt-5 mb-3">
    <small class="text-muted">HiNAS Control Mock Simulator {{ app_version }}</small>
  </footer>

</body>
</html>
