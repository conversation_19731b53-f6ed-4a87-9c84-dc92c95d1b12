version: '3.8'

services:
  init-config:
    image: hinas-control-mock-simulator:v1.0.0-RC4
    command: >
      sh -c '
        set -e
        mkdir -p /host/ship-simulator/config /host/autopilot-simulator/config;

        if [ -z "$(ls -A /host/ship-simulator/config 2>/dev/null)" ]; then
          echo "[init] copy ship-simulator config -> host"
          cp -a /app/ship-simulator/config/. /host/ship-simulator/config/
        fi

        if [ -z "$(ls -A /host/autopilot-simulator/config 2>/dev/null)" ]; then
          echo "[init] copy autopilot-simulator config -> host"
          cp -a /app/autopilot-simulator/config/. /host/autopilot-simulator/config/
        fi

        echo "[init] done"
      '
    volumes:
      - /app/ship-simulator:/host/ship-simulator
      - /app/autopilot-simulator:/host/autopilot-simulator
    restart: "no"

  app:
    image: hinas-control-mock-simulator:v1.0.0-RC4
    network_mode: host
    depends_on:
      init-config:
        condition: service_completed_successfully
    environment:
      - DISPLAY=:0
      - XDG_RUNTIME_DIR=/tmp/runtime-root
      - APP_VERSION=${APP_VERSION:-v1.0.0-RC4}
      - DEFAULT_ECDIS_IMAGE=${DEFAULT_ECDIS_IMAGE:-nas2-ecdis:v0.0.15-furuno}
      - DEFAULT_REDIS_IMAGE=${DEFAULT_REDIS_IMAGE:-redis:7.2.7-alpine}
      - DEFAULT_SHIP_SIMULATOR_IMAGE=${DEFAULT_SHIP_SIMULATOR_IMAGE:-ship-simulator:v1.0.0-RC4}
      - DEFAULT_AUTOPILOT_SERVER_IMAGE=${DEFAULT_AUTOPILOT_SERVER_IMAGE:-autopilot-simulator:v1.0.0-RC4}
      - DEFAULT_AUTOPILOT_UI_IMAGE=${DEFAULT_AUTOPILOT_UI_IMAGE:-autopilot-simulator-ui:v1.0.0-RC4}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /tmp/.X11-unix:/tmp/.X11-unix
      - ./runtime-root:/tmp/runtime-root
      - /app/ship-simulator/config:/app/ship-simulator/config
      - /app/autopilot-simulator/config:/app/autopilot-simulator/config
    restart: unless-stopped