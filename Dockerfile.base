FROM python:3.12-slim

WORKDIR /app

RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    iproute2 \
    libqt5widgets5 \
    libqt5gui5 \
    libqt5core5a \
    wget \
    gdb \
    openssl \
    libssl-dev \
    curl \
    libcurl4-openssl-dev \
 && rm -rf /var/lib/apt/lists/*

RUN mkdir /temp
WORKDIR /temp

RUN wget --no-check-certificate https://github.com/Kitware/CMake/releases/download/v3.24.3/cmake-3.24.3.tar.gz
RUN tar -xzvf cmake-3.24.3.tar.gz
WORKDIR /temp/cmake-3.24.3
RUN ./bootstrap --prefix=/usr --parallel=$(nproc)
RUN make install -j$(nproc)
WORKDIR /temp

RUN wget https://github.com/stephane/libmodbus/releases/download/v3.1.8/libmodbus-3.1.8.tar.gz
RUN tar -zxvf libmodbus-3.1.8.tar.gz
WORKDIR /temp/libmodbus-3.1.8
RUN ./configure && make install -j$(nproc)
WORKDIR /
RUN rm -rf temp

WORKDIR /app

COPY requirements.txt .
RUN pip install --upgrade pip && pip install --no-cache-dir -r requirements.txt

# 헤드리스 환경에서 Qt가 모드를 사용하도록 설정
ENV QT_QPA_PLATFORM=xcb
ENV XDG_RUNTIME_DIR=/tmp/runtime-root
RUN mkdir -p /tmp/runtime-root && chmod 700 /tmp/runtime-root
# ENV QT_DEBUG_PLUGINS=1

RUN echo "LD_LIBRARY_PATH=/lib:/usr/lib:/usr/local/lib" >> /root/.bashrc
RUN echo "/usr/local/lib" >> /etc/ld.so.conf.d/shared-lib.conf
RUN ldconfig

RUN apt-get update && apt-get install -y --no-install-recommends \
    qtbase5-dev \
    qt5-qmake \
    qttools5-dev-tools \
    libgl1-mesa-dev \
    libx11-dev \
    libxcb-xinerama0-dev