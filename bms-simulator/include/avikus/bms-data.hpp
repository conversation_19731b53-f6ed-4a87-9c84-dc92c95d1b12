#ifndef HG_BMS_DATA_HPP
#define HG_BMS_DATA_HPP

#include <memory>
#include <mutex>

namespace Avikus
{
    class BmsData
    {
    public:
        using Ptr = std::shared_ptr<BmsData>;
        enum class BmsMaker
        {
            KONGSBERG,
            NABTESCO
        };

    private:
        std::mutex _lock;
        struct shared_data
        {
            BmsMaker bms_maker = BmsMaker::KONGSBERG;
            float hinas_command_rpm = 0.0;
            float bms_set_rpm = 0.0;
            float bms_actual_rpm = 0.0;
            float setting_min_rpm = 0.0;
            float setting_max_rpm = 0.0;
            bool is_connected = false;
            bool hinas_ready = false;
            bool hinas_active = false;
            bool hinas_abnormal = false;
            bool bms_ready = false;
            bool bms_active = false;
            bool bms_status[7] = {false, false, false, false, false, false, false};
        } _data;

    public:
        inline std::mutex &GetLock() { return _lock; }

        shared_data Initiate()
        {
            _data = shared_data(
                {.hinas_command_rpm = 0.0,
                 .bms_set_rpm = 0.0,
                 .bms_actual_rpm = 0.0,
                 .setting_min_rpm = 0.0,
                 .setting_max_rpm = 0.0,
                 .is_connected = false,
                 .hinas_ready = false,
                 .hinas_active = false,
                 .hinas_abnormal = false,
                 .bms_ready = false,
                 .bms_active = false,
                 .bms_status = {false,
                                false,
                                false,
                                false,
                                false,
                                false,
                                false}});
            return _data;
        }

        const shared_data GetData()
        {
            std::lock_guard<std::mutex> lock(GetLock());
            return _data;
        }
        void SetHinasCommandRpm(float value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.hinas_command_rpm = value;
        }
        void SetBmsSetRpm(float value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.bms_set_rpm = value;
        }
        void SetBmsActualRpm(float value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.bms_actual_rpm = value;
        }
        void SetSettingMinRpm(float value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.setting_min_rpm = value;
        }
        void SetSettingMaxRpm(float value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.setting_max_rpm = value;
        }
        void SetIsConnected(bool value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.is_connected = value;
        }
        void SetHinasReady(bool value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.hinas_ready = value;
        }
        void SetHinasActive(bool value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.hinas_active = value;
        }
        void SetHinasAbnormal(bool value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.hinas_abnormal = value;
        }
        void SetBmsReady(bool value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.bms_ready = value;
        }
        void SetBmsActive(bool value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.bms_active = value;
        }
        void SetBmsStatus(int index, bool value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.bms_status[index] = value;
        }
        void SetBmsMaker(BmsMaker value)
        {
            std::lock_guard<std::mutex> lock(GetLock());
            _data.bms_maker = value;
        }
    }; // class BmsData;
} // namespace Avikus

#endif // HG_BMS_DATA_HPP