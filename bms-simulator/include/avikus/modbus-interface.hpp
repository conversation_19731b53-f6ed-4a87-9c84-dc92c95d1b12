#ifndef HG_MODBUS_INTERFACE_HPP
#define HG_MODBUS_INTERFACE_HPP

#include <cstdint>
#include <mutex>
#include <string>

#include <modbus/modbus.h>
#include <modbus/modbus-tcp.h>

#include <avikus/bms-data.hpp>

namespace Avikus
{
    class ModbusInterface
    {
    private:
        Avikus::BmsData::Ptr _bms_data;
        modbus_t *_ctx;
        std::string _ip;
        int _port;
        uint8_t *_query8;
        uint16_t *_query16;
        std::mutex _lock;

    public:
        ModbusInterface(const std::string &ip, int port, Avikus::BmsData::Ptr bms_data = nullptr);
        ~ModbusInterface();

        inline std::mutex &GetLock() { return _lock; }
        int Connect();

        int GetHinasStatus_0x02();
        int GetHinasCommandRpm_0x04();
        int WriteBmsStatus_0x0F();
        int WriteBmsRpm_0x10();

        const uint8_t *GetQuery8() const { return _query8; }
        const uint16_t *GetQuery16() const { return _query16; }
        void SetQuery8(int index, uint8_t data) { _query8[index] = data; }
        void SetQuery16(int index, uint16_t data) { _query16[index] = data; }

        void Run();
    }; // class ModbusInterface
} // namespace Avikus

#endif // HG_MODBUS_INTERFACE_HPP