#ifndef HG_BMS_SIMULATOR_WINDOW_HPP
#define HG_BMS_SIMULATOR_WINDOW_HPP

#include <qt5/QtWidgets/QMainWindow>
#include <qt5/QtWidgets/QGroupBox>
#include <qt5/QtWidgets/QWidget>

#include <avikus/bms-data.hpp>

namespace Avikus
{
    class BmsSimulatorWindow : public QMainWindow
    {
        Q_OBJECT
    private:
        Avikus::BmsData::Ptr _bms_data;
        float _bms_lever_rpm = 0.0;

    public:
        explicit BmsSimulatorWindow(QWidget *parent = nullptr, Avikus::BmsData::Ptr bms_data = nullptr);
        ~BmsSimulatorWindow();

    private:
        QWidget *createCentralWidget();

        QGroupBox *createLeftPanel();
        QGroupBox *createCenterPanel();
        QGroupBox *createRightPanel();
    };
} // namespace Avikus

#endif // HG_BMS_SIMULATOR_WINDOW_HPP