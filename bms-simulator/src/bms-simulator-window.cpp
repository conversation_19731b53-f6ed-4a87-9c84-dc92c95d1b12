#include <avikus/bms-simulator-window.hpp>

#include <cmath>
#include <iostream>

#include <qt5/QtCore/QTimer>
#include <qt5/QtWidgets/QApplication>
#include <qt5/QtWidgets/QComboBox>
#include <qt5/QtWidgets/QDial>
#include <qt5/QtWidgets/QFrame>
#include <qt5/QtWidgets/QGroupBox>
#include <qt5/QtWidgets/QHBoxLayout>
#include <qt5/QtWidgets/QLabel>
#include <qt5/QtWidgets/QLineEdit>
#include <qt5/QtWidgets/QMainWindow>
#include <qt5/QtWidgets/QPushButton>
#include <qt5/QtWidgets/QSlider>
#include <qt5/QtWidgets/QSpacerItem>
#include <qt5/QtWidgets/QSizePolicy>
#include <qt5/QtWidgets/QVBoxLayout>
#include <qt5/QtWidgets/QWidget>

#include <avikus/bms-data.hpp>

namespace Avikus
{

    BmsSimulatorWindow::BmsSimulatorWindow(QWidget *parent, Avikus::BmsData::Ptr bms_data)
        : QMainWindow(parent), _bms_data(bms_data)
    {
        QWidget *central = createCentralWidget();
        setCentralWidget(central);
    }

    BmsSimulatorWindow::~BmsSimulatorWindow()
    {
    }

    QWidget *BmsSimulatorWindow::createCentralWidget()
    {
        QWidget *central = new QWidget(this);
        QHBoxLayout *main_layout = new QHBoxLayout(central);

        main_layout->addWidget(createLeftPanel());
        main_layout->addWidget(createCenterPanel());
        main_layout->addWidget(createRightPanel());

        return central;
    }

    QGroupBox *BmsSimulatorWindow::createLeftPanel()
    {
        QGroupBox *left_group = new QGroupBox("Status", this);
        QVBoxLayout *left_layout = new QVBoxLayout(left_group);

        QStringList bmsStatusList = {
            "ME - Ready",
            "HiNAS RPM Validated",
            "HiNAS RPM Matching WhLever",
            "HiNAS System Ready",
            "HiNAS Automode Act",
            "HiNAS Normal Operation",
            "HiNAS Communication OK"};

        QVector<QLabel *> status_labels;

        for (const auto &status : bmsStatusList)
        {
            QLabel *label = new QLabel(status, left_group);
            if (_bms_data->GetData().bms_status[bmsStatusList.indexOf(status)])
            {
                label->setStyleSheet("background-color: green");
            }
            else
            {
                label->setStyleSheet("background-color: none");
            }
            left_layout->addWidget(label);
            status_labels.append(label);
        }

        QFrame *line = new QFrame(left_group);
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);
        left_layout->addWidget(line);

        QTimer *timer = new QTimer(left_group);

        QPushButton *activate_button = new QPushButton("HiNAS Activate", left_group);
        QPushButton *deactivate_button = new QPushButton("HiNAS Deactivate", left_group);

        left_layout->addWidget(activate_button);
        left_layout->addWidget(deactivate_button);

        connect(activate_button, &QPushButton::clicked, [this]()
                { _bms_data->SetBmsActive(true); });

        connect(deactivate_button, &QPushButton::clicked, [this]()
                { _bms_data->SetBmsActive(false); });

        connect(timer, &QTimer::timeout, [this, activate_button, deactivate_button]()
                {
            const auto data = _bms_data->GetData();
            bool is_activate_available = true;
            for (const auto status : data.bms_status)
            {
                if (!status)
                {
                    is_activate_available = false;
                    break;
                }
            }
            activate_button->setEnabled(is_activate_available && !data.bms_active);
            deactivate_button->setEnabled(data.bms_active); });

        QLineEdit *min_rpm = new QLineEdit(left_group);
        min_rpm->setPlaceholderText("Min RPM");
        QLineEdit *max_rpm = new QLineEdit(left_group);
        max_rpm->setPlaceholderText("Max RPM");

        left_layout->addWidget(min_rpm);
        left_layout->addWidget(max_rpm);

        connect(min_rpm, &QLineEdit::editingFinished, [this, min_rpm]()
                {
            bool ok;
            float value = min_rpm->text().toFloat(&ok);
            if (ok && !_bms_data->GetData().bms_active)
            {
                _bms_data->SetSettingMinRpm(value);
            } });

        connect(max_rpm, &QLineEdit::editingFinished, [this, max_rpm]()
                {
            bool ok;
            float value = max_rpm->text().toFloat(&ok);
            if (ok && !_bms_data->GetData().bms_active)
            {
                _bms_data->SetSettingMaxRpm(value);
            } });

        QComboBox *bms_maker = new QComboBox(left_group);
        bms_maker->addItem("Kongsberg");
        bms_maker->addItem("Nabtesco");

        connect(bms_maker, &QComboBox::currentTextChanged, [this, bms_maker]()
                {
            if (bms_maker->currentText() == "Nabtesco")
            {
                _bms_data->SetBmsMaker(Avikus::BmsData::BmsMaker::NABTESCO);
            }
            else
            {
                _bms_data->SetBmsMaker(Avikus::BmsData::BmsMaker::KONGSBERG);
            } });

        left_layout->addWidget(bms_maker);
        left_layout->addStretch();

        connect(timer, &QTimer::timeout, [this, status_labels]()
                {
            const auto data = _bms_data->GetData();

            data.bms_actual_rpm != 0 ? _bms_data->SetBmsStatus(0, true) : _bms_data->SetBmsStatus(0, false);
            data.hinas_command_rpm >= data.setting_min_rpm && data.hinas_command_rpm <= data.setting_max_rpm ? _bms_data->SetBmsStatus(1, true) : _bms_data->SetBmsStatus(1, false);
            fabs( data.hinas_command_rpm - _bms_lever_rpm) <= 10.0 ? _bms_data->SetBmsStatus(2, true) : _bms_data->SetBmsStatus(2, false);
            data.hinas_ready ? _bms_data->SetBmsStatus(3, true) : _bms_data->SetBmsStatus(3, false);
            data.hinas_active ? _bms_data->SetBmsStatus(4, true) : _bms_data->SetBmsStatus(4, false);
            !data.hinas_abnormal ? _bms_data->SetBmsStatus(5, true) : _bms_data->SetBmsStatus(5, false);
            data.is_connected ? _bms_data->SetBmsStatus(6, true) : _bms_data->SetBmsStatus(6, false);

            const auto updated_data = _bms_data->GetData();

            if (updated_data.bms_status[0] &&
                updated_data.bms_status[1] &&
                updated_data.bms_status[2])
            {
                _bms_data->SetBmsReady(true);
            }
            else
            {
                _bms_data->SetBmsReady(false);
            }

            if ((updated_data.bms_active && !updated_data.bms_status[4]))
            {
                _bms_data->SetBmsActive(false);
            }

            for (int i = 0; i < status_labels.size(); ++i)
            {
                if (updated_data.bms_status[i])
                {
                    status_labels[i]->setStyleSheet("background-color: green");
                }
                else
                {
                    status_labels[i]->setStyleSheet("background-color: none");
                }
            } });
        timer->start(50);

        return left_group;
    }

    QGroupBox *BmsSimulatorWindow::createCenterPanel()
    {
        QGroupBox *center_group = new QGroupBox("Center Controls", this);
        QVBoxLayout *center_layout = new QVBoxLayout(center_group);

        enum class SliderType
        {
            DISABLE,
            ENABLE
        };

        QSlider *hinas_slider = nullptr;
        QLabel *hinas_label = nullptr;
        QSlider *lever_slider = nullptr;
        QLabel *lever_label = nullptr;
        QSlider *setpoint_slider = nullptr;
        QLabel *setpoint_label = nullptr;

        auto createSliderColumn = [&](const QString &title, double init_value, SliderType type,
                                      QSlider **slider_out = nullptr, QLabel **label_out = nullptr) -> QWidget *
        {
            QWidget *widget = new QWidget;
            QVBoxLayout *vbox = new QVBoxLayout(widget);

            QLabel *top_label = new QLabel(title, widget);
            top_label->setAlignment(Qt::AlignCenter);
            vbox->addWidget(top_label);

            QSlider *slider = new QSlider(Qt::Vertical, widget);
            slider->setRange(-1500, 1500);
            slider->setValue(static_cast<int>(init_value * 10));
            slider->setTickPosition(QSlider::TicksBothSides);
            slider->setTickInterval(10);
            slider->setEnabled(type == SliderType::ENABLE);
            vbox->addWidget(slider, 1, Qt::AlignHCenter);

            QLabel *bottom_label = new QLabel(QString::number(init_value, 'f', 1), widget);
            bottom_label->setAlignment(Qt::AlignCenter);
            vbox->addWidget(bottom_label);

            connect(slider, &QSlider::valueChanged, [bottom_label, title, this](int value)
                    { 
                        if (title == "Lever")
                        {
                            _bms_lever_rpm = value / 10.0;
                        }
                        bottom_label->setText(QString::number(value / 10.0, 'f', 1)); });

            if (slider_out)
                *slider_out = slider;
            if (label_out)
                *label_out = bottom_label;

            return widget;
        };

        QWidget *hinas_widget = createSliderColumn("HiNAS", 0.0, SliderType::DISABLE, &hinas_slider, &hinas_label);
        QWidget *lever_widget = createSliderColumn("Lever", 0.0, SliderType::ENABLE, &lever_slider, &lever_label);
        QWidget *setpoint_widget = createSliderColumn("Setpoint", 0.0, SliderType::DISABLE, &setpoint_slider, &setpoint_label);

        center_layout->addWidget(hinas_widget);
        center_layout->addWidget(lever_widget);
        center_layout->addWidget(setpoint_widget);

        QTimer *timer = new QTimer(center_group);
        connect(timer, &QTimer::timeout, [this, hinas_slider, hinas_label, lever_slider, lever_label, setpoint_slider, setpoint_label]()
                {
            const auto data = _bms_data->GetData();
            if (hinas_slider)
            {
                hinas_slider->setValue(data.hinas_command_rpm * 10);
                if (hinas_label)
                    hinas_label->setText(QString::number(data.hinas_command_rpm, 'f', 1));
            }

            if (lever_slider)
            {
                lever_slider->setEnabled(!data.bms_active);
                if (data.bms_active)
                {
                    if(fabs(data.hinas_command_rpm - _bms_lever_rpm) > 0.1)
                    {
                        if (data.hinas_command_rpm < _bms_lever_rpm)
                        {
                            _bms_lever_rpm -= 0.1;
                        }
                        else
                        {
                            _bms_lever_rpm += 0.1;
                        }
                    }
                    else if (data.hinas_command_rpm != _bms_lever_rpm)
                    {
                        _bms_lever_rpm = data.hinas_command_rpm;
                    }
                }

                if (lever_label)
                    lever_label->setText(QString::number(_bms_lever_rpm, 'f', 1));
            }

            if (fabs(data.bms_set_rpm - _bms_lever_rpm) > 0.1)
            {
                if (data.bms_set_rpm < _bms_lever_rpm)
                {
                    _bms_data->SetBmsSetRpm(data.bms_set_rpm + 0.1);
                }
                else
                {
                    _bms_data->SetBmsSetRpm(data.bms_set_rpm - 0.1);
                }
            }
            else if (data.bms_set_rpm != _bms_lever_rpm)
            {
                _bms_data->SetBmsSetRpm(_bms_lever_rpm);
            }

            const auto updated_data = _bms_data->GetData();
            
            if (setpoint_slider)
            {
                setpoint_slider->setValue(updated_data.bms_set_rpm * 10);
                if (setpoint_label)
                    setpoint_label->setText(QString::number(updated_data.bms_set_rpm, 'f', 1));
            } });

        timer->start(20);

        return center_group;
    }

    QGroupBox *BmsSimulatorWindow::createRightPanel()
    {
        QGroupBox *right_group = new QGroupBox("ME RPM", this);
        QVBoxLayout *right_layout = new QVBoxLayout(right_group);

        QDial *guage_dial = new QDial(right_group);
        guage_dial->setMinimum(-1500);
        guage_dial->setMaximum(1500);
        guage_dial->setValue(0);
        guage_dial->setNotchesVisible(true);
        guage_dial->setFixedSize(150, 120);
        guage_dial->setDisabled(true);

        QLabel *rpm_label = new QLabel("0.0 RPM", right_group);
        rpm_label->setAlignment(Qt::AlignCenter);

        QTimer *timer = new QTimer(right_group);
        connect(timer, &QTimer::timeout, [this, guage_dial, rpm_label]()
                { 
            const auto data = _bms_data->GetData();
            if (std::fabs(data.bms_actual_rpm - data.bms_set_rpm) > 0.1)
            {
                if (data.bms_actual_rpm < data.bms_set_rpm)
                {
                    _bms_data->SetBmsActualRpm(data.bms_actual_rpm + 0.1);
                }
                else
                {
                    _bms_data->SetBmsActualRpm(data.bms_actual_rpm - 0.1);
                }   
            }
            else if(data.bms_actual_rpm != data.bms_set_rpm)
            {
                _bms_data->SetBmsActualRpm(data.bms_set_rpm);
            }
            const auto updated_data = _bms_data->GetData();
            guage_dial->setValue(updated_data.bms_actual_rpm*10);
            rpm_label->setText(QString::number(updated_data.bms_actual_rpm, 'f', 1) + " RPM"); });

        timer->start(20);

        right_layout->addWidget(guage_dial, 0, Qt::AlignHCenter);
        right_layout->addWidget(rpm_label);
        right_layout->addStretch();

        return right_group;
    }

} // namespace Avikus