#include <qt5/QtWidgets/QApplication>

#include <memory>
#include <iostream>
#include <string>
#include <thread>

#include <avikus/bms-data.hpp>
#include <avikus/bms-simulator-window.hpp>
#include <avikus/modbus-interface.hpp>

int main(int argc, char *argv[])
{
    std::string target_ip = "***********";
    int port = 0;

    for (int i = 0; i < argc; i++)
    {
        if (i == 2)
        {
            port = std::stoi(argv[i]);
        }
    }

    Avikus::BmsData::Ptr bms_data = std::make_shared<Avikus::BmsData>();
    bms_data->Initiate();

    std::thread simulator_window_thread(
        [&bms_data, &argc, argv]()
        {
            QApplication app(argc, argv);

            Avikus::BmsSimulatorWindow bms_simulator(nullptr, bms_data);
            bms_simulator.setWindowTitle("BMS Simulator");
            bms_simulator.resize(1000, 600);
            bms_simulator.show();

            app.exec(); });

    std::thread modbus_interface_thread(
        [&bms_data, target_ip, port]()
        {
            std::cout << "ip : " << target_ip << " , port : " << port << std::endl;
            Avikus::ModbusInterface modbus_interface(target_ip, port, bms_data);
            modbus_interface.Run();
        });

    simulator_window_thread.join();
    modbus_interface_thread.join();

    return 0;
}