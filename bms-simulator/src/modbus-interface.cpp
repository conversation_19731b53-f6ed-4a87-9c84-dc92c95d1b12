#include <avikus/modbus-interface.hpp>

#include <chrono>
#include <cstdint>
#include <mutex>
#include <iostream>
#include <iterator>
#include <stdexcept>
#include <string>
#include <thread>

#include <modbus/modbus.h>
#include <modbus/modbus-tcp.h>

using std::begin;
using std::end;

namespace Avikus
{
    ModbusInterface::ModbusInterface(
        const std::string &ip, int port, Avikus::BmsData::Ptr bms_data)
        : _bms_data(bms_data),
          _ip(ip),
          _port(port),
          _ctx(nullptr),
          _query8((uint8_t *)calloc(MODBUS_TCP_MAX_ADU_LENGTH, sizeof(uint8_t))),
          _query16((uint16_t *)calloc(MODBUS_TCP_MAX_ADU_LENGTH / 2, sizeof(uint16_t)))
    {
        _ctx = modbus_new_tcp(_ip.c_str(), _port);
        modbus_set_slave(_ctx, 1);
        if (_ctx == nullptr)
        {
            free(_query8);
            free(_query16);
            throw std::runtime_error("Unable to allocate libmodbus context");
        }
    }

    ModbusInterface::~ModbusInterface()
    {
        free(_query8);
        free(_query16);
        if (_ctx)
        {
            modbus_close(_ctx);
            modbus_free(_ctx);
        }
    }

    int ModbusInterface::Connect()
    {
        int return_code = modbus_connect(_ctx);
        if (return_code == -1)
        {
            std::cout << "Unable to connect to modbus server" << std::endl;
        }
        return return_code;
    }

    int ModbusInterface::GetHinasStatus_0x02()
    {
        std::cout << "\033[1;33m" << "Get HiNAS Status 0x02" << "\033[0m" << std::endl;
        if (_bms_data->GetData().bms_maker == Avikus::BmsData::BmsMaker::KONGSBERG)
        {
            modbus_flush(_ctx);
        }
        int return_code = modbus_read_input_bits(_ctx, 0x00, 0x03, _query8);

        for (int i = 0; i < 3; i++)
        {
            std::cout << i << " query value : " << _query8[i] << std::endl;
        }
        if (return_code == -1)
        {
            std::cout << "\033[1;31m" << "Read input bits failed: " + std::string(modbus_strerror(errno)) << "\033[0m" << std::endl;
        }
        return return_code;
    }

    int ModbusInterface::GetHinasCommandRpm_0x04()
    {
        std::cout << "\033[1;33m" << "Get HiNAS Command RPM 0x04" << "\033[0m" << std::endl;
        if (_bms_data->GetData().bms_maker == Avikus::BmsData::BmsMaker::KONGSBERG)
        {
            modbus_flush(_ctx);
        }
        int return_code = modbus_read_input_registers(_ctx, 0x00, 0x01, _query16);

        for (int i = 0; i < 1; i++)
        {
            std::cout << i << " query value : " << _query16[i] << std::endl;
        }
        if (return_code == -1)
        {
            std::cout << "\033[1;31m" << "Read input registers failed: " + std::string(modbus_strerror(errno)) << "\033[0m" << std::endl;
        }
        return return_code;
    }

    int ModbusInterface::WriteBmsStatus_0x0F()
    {
        std::cout << "\033[1;33m" << "Write Bms Status 0x0F" << "\033[0m" << std::endl;
        if (_bms_data->GetData().bms_maker == Avikus::BmsData::BmsMaker::KONGSBERG)
        {
            modbus_flush(_ctx);
        }
        int return_code = modbus_write_bits(_ctx, 0x64, 0x02, _query8);

        for (int i = 0; i < 2; i++)
        {
            std::cout << i << " query value : " << _query8[i] << std::endl;
        }
        if (return_code == -1)
        {
            std::cout << "\033[1;31m" << "Write bits failed: " + std::string(modbus_strerror(errno)) << "\033[0m" << std::endl;
        }
        return return_code;
    }

    int ModbusInterface::WriteBmsRpm_0x10()
    {
        std::cout << "\033[1;33m" << "Write Bms Rpm 0x10" << "\033[0m" << std::endl;
        if (_bms_data->GetData().bms_maker == Avikus::BmsData::BmsMaker::KONGSBERG)
        {
            modbus_flush(_ctx);
        }
        int return_code = modbus_write_registers(_ctx, 0x64, 0x02, _query16);

        for (int i = 0; i < 2; i++)
        {
            std::cout << i << " query value : " << _query16[i] << std::endl;
        }
        if (return_code == -1)
        {
            std::cout << "\033[1;31m" << "Write registers failed: " + std::string(modbus_strerror(errno)) << "\033[0m" << std::endl;
        }
        return return_code;
    }

    void ModbusInterface::Run()
    {
        while (!_bms_data->GetData().is_connected && Connect() == -1)
        {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        while (true)
        {
            if (!_bms_data->GetData().is_connected && Connect() == -1)
            {
                _bms_data->SetIsConnected(false);
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }
            else
            {
                _bms_data->SetIsConnected(true);
            }
            int return_code = GetHinasStatus_0x02();
            if (return_code != -1)
            {
                _bms_data->SetHinasReady(static_cast<bool>(_query8[0]));
                _bms_data->SetHinasActive(static_cast<bool>(_query8[1]));
                _bms_data->SetHinasAbnormal(static_cast<bool>(_query8[2]));
            }
            else
            {
                std::cout << "\033[1;31m" << "Failed to get HiNAS status" << "\033[0m" << std::endl;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            return_code = GetHinasCommandRpm_0x04();
            if (return_code != -1)
            {
                _bms_data->SetHinasCommandRpm(static_cast<float>(_query16[0]) / 10.0f);
            }
            else
            {
                std::cout << "\033[1;31m" << "Failed to get HiNAS command RPM" << "\033[0m" << std::endl;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            const auto data = _bms_data->GetData();
            {
                SetQuery8(0, data.bms_ready);
                SetQuery8(1, data.bms_active);
            }
            return_code = WriteBmsStatus_0x0F();
            if (return_code != -1)
            {
                std::cout << "BMS status written successfully" << std::endl;
            }
            else
            {
                std::cout << "\033[1;31m" << "Failed to write BMS status" << "\033[0m" << std::endl;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            {
                SetQuery16(0, static_cast<uint16_t>(data.bms_set_rpm * 10));
                SetQuery16(1, static_cast<uint16_t>(data.bms_actual_rpm * 10));
            }
            return_code = WriteBmsRpm_0x10();
            if (return_code != -1)
            {
                std::cout << "BMS RPM written successfully" << std::endl;
            }
            else
            {
                std::cout << "\033[1;31m" << "Failed to write BMS RPM" << "\033[0m" << std::endl;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }
} // namespace Avikus
