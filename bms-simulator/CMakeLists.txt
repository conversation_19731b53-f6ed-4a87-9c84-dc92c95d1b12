cmake_minimum_required(VERSION 3.24.0)

# ==== Project Name & Version ====
project(BMS-SIMULATOR VERSION 0.0.1)

# ==== Set Module Informations ====
set(MODULE_NAME bms-simulator)
file(GLOB_RECURSE LIB_SOURCES
    src/modbus-interface.cpp
    src/bms-simulator-window.cpp
)

file(GLOB_RECURSE LIB_HEADERS
    include/avikus/*.hpp
)

# ==== C/C++ Standard ====
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED True)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_AUTOMOC ON)

add_library(${MODULE_NAME}-lib STATIC ${LIB_SOURCES} ${LIB_HEADERS})

# ==== Prerequisites ====
find_package(Qt5 COMPONENTS Widgets REQUIRED)

# ==== Include Directories ====
target_include_directories(${MODULE_NAME}-lib 
    PUBLIC 
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# ==== Compile Options ====
target_compile_options(${MODULE_NAME}-lib
    PUBLIC
        -pthread
        -fPIC
        -O3
        -g
)

# ==== Library Links ====
target_link_libraries(${MODULE_NAME}-lib
    PUBLIC
        Qt5::Widgets
        modbus
)

# ==== Linker Options ====
target_link_options(${MODULE_NAME}-lib
    PUBLIC
        -pthread
        -fPIC
        -O3
        -g
)


# ==== Define Executable ====
add_executable(${MODULE_NAME} src/main.cpp)

target_link_libraries(${MODULE_NAME} 
    PRIVATE
        ${MODULE_NAME}-lib
)