# HiNAS Control Mock Simulator

HiNAS 제어 시스템을 위한 Mock Simulator 웹 애플리케이션입니다.

## 주요 기능

- **ECDIS**: 전자해도시스템 시뮬레이터 (ECDIS + Redis)
- **Ship Simulator**: 선박 시뮬레이터
- **Autopilot Simulator**: 자동조타 시뮬레이터 (Server + UI)
- **BMS**: BMS 시뮬레이터

## 환경변수 설정

### 제품 버전
- `APP_VERSION`: 애플리케이션 버전 (기본값: v1.0.0)

### 기본 이미지 설정
- `DEFAULT_ECDIS_IMAGE`: ECDIS 이미지 (기본값: nas2-ecdis:v0.0.15-furuno)
- `DEFAULT_REDIS_IMAGE`: Redis 이미지 (기본값: redis-7.2.7-alpine)
- `DEFAULT_SHIP_SIMULATOR_IMAGE`: Ship Simulator 이미지 (기본값: ship-simulator:v0.1)
- `DEFAULT_AUTOPILOT_SERVER_IMAGE`: Autopilot Server 이미지 (기본값: autopilot-simulator:v0.0.1)
- `DEFAULT_AUTOPILOT_UI_IMAGE`: Autopilot UI 이미지 (기본값: autopilot-ui:v0.4)

## 실행 방법

### Docker Compose 사용 (권장)

```bash
# 기본 설정으로 실행
docker-compose up -d
```

### 직접 실행

```bash
# Python 가상환경 설정
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 의존성 설치
pip install -r requirements.txt

# 실행
uvicorn app:app --host 0.0.0.0 --port 8000
```

## 사용 예시

### 1. 기본 설정 사용
```bash
docker-compose up -d
```

### 2. 버전 변경
```bash
# .env 파일에서 APP_VERSION=v1.1.0 으로 수정 후
docker-compose up -d
```

### 3. 특정 이미지 변경
```bash
# .env 파일에서 다음과 같이 수정:
# DEFAULT_AUTOPILOT_SERVER_IMAGE=autopilot-simulator:v0.0.2
# DEFAULT_AUTOPILOT_UI_IMAGE=autopilot-ui:v0.5
docker-compose up -d
```

### 4. 런타임 오버라이드
```bash
# 환경변수로 직접 오버라이드
ECDIS_IMAGE=my-custom-ecdis:v1.0.0 docker-compose up -d
```

## 접속 정보

- **웹 UI**: http://localhost:7778
- **이미지 관리**: http://localhost:7778/images
- **시스템 다이어그램**: http://localhost:7778 (Diagram 버튼)

## 파일 구조

```
.
├── app.py                 # 메인 애플리케이션
├── docker-compose.yaml    # Docker Compose 설정
├── .env                   # 환경변수 설정
├── .env.example          # 환경변수 예시
├── templates/            # HTML 템플릿
├── static/              # 정적 파일
├── autopilot-simulator/ # Autopilot 시뮬레이터
└── ship-simulator/      # Ship 시뮬레이터
```
