#!/usr/bin/env python3
"""
Utility script to convert MATLAB .mat wind coefficient files to CSV format.
This script can be used to migrate from .mat files to the new CSV-based system.
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd

try:
    import scipy.io as sio
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("Warning: scipy not available. Cannot convert .mat files.")


def convert_mat_to_csv(mat_file: str, variable_name: str, output_csv: str, 
                      ship_name: str = "Unknown") -> bool:
    """
    Convert MATLAB .mat file to CSV format.
    
    Args:
        mat_file: Path to .mat file
        variable_name: Variable name in .mat file
        output_csv: Output CSV file path
        ship_name: Ship name for header comment
        
    Returns:
        True if successful, False otherwise
    """
    if not SCIPY_AVAILABLE:
        print("Error: scipy is required to convert .mat files")
        return False
    
    try:
        # Load MATLAB file
        print(f"Loading {mat_file}...")
        mat_data = sio.loadmat(mat_file)
        
        if variable_name not in mat_data:
            print(f"Error: Variable '{variable_name}' not found in {mat_file}")
            print(f"Available variables: {list(mat_data.keys())}")
            return False
        
        wind_coeff = mat_data[variable_name]
        print(f"Loaded wind coefficient array with shape: {wind_coeff.shape}")
        
        # Validate array format
        if len(wind_coeff.shape) != 2 or wind_coeff.shape[1] < 3:
            print(f"Error: Expected 2D array with at least 3 columns, got shape {wind_coeff.shape}")
            return False
        
        # Create angle array if not present
        if wind_coeff.shape[1] == 3:
            # Assume angles from 0 to 360 in equal steps
            num_points = wind_coeff.shape[0]
            angles = np.linspace(0, 360, num_points, endpoint=True)
            # Combine angles with coefficients
            wind_coeff_with_angles = np.column_stack([angles, wind_coeff])
        else:
            # Assume first column is angles
            wind_coeff_with_angles = wind_coeff
        
        # Create DataFrame
        df = pd.DataFrame(wind_coeff_with_angles, columns=['angle', 'Cx', 'Cy', 'Cn'])
        
        # Create header
        header = f"# {ship_name} Wind Coefficients\n"
        header += "# Angle (degrees), Cx (longitudinal), Cy (lateral), Cn (yaw moment)\n"
        header += f"# Converted from {os.path.basename(mat_file)}\n"
        
        # Write CSV file
        print(f"Writing to {output_csv}...")
        with open(output_csv, 'w') as f:
            f.write(header)
        
        df.to_csv(output_csv, mode='a', index=False)
        
        print(f"✅ Successfully converted {mat_file} to {output_csv}")
        print(f"   Data points: {len(df)}")
        print(f"   Angle range: {df['angle'].min():.1f}° to {df['angle'].max():.1f}°")
        print(f"   Cx range: {df['Cx'].min():.3f} to {df['Cx'].max():.3f}")
        print(f"   Cy range: {df['Cy'].min():.3f} to {df['Cy'].max():.3f}")
        print(f"   Cn range: {df['Cn'].min():.3f} to {df['Cn'].max():.3f}")
        
        return True
        
    except Exception as e:
        print(f"Error converting {mat_file}: {e}")
        return False


def convert_all_default_files():
    """Convert all default .mat files to CSV format."""
    conversions = [
        {
            'mat_file': 'app/own_simulator/matlab/dynamics/wind_coefficient_ferry.mat',
            'variable': 'wind_coefficient_ferry',
            'csv_file': 'ship_models/wind_coefficients/ferry_wind_coefficients.csv',
            'ship_name': 'Ferry'
        },
        {
            'mat_file': 'app/own_simulator/matlab/dynamics/wind_coefficient_container.mat',
            'variable': 'wind_coefficient_container',
            'csv_file': 'ship_models/wind_coefficients/container_wind_coefficients.csv',
            'ship_name': 'Container'
        },
        {
            'mat_file': 'app/own_simulator/matlab/dynamics/wind_coefficient_vlcc.mat',
            'variable': 'wind_coefficient_vlcc',
            'csv_file': 'ship_models/wind_coefficients/tanker_wind_coefficients.csv',
            'ship_name': 'Tanker (VLCC)'
        }
    ]
    
    success_count = 0
    
    for conversion in conversions:
        if os.path.exists(conversion['mat_file']):
            # Create output directory if it doesn't exist
            os.makedirs(os.path.dirname(conversion['csv_file']), exist_ok=True)
            
            success = convert_mat_to_csv(
                conversion['mat_file'],
                conversion['variable'],
                conversion['csv_file'],
                conversion['ship_name']
            )
            
            if success:
                success_count += 1
        else:
            print(f"Warning: {conversion['mat_file']} not found, skipping...")
    
    print(f"\nConversion complete: {success_count}/{len(conversions)} files converted successfully")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Convert MATLAB .mat wind coefficient files to CSV format"
    )
    
    parser.add_argument(
        '--mat-file', '-m',
        help='Path to .mat file to convert'
    )
    
    parser.add_argument(
        '--variable', '-v',
        help='Variable name in .mat file'
    )
    
    parser.add_argument(
        '--output', '-o',
        help='Output CSV file path'
    )
    
    parser.add_argument(
        '--ship-name', '-s',
        default='Unknown',
        help='Ship name for header comment'
    )
    
    parser.add_argument(
        '--convert-all', '-a',
        action='store_true',
        help='Convert all default .mat files'
    )
    
    args = parser.parse_args()
    
    if args.convert_all:
        convert_all_default_files()
    elif args.mat_file and args.variable and args.output:
        success = convert_mat_to_csv(args.mat_file, args.variable, args.output, args.ship_name)
        sys.exit(0 if success else 1)
    else:
        parser.print_help()
        print("\nExamples:")
        print("  # Convert all default files:")
        print("  python convert_wind_coefficients.py --convert-all")
        print()
        print("  # Convert specific file:")
        print("  python convert_wind_coefficients.py -m wind_coeff.mat -v wind_data -o output.csv")


if __name__ == "__main__":
    main()
