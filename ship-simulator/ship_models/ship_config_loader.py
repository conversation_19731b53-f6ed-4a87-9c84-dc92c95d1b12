"""
Ship configuration loader for loading ship parameters from JSON files.
"""

import json
import os
import pandas as pd
from typing import Dict, Any, Optional
import numpy as np


class ShipConfigLoader:
    """
    Loads ship configuration from JSON files and CSV wind coefficient files.

    Handles:
    - Loading ship parameters from JSON configuration files
    - Loading wind coefficient data from CSV files
    - Validation of configuration data
    - Caching of loaded configurations
    """
    
    def __init__(self, config_dir: str = None):
        """
        Initialize ship configuration loader.
        
        Args:
            config_dir: Directory containing ship configuration files
        """
        if config_dir is None:
            # Default to configs directory relative to this file
            self.config_dir = os.path.join(os.path.dirname(__file__), "configs")
        else:
            self.config_dir = config_dir
            
        self._config_cache = {}
        self._wind_coeff_cache = {}
        
        # Ship type to config file mapping
        self.ship_type_mapping = {
            1: "ferry.json",
            2: "container.json", 
            3: "tanker.json"
        }
        
        # Ship type to name mapping
        self.ship_names = {
            1: "Ferry",
            2: "Container",
            3: "Tanker"
        }
    
    def load_ship_config(self, ship_type: int) -> Dict[str, Any]:
        """
        Load ship configuration for specified ship type.
        
        Args:
            ship_type: Ship type (1: ferry, 2: container, 3: tanker)
            
        Returns:
            Dictionary containing ship configuration
            
        Raises:
            ValueError: If ship type is invalid
            FileNotFoundError: If configuration file not found
        """
        if ship_type not in self.ship_type_mapping:
            raise ValueError(f"Invalid ship type: {ship_type}. Must be 1, 2, or 3.")
        
        # Check cache first
        if ship_type in self._config_cache:
            return self._config_cache[ship_type].copy()
        
        # Load from file
        config_file = self.ship_type_mapping[ship_type]
        config_path = os.path.join(self.config_dir, config_file)
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Ship configuration file not found: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Validate configuration
            self._validate_config(config)
            
            # Cache the configuration
            self._config_cache[ship_type] = config
            
            return config.copy()
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file {config_path}: {e}")
    
    def get_ship_parameters(self, ship_type: int) -> Dict[str, Any]:
        """
        Get ship parameters in the format expected by the simulation.
        
        Args:
            ship_type: Ship type (1: ferry, 2: container, 3: tanker)
            
        Returns:
            Dictionary containing ship parameters for simulation
        """
        config = self.load_ship_config(ship_type)
        
        # Flatten the configuration structure
        ship_param = {}
        
        # Add all parameter groups
        ship_param.update(config["physical_parameters"])
        ship_param.update(config["dynamics_parameters"])
        ship_param.update(config["rpm"])

        # Add control_parameters if present (optional)
        if "control_parameters" in config:
            ship_param.update(config["control_parameters"])
        
        # Load wind coefficients
        wind_coeff = self._load_wind_coefficients(config)
        ship_param["wind_coefficient"] = wind_coeff
        
        return ship_param
    
    def _load_wind_coefficients(self, config: Dict[str, Any]) -> np.ndarray:
        """
        Load wind coefficients from CSV file.

        Args:
            config: Ship configuration dictionary

        Returns:
            Wind coefficient array in format [angle, Cx, Cy, Cn]
        """
        wind_file = config.get("wind_coefficient_file", "")

        # Check cache first
        cache_key = wind_file
        if cache_key in self._wind_coeff_cache:
            return self._wind_coeff_cache[cache_key]

        try:
            # Try to load CSV file first
            if wind_file.endswith('.csv'):
                wind_coeff = self._load_csv_wind_coefficients(wind_file)
            else:
                # Fallback to default coefficients based on ship type
                wind_coeff = self._get_default_wind_coefficients(config["ship_type"])

            # Cache the result
            self._wind_coeff_cache[cache_key] = wind_coeff

            return wind_coeff

        except Exception as e:
            print(f"Warning: Could not load wind coefficients from {wind_file}: {e}")
            # Return default wind coefficients
            return self._get_default_wind_coefficients(config["ship_type"])

    def _load_csv_wind_coefficients(self, csv_file: str) -> np.ndarray:
        """
        Load wind coefficients from CSV file.

        Args:
            csv_file: Path to CSV file

        Returns:
            Wind coefficient array
        """
        if not os.path.exists(csv_file):
            # Try relative to wind_coefficients directory
            wind_coeff_dir = os.path.join(os.path.dirname(__file__), "wind_coefficients")
            csv_file = os.path.join(wind_coeff_dir, os.path.basename(csv_file))

        if not os.path.exists(csv_file):
            raise FileNotFoundError(f"Wind coefficient file not found: {csv_file}")

        # Load CSV file
        df = pd.read_csv(csv_file, comment='#')

        # Validate required columns
        required_columns = ['angle', 'Cx', 'Cy', 'Cn']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Missing required column '{col}' in wind coefficient file")

        # Convert to numpy array in the expected format
        wind_coeff = df[required_columns].values

        return wind_coeff

    def _get_default_wind_coefficients(self, ship_type: int) -> np.ndarray:
        """
        Get default wind coefficients based on ship type.

        Args:
            ship_type: Ship type (1: ferry, 2: container, 3: tanker)

        Returns:
            Default wind coefficient array
        """
        # Map ship types to CSV files
        wind_coeff_files = {
            1: "ferry_wind_coefficients.csv",
            2: "container_wind_coefficients.csv",
            3: "tanker_wind_coefficients.csv"
        }

        csv_file = wind_coeff_files.get(ship_type, "container_wind_coefficients.csv")
        wind_coeff_dir = os.path.join(os.path.dirname(__file__), "wind_coefficients")
        full_path = os.path.join(wind_coeff_dir, csv_file)

        try:
            return self._load_csv_wind_coefficients(full_path)
        except Exception as e:
            print(f"Warning: Could not load default wind coefficients: {e}")
            # Return basic sinusoidal coefficients as last resort
            return self._generate_basic_wind_coefficients()

    def _generate_basic_wind_coefficients(self) -> np.ndarray:
        """
        Generate basic wind coefficients using sinusoidal approximation.

        Returns:
            Basic wind coefficient array
        """
        angles = np.arange(0, 361, 10)  # 0 to 360 degrees in 10-degree steps
        angles_rad = np.deg2rad(angles)

        # Basic sinusoidal approximation for wind coefficients
        Cx = 0.8 * np.cos(angles_rad)  # Longitudinal force coefficient
        Cy = 0.8 * np.sin(angles_rad)  # Lateral force coefficient
        Cn = 0.4 * np.sin(2 * angles_rad)  # Yaw moment coefficient

        # Combine into array [angle, Cx, Cy, Cn]
        wind_coeff = np.column_stack([angles, Cx, Cy, Cn])

        return wind_coeff

    def create_wind_coefficient_csv(self, ship_type: int, output_file: str,
                                   custom_coefficients: Optional[Dict[str, np.ndarray]] = None) -> None:
        """
        Create a wind coefficient CSV file.

        Args:
            ship_type: Ship type for default coefficients
            output_file: Output CSV file path
            custom_coefficients: Optional custom coefficient arrays
        """
        if custom_coefficients:
            angles = custom_coefficients.get('angles', np.arange(0, 361, 10))
            Cx = custom_coefficients.get('Cx', np.zeros_like(angles))
            Cy = custom_coefficients.get('Cy', np.zeros_like(angles))
            Cn = custom_coefficients.get('Cn', np.zeros_like(angles))
        else:
            # Use basic coefficients
            wind_coeff = self._generate_basic_wind_coefficients()
            angles, Cx, Cy, Cn = wind_coeff[:, 0], wind_coeff[:, 1], wind_coeff[:, 2], wind_coeff[:, 3]

        # Create DataFrame
        df = pd.DataFrame({
            'angle': angles,
            'Cx': Cx,
            'Cy': Cy,
            'Cn': Cn
        })

        # Add header comment
        ship_names = {1: "Ferry", 2: "Container", 3: "Tanker"}
        header = f"# {ship_names.get(ship_type, 'Unknown')} Wind Coefficients\n"
        header += "# Angle (degrees), Cx (longitudinal), Cy (lateral), Cn (yaw moment)\n"

        # Save to CSV
        with open(output_file, 'w') as f:
            f.write(header)

        df.to_csv(output_file, mode='a', index=False)
        print(f"Wind coefficient CSV created: {output_file}")

    def validate_wind_coefficients(self, wind_coeff: np.ndarray) -> bool:
        """
        Validate wind coefficient array format.

        Args:
            wind_coeff: Wind coefficient array to validate

        Returns:
            True if valid, False otherwise
        """
        if wind_coeff.shape[1] != 4:
            print(f"Invalid wind coefficient shape: expected (N, 4), got {wind_coeff.shape}")
            return False

        # Check angle range
        angles = wind_coeff[:, 0]
        if not (np.min(angles) >= 0 and np.max(angles) <= 360):
            print(f"Invalid angle range: {np.min(angles)} to {np.max(angles)}")
            return False

        # Check for reasonable coefficient values
        for i, coeff_name in enumerate(['Cx', 'Cy', 'Cn'], 1):
            coeff_values = wind_coeff[:, i]
            if np.any(np.abs(coeff_values) > 2.0):  # Reasonable upper bound
                print(f"Warning: {coeff_name} values seem unusually large: max={np.max(np.abs(coeff_values))}")

        return True
    
    def _validate_config(self, config: Dict[str, Any]) -> None:
        """
        Validate ship configuration structure.
        
        Args:
            config: Configuration dictionary to validate
            
        Raises:
            ValueError: If configuration is invalid
        """
        required_sections = [
            "ship_type", "name", "description",
            "physical_parameters", "dynamics_parameters", "rpm"
        ]
        
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required section: {section}")
        
        # Validate physical parameters
        required_physical = [
            "Lpp", "Mass", "bdt", "T_p", "T_delta", "RFO", "U_max",
            "A_lateral", "A_front"
        ]
        
        for param in required_physical:
            if param not in config["physical_parameters"]:
                raise ValueError(f"Missing required physical parameter: {param}")
        
        # Validate dynamics parameters
        required_dynamics = [
            "Kr", "tau_u", "tau_v", "tau_r", "gamma", "rudder_max"
        ]
        
        for param in required_dynamics:
            if param not in config["dynamics_parameters"]:
                raise ValueError(f"Missing required dynamics parameter: {param}")
        
        # Validate control parameters (optional)
        if "control_parameters" in config:
            required_control = [
                "kp_psi", "kd_psi", "kp_rot", "ki_rot", "kff_rot"
            ]

            for param in required_control:
                if param not in config["control_parameters"]:
                    raise ValueError(f"Missing required control parameter: {param}")

        # Validate RPM parameters
        required_rpm = ["min", "max"]

        for param in required_rpm:
            if param not in config["rpm"]:
                raise ValueError(f"Missing required RPM parameter: {param}")

        # Validate RPM range
        rpm_min = config["rpm"]["min"]
        rpm_max = config["rpm"]["max"]
        if rpm_min >= rpm_max:
            raise ValueError(f"RPM min ({rpm_min}) must be less than max ({rpm_max})")
        if rpm_min < 0:
            raise ValueError(f"RPM min ({rpm_min}) must be non-negative")

    def get_available_ship_types(self) -> Dict[int, str]:
        """
        Get available ship types and their names.
        
        Returns:
            Dictionary mapping ship type numbers to names
        """
        return self.ship_names.copy()
    
    def get_ship_info(self, ship_type: int) -> Dict[str, Any]:
        """
        Get basic information about a ship type.
        
        Args:
            ship_type: Ship type number
            
        Returns:
            Dictionary with ship information
        """
        config = self.load_ship_config(ship_type)
        
        return {
            "ship_type": config["ship_type"],
            "name": config["name"],
            "description": config["description"],
            "length": config["physical_parameters"]["Lpp"],
            "mass": config["physical_parameters"]["Mass"],
            "max_speed": config["physical_parameters"]["U_max"],
        }
    
    def clear_cache(self) -> None:
        """Clear all cached configurations and wind coefficients."""
        self._config_cache.clear()
        self._wind_coeff_cache.clear()
    
    def reload_config(self, ship_type: int) -> Dict[str, Any]:
        """
        Force reload configuration from file (bypass cache).
        
        Args:
            ship_type: Ship type to reload
            
        Returns:
            Reloaded configuration
        """
        # Remove from cache if present
        if ship_type in self._config_cache:
            del self._config_cache[ship_type]
        
        return self.load_ship_config(ship_type)
