"""
Wave disturbance model for ship simulation.
Implements dynamic wave disturbance generation based on IEC 62065 standard.
"""

import random
from typing import Generator, Tuple


class DynamicWaveDisturbance:
    """
    Dynamic wave disturbance generator based on IEC 62065 standard.
    
    Generates wave disturbances for different sea states that affect ship motion.
    The disturbance values are used in the ship dynamics model to simulate
    wave effects on ship behavior.
    """
    
    def __init__(self, sea_state: int = 0):
        """
        Initialize wave disturbance generator.

        Args:
            sea_state: Sea state number (0-9 supported, default 0 for calm)

        Raises:
            ValueError: If sea state is not supported
        """
        self.sea_state = sea_state
        self.generator = self.wave_disturbance_generator(sea_state)
        self.current_t_end = 0.0
        self.current_Wp = 0.0
        self.scale_factor = 0.03 # Scale factor for Wp

        # Validate sea state
        if sea_state not in range(0, 10):
            raise ValueError(f"Unsupported sea state: {sea_state}. "
                           f"Supported states: 0-9")

    def get_Wb(self, t: float = None) -> float:
        """
        Get wave disturbance value. Automatically advances to next value on each call.

        Args:
            t: Current simulation time in seconds (optional, for compatibility)

        Returns:
            Wave disturbance value Wb
        """
        if t is None or t >= self.current_t_end:
            Ti, Wp = next(self.generator)
            self.current_t_end += Ti
            self.current_Wp = Wp
        return self.current_Wp

    def get_next_wave(self) -> float:
        """
        Get next wave disturbance value. Automatically advances to next value.

        Returns:
            Wave disturbance value Wb
        """
        Ti, Wp = next(self.generator)
        self.current_t_end += Ti
        self.current_Wp = Wp
        return self.current_Wp
    
    def wave_disturbance_generator(self, sea_state: int) -> Generator[Tuple[float, float], None, None]:
        """
        Infinite generator for wave disturbance based on IEC 62065.
        
        Args:
            sea_state: Sea state number
            
        Yields:
            Tuple of (Ti, Wp) where Ti is time interval and Wp is wave disturbance
        """
        SEA_STATE_TABLE = {
            0: (0.0, 0.0),    # Calm - no waves
            1: (2.5, 0.1),    # Light air - ripples
            2: (5.0, 0.5),    # Light breeze - small wavelets
            3: (7.5, 1.25),   # Gentle breeze - large wavelets
            4: (10.0, 2.5),   # Moderate breeze - small waves
            5: (14.0, 4.0),   # Fresh breeze - moderate waves
            6: (17.0, 6.0),   # Strong breeze - large waves
            7: (20.0, 9.5),   # Near gale - very large waves
            8: (23.0, 14.0),  # Gale - exceptionally high waves
            9: (26.0, 20.0),  # Strong gale - phenomenal waves
        }

        if sea_state not in SEA_STATE_TABLE:
            raise ValueError(f"Unsupported sea state: {sea_state}. "
                           f"Only {list(SEA_STATE_TABLE.keys())} are defined in Table H.1.")

        T0, H0 = SEA_STATE_TABLE[sea_state]
        Tr, Hr = 0.5, 0.5  # Relative variation factors
        sign = 1

        while True:
            # Generate random variations
            randb_period = random.uniform(-1, 1)
            randb_height = random.uniform(-1, 1)

            # Calculate time interval and wave height
            Ti = 0.5 * T0 * (1 + Tr * randb_period)
            Hi = H0 * (1 + Hr * randb_height) * sign
            Wp = Hi / H0 * self.scale_factor  

            yield Ti, Wp
            sign *= -1  # Alternate sign for wave variation
    
    @staticmethod
    def get_supported_sea_states() -> set:
        """
        Get supported sea state numbers.

        Returns:
            Set of supported sea state numbers
        """
        return set(range(0, 10))
    
    def get_sea_state_info(self) -> dict:
        """
        Get information about current sea state.

        Returns:
            Dictionary with sea state information
        """
        SEA_STATE_INFO = {
            0: {"name": "Calm", "description": "Calm sea - no waves", "T0": 0.0, "H0": 0.0},
            1: {"name": "Light air", "description": "Light air - ripples", "T0": 2.5, "H0": 0.1},
            2: {"name": "Light breeze", "description": "Light breeze - small wavelets", "T0": 5.0, "H0": 0.5},
            3: {"name": "Gentle breeze", "description": "Gentle breeze - large wavelets", "T0": 7.5, "H0": 1.25},
            4: {"name": "Moderate breeze", "description": "Moderate breeze - small waves", "T0": 10.0, "H0": 2.5},
            5: {"name": "Fresh breeze", "description": "Fresh breeze - moderate waves", "T0": 14.0, "H0": 4.0},
            6: {"name": "Strong breeze", "description": "Strong breeze - large waves", "T0": 17.0, "H0": 6.0},
            7: {"name": "Near gale", "description": "Near gale - very large waves", "T0": 20.0, "H0": 9.5},
            8: {"name": "Gale", "description": "Gale - exceptionally high waves", "T0": 23.0, "H0": 14.0},
            9: {"name": "Strong gale", "description": "Strong gale - phenomenal waves", "T0": 26.0, "H0": 20.0}
        }

        return SEA_STATE_INFO.get(self.sea_state, {
            "name": "Unknown",
            "description": "Unknown sea state",
            "T0": 0.0,
            "H0": 0.0
        })
    
    def reset(self) -> None:
        """Reset the wave disturbance generator."""
        self.generator = self.wave_disturbance_generator(self.sea_state)
        self.current_t_end = 0.0
        self.current_Wp = 0.0
    
    def set_scale_factor(self, scale_factor: float) -> None:
        """
        Set the scale factor for wave disturbance.
        
        Args:
            scale_factor: New scale factor value
        """
        if scale_factor < 0:
            raise ValueError("Scale factor must be non-negative")
        self.scale_factor = scale_factor
    
    def get_current_state(self) -> dict:
        """
        Get current wave disturbance state.
        
        Returns:
            Dictionary with current state information
        """
        return {
            "sea_state": self.sea_state,
            "scale_factor": self.scale_factor,
            "current_t_end": self.current_t_end,
            "current_Wp": self.current_Wp,
            "sea_state_info": self.get_sea_state_info()
        }
