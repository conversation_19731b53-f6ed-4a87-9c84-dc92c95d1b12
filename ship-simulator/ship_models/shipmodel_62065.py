"""
Ship dynamics model 62065 - Refactored version.
Implements ship dynamics calculations with wind, wave, and tidal effects.

This module maintains the original calculation logic while providing
a cleaner, more maintainable class-based structure.
"""

# pylint:disable=C0301,R0914,R0915,C0103,R0912,W0612
import numpy as np
from typing import Dict, Any, Optional
from utils.utils import MathUtils
# Wave and current disturbances are now handled externally


class ShipModel62065:
    """
    Ship dynamics model implementing the 62065 mathematical model.
    
    This class encapsulates ship dynamics calculations including:
    - Surge, sway, and yaw motions
    - Wind effects with coefficient interpolation
    - Wave disturbances
    - Tidal current effects
    - Rudder and propeller dynamics
    """
    
    def __init__(self, env_param):
        """Initialize the ship model."""
        self.model_name = "ShipModel62065"
        self.version = "1.0.0"
        
        # Constants
        self.KNOTS_TO_MS = 0.5144  # Conversion factor from knots to m/s
        self.MS_TO_KNOTS = 1.0 / 0.5144  # Conversion factor from m/s to knots
        self.AIR_DENSITY = 1.225  # kg/m³
        self.INERTIA_FACTOR = 0.35  # Factor for moment of inertia calculation
        self.env_param = env_param
        # Wave and tidal effects are now calculated externally


    
    def calculate_dynamics(self,
                          ship_state: Dict[str, Any],
                          ship_param: Dict[str, Any],
                          command: Dict[str, Any],
                          timestep: float,
                          wave_effect: float = 0.0,
                          tidal_effects: Dict[str, float] = None,
                          time_acceleration: float = 1.0):
        """
        Calculate ship dynamics for one timestep.
        
        Args:
            ship_state: Current ship state dictionary
            ship_param: Ship parameters dictionary
            command: Control commands dictionary
            env_param: Environmental parameters dictionary
            timestep: Time step for integration
            sim_time: Current simulation time
            wave: Optional wave object for wave effects
            
        Returns:
            Updated ship state dictionary
        """
        # Extract input parameters
        inputs = self._extract_inputs(ship_state, ship_param, command)

        # Ensure proper rudder dynamics: use rudder_cmd for command, actual rudder_angle for current
        # This prevents immediate rudder response and enables proper rate limiting
        if 'rudder_cmd' in ship_state:
            inputs['del_cmd'] = ship_state['rudder_cmd'] + ship_param["RFO"]

        # Use default tidal effects if not provided
        if tidal_effects is None:
            tidal_effects = {'Tx': 0.0, 'Ty': 0.0, 'TN': 0.0, 'TE': 0.0}

        # Apply time acceleration to timestep for physics calculations
        physics_timestep = timestep * time_acceleration

        # Calculate environmental effects
        wind_forces = self._calculate_wind_effects(inputs, ship_param, self.env_param)

        # Update actuator states
        new_rudder_angle = self._update_rudder_dynamics(inputs, ship_param, physics_timestep)
        new_lever_position = self._update_propeller_dynamics(inputs, ship_param, physics_timestep)
        
        # Calculate forces and moments
        dynamics = self._calculate_ship_dynamics(
            inputs, ship_param, wind_forces, wave_effect, 
            new_rudder_angle, new_lever_position
        )
        
        # Integrate motion equations
        updated_state = self._integrate_motion(
            inputs, dynamics, tidal_effects, physics_timestep
        )
        
        # Update ship state
        return self._update_ship_state(
            ship_state, updated_state, new_rudder_angle, new_lever_position
        )
    
    def _extract_inputs(self, ship_state: Dict[str, Any],
                       ship_param: Dict[str, Any],
                       command: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and organize input parameters."""
        return {
            # Control inputs (matching original logic)
            'RFO': ship_param["RFO"],
            'del_cmd': command["rudder"] + ship_param["RFO"],  # del_cmd in original
            'del_': ship_state.get("rudder_angle", 0),  # del_ in original
            'lever': ship_state.get("lever", 0.0),
            'lever_cmd': ship_state.get("lever_cmd", 0.0),
            
            # Ship parameters
            'Lpp': ship_param["Lpp"],
            'Mass': ship_param["Mass"],
            'Izz': (self.INERTIA_FACTOR * ship_param["Lpp"]) ** 2 * ship_param["Mass"],
            
            # Current state
            'ug': ship_state["u"],  # Surge velocity
            'vg': ship_state["v"],  # Sway velocity
            'r': ship_state["r"],   # Yaw rate
            'N': ship_state["y"],   # North position (y is North in ENU)
            'E': ship_state["x"],   # East position (x is East in ENU)
            'Hdg': ship_state["psi"],  # Heading
            'COG': ship_state["COG"],  # Course over ground
            'uw': ship_state["uw"],    # Surge velocity relative to water
            'vw': ship_state["vw"],    # Sway velocity relative to water
            
            # Derived quantities
            'spd': np.sqrt(ship_state["u"]**2 + ship_state["v"]**2),
        }
    
    def _calculate_wind_effects(self, inputs: Dict[str, Any],
                               ship_param: Dict[str, Any],
                               env_param: Dict[str, Any]) -> Dict[str, float]:
        """Calculate wind forces and moments."""
        wind = env_param.get("wind", {})

        # Check if wind is disabled or speed is zero
        if not wind.get("enabled", True) or wind.get("speed", 0) == 0:
            return {'WX': 0.0, 'WY': 0.0, 'WN': 0.0}

        wind_coefficient = ship_param["wind_coefficient"]
        wind_dir = wind.get("direction")
        wind_speed = wind.get("speed") * self.KNOTS_TO_MS
        
        # Calculate apparent wind
        ship_spd_north = inputs['spd'] * np.cos(inputs['COG'])
        ship_spd_east = inputs['spd'] * np.sin(inputs['COG'])
        wind_spd_north = -wind_speed * np.cos(np.deg2rad(wind_dir))
        wind_spd_east = -wind_speed * np.sin(np.deg2rad(wind_dir))
        
        aws_north = wind_spd_north - ship_spd_north
        aws_east = wind_spd_east - ship_spd_east
        aws = np.sqrt(aws_north**2 + aws_east**2)
        awd = MathUtils.logic_bound_to_pi(np.arctan2(aws_east, aws_north) + np.pi)
        gamma = MathUtils.logic_bound_to_pi(-awd + inputs['Hdg'])
        
        # Interpolate wind coefficients
        wind_coeffs = self._interpolate_wind_coefficients(gamma, wind_coefficient)
        
        # Calculate wind forces
        A_front = ship_param["A_front"]
        A_lateral = ship_param["A_lateral"]
        dynamic_pressure = 0.5 * self.AIR_DENSITY * aws**2
        
        return {
            'WX': wind_coeffs['Cx'] * dynamic_pressure * A_front,
            'WY': wind_coeffs['Cy'] * dynamic_pressure * A_lateral,
            'WN': wind_coeffs['Cn'] * dynamic_pressure * inputs['Lpp'] * A_lateral
        }

    def _interpolate_wind_coefficients(self, gamma: float,
                                     wind_coefficient: np.ndarray) -> Dict[str, float]:
        """Interpolate wind coefficients based on relative wind angle."""
        wind_a = np.rad2deg(gamma) / 10
        wind_b = int(np.floor(wind_a))
        wind_c = wind_a - wind_b

        if wind_b + 2 < len(wind_coefficient):
            wind_Cx = (wind_coefficient[wind_b + 1, 1] * (1 - wind_c) +
                      wind_coefficient[wind_b + 2, 1] * wind_c)
            wind_Cy = (wind_coefficient[wind_b + 1, 2] * (1 - wind_c) +
                      wind_coefficient[wind_b + 2, 2] * wind_c)
            wind_Cn = (wind_coefficient[wind_b + 1, 3] * (1 - wind_c) +
                      wind_coefficient[wind_b + 2, 3] * wind_c)
        else:
            wind_Cx = wind_Cy = wind_Cn = 0.0

        return {'Cx': wind_Cx, 'Cy': wind_Cy, 'Cn': wind_Cn}



    def _update_rudder_dynamics(self, inputs: Dict[str, Any],
                              ship_param: Dict[str, Any],
                              timestep: float) -> float:
        """Update rudder angle with rate limiting."""
        rudder_max = ship_param["rudder_max"]
        T_delta = ship_param["T_delta"]
        DelRate = rudder_max * 2 / T_delta

        del_cmd = inputs['del_cmd']
        del_ = inputs['del_']

        # Apply rate limiting (matching original logic)
        del_new = np.sign(del_cmd - del_) * DelRate * timestep + del_

        # Check if command is reached
        if (del_cmd - del_) * (del_cmd - del_new) < 0:
            del_new = del_cmd

        # Apply limits (matching original logic)
        if del_new > rudder_max:
            del_new = rudder_max
        elif del_new < -rudder_max:
            del_new = -rudder_max

        return del_new

    def _update_propeller_dynamics(self, inputs: Dict[str, Any],
                                 ship_param: Dict[str, Any],
                                 timestep: float) -> float:
        """Update propeller lever position with rate limiting (matching original logic)."""
        T_p = ship_param["T_p"]
        LeverRate = 2 / T_p

        lever_cmd = inputs['lever_cmd']
        lever = inputs['lever']

        # Apply rate limiting (matching original logic)
        lever_new = np.sign(lever_cmd - lever) * LeverRate * timestep + lever

        # Check if command is reached
        if (lever_cmd - lever) * (lever_cmd - lever_new) < 0:
            lever_new = lever_cmd

        # Apply limits (matching original logic)
        if lever_new > 1:
            lever_new = 1
        elif lever_new < -1:
            lever_new = -1

        return lever_new

    def _calculate_ship_dynamics(self, inputs: Dict[str, Any],
                               ship_param: Dict[str, Any],
                               wind_forces: Dict[str, float],
                               wave_effect: float,
                               rudder_angle: float,
                               lever_position: float) -> Dict[str, float]:
        """Calculate ship dynamics forces and accelerations."""
        # Ship parameters
        U_max = ship_param["U_max"]
        Kr = ship_param["Kr"]
        tau_u = ship_param["tau_u"]
        tau_v = ship_param["tau_v"]
        tau_r = ship_param["tau_r"]
        gamma = ship_param["gamma"]
        rudder_max = ship_param["rudder_max"]

        # Normalized values
        rudder_norm = rudder_angle / rudder_max
        Ku = U_max * self.KNOTS_TO_MS / tau_u
        X_thrust = lever_position

        # Current velocities
        ug = inputs['ug']
        vg = inputs['vg']
        r = inputs['r']
        uw = inputs['uw']
        vw = inputs['vw']
        L = inputs['Lpp']
        Mass = inputs['Mass']
        Izz = inputs['Izz']

        # Calculate accelerations (original dynamics equations)
        ug_dot = (Ku * X_thrust + vg * r - uw / tau_u +
                 wind_forces['WX'] / Mass)

        vg_dot = (-ug * r - vw / tau_v +
                 wind_forces['WY'] / Mass)

        r_dot = (np.pi / 180 * 100 * Kr * ((Ku * X_thrust * tau_u / L) * rudder_norm) +
                Kr * wave_effect +
                12 * gamma * (vw - gamma * L * r) / (L * tau_v) -
                r / tau_r +
                wind_forces['WN'] / Izz)

        # Debug r_dot calculation when rudder is applied
        if abs(rudder_norm) > 0.1:  # When rudder is significantly applied
            print(f"R_DOT_DEBUG: rudder_norm={rudder_norm:.3f}")
            print(f"  Kr={Kr}, Ku={Ku:.3f}, X_thrust={X_thrust:.3f}")
            print(f"  Main term: {np.pi / 180 * 100 * Kr * ((Ku * X_thrust * tau_u / L) * rudder_norm):.6f}")
            print(f"  Damping term: {-r / tau_r:.6f}")
            print(f"  Total r_dot: {r_dot:.6f} rad/s²")

        return {
            'ug_dot': ug_dot,
            'vg_dot': vg_dot,
            'r_dot': r_dot,
            'X_thrust': X_thrust
        }

    def _integrate_motion(self, inputs: Dict[str, Any],
                         dynamics: Dict[str, float],
                         tidal_effects: Dict[str, float],
                         timestep: float) -> Dict[str, Any]:
        """Integrate motion equations and update positions."""
        # Current state
        ug = inputs['ug']
        vg = inputs['vg']
        r = inputs['r']
        Hdg = inputs['Hdg']
        N = inputs['N']
        E = inputs['E']

        # Calculate tidal effects in ship coordinate system 
        TN = tidal_effects['TN']  # Already in m/s
        TE = tidal_effects['TE']  # Already in m/s
        Tx = np.cos(Hdg) * TN + np.sin(Hdg) * TE
        Ty = -np.sin(Hdg) * TN + np.cos(Hdg) * TE

        # Calculate velocities relative to water 
        uw = ug - Tx
        vw = vg - Ty

        # Integrate velocities FIRST (정답지 순서)
        ug_new = ug + dynamics['ug_dot'] * timestep
        vg_new = vg + dynamics['vg_dot'] * timestep
        r_new = r + dynamics['r_dot'] * timestep

        # Integrate heading using NEW angular velocity
        Hdg_new = Hdg + r_new * timestep

        # Calculate position derivatives using NEW heading and velocities (ENU coordinate system)
        x_dot = np.cos(Hdg_new) * ug_new - np.sin(Hdg_new) * vg_new  # North direction velocity
        y_dot = np.sin(Hdg_new) * ug_new + np.cos(Hdg_new) * vg_new  # East direction velocity

        # Integrate positions LAST
        N_new = N + x_dot * timestep
        E_new = E + y_dot * timestep

        # Debug velocity and position derivatives
        if abs(x_dot) > 0.1 or abs(y_dot) > 0.1:
            print(f"VEL_DEBUG: ug_new={ug_new:.2f}, vg_new={vg_new:.2f}, Hdg_new={np.degrees(Hdg_new):.1f}°")
            print(f"VEL_DEBUG: x_dot={x_dot:.2f}m/s, y_dot={y_dot:.2f}m/s")

        # Calculate speed and course (정답지 방식)
        U = np.sqrt(ug_new**2 + vg_new**2)
        if U == 0:
            beta = 0
        else:
            beta = np.arctan2(vg_new, ug_new)  # Drift angle in ship coordinates

        COG_new = Hdg_new + beta  # Course over ground

        # Normalize angles (정답지 방식)
        if Hdg_new > 2 * np.pi:
            Hdg_new = Hdg_new - 2 * np.pi
        elif Hdg_new < 0:
            Hdg_new = Hdg_new + 2 * np.pi

        if beta > 2 * np.pi:
            beta = beta - 2 * np.pi
        elif beta < 0:
            beta = beta + 2 * np.pi

        if COG_new > 2 * np.pi:
            COG_new = COG_new - 2 * np.pi
        elif COG_new < 0:
            COG_new = COG_new + 2 * np.pi

        return {
            'ug': ug_new,
            'vg': vg_new,
            'r': r_new,
            'Hdg': Hdg_new,
            'N': N_new,
            'E': E_new,
            'uw': uw,
            'vw': vw,
            'COG': COG_new,
            'beta': beta,
            'U': U,
            'ug_dot': dynamics['ug_dot']
        }

    def _normalize_angle(self, angle: float) -> float:
        """Normalize angle to [0, 2π] range (matching original logic)."""
        if angle > 2 * np.pi:
            angle = angle - 2 * np.pi
        elif angle < 0:
            angle = angle + 2 * np.pi
        return angle

    def _update_ship_state(self, ship_state: Dict[str, Any],
                          updated_state: Dict[str, Any],
                          rudder_angle: float,
                          lever_position: float) -> Dict[str, Any]:
        """Update ship state dictionary with new values."""
        # Update all state variables (maintaining original structure)
        ship_state["x"] = updated_state['E']  # x is East in ENU coordinate system
        ship_state["y"] = updated_state['N']  # y is North in ENU coordinate system
        ship_state["psi"] = updated_state['Hdg']
        ship_state["du"] = updated_state['ug_dot']
        ship_state["u"] = updated_state['ug']
        ship_state["v"] = updated_state['vg']
        ship_state["r"] = updated_state['r']
        ship_state["uw"] = updated_state['uw']
        ship_state["vw"] = updated_state['vw']
        ship_state["COG"] = updated_state['COG']
        ship_state["beta"] = updated_state['beta']
        ship_state["rudder_angle"] = rudder_angle
        ship_state["del"] = rudder_angle  # For backward compatibility
        ship_state["lever"] = lever_position

        # Calculate derived quantities
        ship_state["SOG"] = updated_state['U'] * self.MS_TO_KNOTS  # Speed over ground in knots
        ship_state["STW"] = (np.sqrt(updated_state['uw']**2 + updated_state['vw']**2) *
                           self.MS_TO_KNOTS)  # Speed through water in knots

        return ship_state

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        return {
            'name': self.model_name,
            'version': self.version,
            'description': 'Ship dynamics model implementing 62065 mathematical formulation',
            'features': [
                'Surge, sway, and yaw dynamics',
                'Wind effects with coefficient interpolation',
                'Wave disturbances',
                'Tidal current effects',
                'Rudder and propeller dynamics with rate limiting'
            ]
        }


