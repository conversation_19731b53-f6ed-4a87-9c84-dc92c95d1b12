"""
Current disturbance utilities for ship simulator.
Converts current speed/direction to north/east components for 62065 model.
"""

import math
from typing import Dict, <PERSON><PERSON>, Any


class CurrentDisturbance:
    """
    Current disturbance utilities.

    Converts current speed and direction to north/east components.
    The actual current effects are calculated in the 62065 ship model.
    """
    
    def __init__(self):
        """Initialize current disturbance model."""
        self.model_name = "CurrentDisturbance"
        self.version = "1.0.0"
        
        # Current state
        self.current_speed = 0.0      # Current speed in knots
        self.current_direction = 0.0  # Current direction in degrees (0=North, 90=East)
        self.tidal_north = 0.0        # North component in knots
        self.tidal_east = 0.0         # East component in knots
        self.enabled = False          # Current disturbance enabled flag
        
        # Constants
        self.KNOTS_TO_MS = 0.5144     # Conversion factor from knots to m/s
        self.DEG_TO_RAD = math.pi / 180.0
    
    def set_current(self, speed: float, direction: float, enabled: bool = True) -> None:
        """
        Set current parameters.
        
        Args:
            speed: Current speed in knots
            direction: Current direction in degrees (0=North, 90=East, 180=South, 270=West)
            enabled: Enable/disable current effects
        """
        self.current_speed = speed
        self.current_direction = direction
        self.enabled = enabled
        
        # Convert to north/east components
        self._update_components()
    
    def set_current_components(self, tidal_north: float, tidal_east: float, enabled: bool = True) -> None:
        """
        Set current using north/east components.
        
        Args:
            tidal_north: North component in knots (positive = northward)
            tidal_east: East component in knots (positive = eastward)
            enabled: Enable/disable current effects
        """
        self.tidal_north = tidal_north
        self.tidal_east = tidal_east
        self.enabled = enabled
        
        # Convert to speed/direction
        self._update_speed_direction()
    
    def _update_components(self) -> None:
        """Update north/east components from speed/direction."""
        if self.current_speed == 0:
            self.tidal_north = 0.0
            self.tidal_east = 0.0
            return
        
        # Convert direction to radians
        direction_rad = self.current_direction * self.DEG_TO_RAD
        
        # Calculate components (oceanographic convention: direction current is flowing TO)
        self.tidal_north = self.current_speed * math.cos(direction_rad)
        self.tidal_east = self.current_speed * math.sin(direction_rad)
    
    def _update_speed_direction(self) -> None:
        """Update speed/direction from north/east components."""
        # Calculate speed
        self.current_speed = math.sqrt(self.tidal_north**2 + self.tidal_east**2)
        
        # Calculate direction
        if self.current_speed > 0:
            self.current_direction = math.atan2(self.tidal_east, self.tidal_north) / self.DEG_TO_RAD
            # Normalize to 0-360 degrees
            if self.current_direction < 0:
                self.current_direction += 360
        else:
            self.current_direction = 0.0
    
    def get_current_components(self) -> Tuple[float, float]:
        """
        Get current components in knots.

        Returns:
            Tuple of (tidal_north, tidal_east) in knots
        """
        if not self.enabled:
            return (0.0, 0.0)

        return (self.tidal_north, self.tidal_east)
    
    def get_current_info(self) -> Dict[str, Any]:
        """
        Get current disturbance information.
        
        Returns:
            Dictionary with current parameters
        """
        return {
            "enabled": self.enabled,
            "speed_knots": self.current_speed,
            "direction_deg": self.current_direction,
            "tidal_north_knots": self.tidal_north,
            "tidal_east_knots": self.tidal_east,
            "north_velocity_ms": self.tidal_north * self.KNOTS_TO_MS,
            "east_velocity_ms": self.tidal_east * self.KNOTS_TO_MS
        }
    

    
    def update_from_config(self, config: Dict[str, Any]) -> None:
        """
        Update current parameters from configuration.
        
        Args:
            config: Configuration dictionary with current parameters
        """
        if "speed" in config and "direction" in config:
            self.set_current(
                speed=config.get("speed", 0.0),
                direction=config.get("direction", 0.0),
                enabled=config.get("enabled", False)
            )
        elif "north" in config and "east" in config:
            self.set_current_components(
                tidal_north=config.get("north", 0.0),
                tidal_east=config.get("east", 0.0),
                enabled=config.get("enabled", False)
            )
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        return {
            "name": self.model_name,
            "version": self.version,
            "description": "Current disturbance model for tidal current effects",
            "features": [
                "Speed/direction to north/east component conversion",
                "Current velocity calculation",
                "Ship velocity relative to water calculation",
                "Current force coefficients"
            ]
        }


# Global current disturbance instance
_current_disturbance = None


def get_current_disturbance() -> CurrentDisturbance:
    """
    Get global current disturbance instance.
    
    Returns:
        CurrentDisturbance instance
    """
    global _current_disturbance
    if _current_disturbance is None:
        _current_disturbance = CurrentDisturbance()
    return _current_disturbance


def get_current_components(current_config: Dict[str, Any]) -> Tuple[float, float]:
    """
    Get current north/east components from configuration.

    Args:
        current_config: Current configuration dictionary

    Returns:
        Tuple of (tidal_north, tidal_east) in knots
    """
    current_model = get_current_disturbance()
    current_model.update_from_config(current_config)

    return current_model.get_current_components()


# Example usage and testing
if __name__ == "__main__":
    # Test current disturbance
    current = CurrentDisturbance()
    
    print("Current Disturbance Test")
    print("=" * 40)
    
    # Test 1: Set current by speed/direction
    print("Test 1: Speed/Direction Input")
    current.set_current(speed=2.0, direction=45.0, enabled=True)  # 2 knots, NE direction
    info = current.get_current_info()
    print(f"Speed: {info['speed_knots']:.2f} knots")
    print(f"Direction: {info['direction_deg']:.1f}°")
    print(f"North: {info['tidal_north_knots']:.3f} knots")
    print(f"East: {info['tidal_east_knots']:.3f} knots")
    print()
    
    # Test 2: Set current by components
    print("Test 2: Component Input")
    current.set_current_components(tidal_north=1.0, tidal_east=1.5, enabled=True)
    info = current.get_current_info()
    print(f"North: {info['tidal_north_knots']:.3f} knots")
    print(f"East: {info['tidal_east_knots']:.3f} knots")
    print(f"Speed: {info['speed_knots']:.2f} knots")
    print(f"Direction: {info['direction_deg']:.1f}°")
    print()
    
    # Test 3: Current components
    print("Test 3: Current Components")
    tidal_north, tidal_east = current.get_current_components()
    print(f"Current components: North={tidal_north:.3f}, East={tidal_east:.3f} knots")

    # Test configuration function
    print("\nTest 4: Configuration Function")
    config = {"speed": 3.0, "direction": 30.0, "enabled": True}
    north, east = get_current_components(config)
    print(f"Config {config} -> North={north:.3f}, East={east:.3f} knots")
