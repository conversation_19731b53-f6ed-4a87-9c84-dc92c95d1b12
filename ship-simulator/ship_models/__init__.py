"""
Ship Models Package

Contains ship model configurations and dynamics implementations.

Available ship models:
- Ferry (Type 1)
- Container (Type 2) 
- Tanker (Type 3)

Each model includes:
- Physical parameters (dimensions, mass, etc.)
- Control parameters (PID gains, limits, etc.)
- Wind coefficients
- Dynamics implementation
"""

from .ship_config_loader import ShipConfigLoader
from .shipmodel_62065 import ShipModel62065
from .wave_disturbance import DynamicWaveDisturbance
from .current_disturbance import CurrentDisturbance, get_current_components

__version__ = "1.0.0"

__all__ = [
    "ShipConfigLoader",
    "ShipModel62065",
    "DynamicWaveDisturbance",
    "CurrentDisturbance",
    "get_current_components",
]
