"""
Main ship simulator class that orchestrates all simulation components.
"""

import math
import time
import asyncio
import os
import pandas as pd
from datetime import datetime, timezone
from typing import Dict, Any, List, Tuple

from .communication_manager import CommunicationManager
from .simulation_config import SimulationConfig
from .autopilot_interface import RSAInterface
from .time_manager import TimeManager
from .ship_state import ShipState

# fn_control_apl is no longer needed in RSA mode
from .navigation_data import make_nmea_sentence
from ship_models.shipmodel_62065 import ShipModel62065
from ship_models.wave_disturbance import DynamicWaveDisturbance
from .coordinate_transformer import initialize_coordinate_transformer, get_coordinate_transformer
from utils.redis_manager import RedisManager
from utils.speed_lever_interpolation import interpolate_speed_from_lever, interpolate_lever_from_speed


class ShipSimulator:
    """
    Main ship simulator class that coordinates all simulation components.
    
    Orchestrates:
    - Network communication
    - Message parsing
    - Time management
    - Ship dynamics simulation
    - Control systems
    """
    
    def __init__(self, config : SimulationConfig):
        """
        Initialize ship simulator.
        
        Args:
            lat: Initial latitude in degrees
            lon: Initial longitude in degrees
            speed: Initial speed in knots
            course: Initial course in degrees
        """
        # Get simulation parameters first
        self.config = config
        sim_params = self.config.get_simulation_parameters()
        self.simulation_set = sim_params["simulation_set"]

        # Get time acceleration before initializing TimeManager
        speed_up = self.config.get_simulation_speed_up()

        # Initialize all components with proper time acceleration
        self.communication = CommunicationManager()
        self.time_manager = TimeManager(time_acceleration=speed_up)
        self.rsa_interface = RSAInterface()

        # Base timestep is 0.5 seconds (2Hz)
        base_timestep = 0.5
        # Physics timestep should be accelerated for faster simulation
        self.simulation_timestep = base_timestep * speed_up  # Accelerated physics timestep
        self.time_acceleration = speed_up  # Time acceleration factor
        self.loop_timestep = base_timestep  # Loop timing remains at 2Hz

        self.env_param = sim_params["env_param"]
        self.ship_param = sim_params["ship_param"]

        # Get constants
        self.radian_to_degree = self.config.RADIAN_TO_DEGREE
        self.degree_to_radian = self.config.DEGREE_TO_RADIAN

        # Get initial position for coordinate conversion
        self.lat_ini, self.lon_ini, _ = self.config.get_initial_position()

        # Initialize coordinate transformer
        initialize_coordinate_transformer(self.lat_ini, self.lon_ini, "ENU")
        self.coordinate_transformer = get_coordinate_transformer()

        # Load speed table for lever interpolation (before creating initial state)
        self.speed_table = self._load_speed_table()

        # Initialize ship state using new ShipState class
        initial_state_dict = self._create_initial_ship_state()
        self.ship_state = ShipState.from_dict(initial_state_dict)
        self.step_count = 0  # Add step counter for debugging

        # Initialize local coordinates from GPS coordinates (one-time conversion)
        initial_x, initial_y = self.coordinate_transformer.global_to_local(
            initial_state_dict["lat"], initial_state_dict["lon"]
        )
        self.ship_state.x = initial_x
        self.ship_state.y = initial_y

        # Initialize wave disturbance if enabled
        if self.config.is_wave_enabled():
            sea_state = self.env_param.get("wave", {}).get("sea_state", 0)
            self.wave_disturbance = DynamicWaveDisturbance(sea_state)
        else:
            self.wave_disturbance = None


        # Safely initialize wind and current parameters
        self._safe_init_env_params()
    
        # Initialize ship dynamics model
        self.ship_model = ShipModel62065(self.env_param)

        # Simulation control variables
        self.simulation_step = 0

        # Initialize Redis manager for NMEA display data with environment variables
        try:
            # Get Redis configuration from environment variables
            redis_config = {
                'host': os.getenv('REDIS_HOST', 'localhost'),
                'port': int(os.getenv('REDIS_PORT', '6379')),
                'password': os.getenv('REDIS_PASSWORD', '') or None,
                'ssl': os.getenv('REDIS_SSL', 'false').lower() == 'true',
                'auto_conn': os.getenv('REDIS_AUTO_CONN', 'true').lower() == 'true',
                'auto_utc_set': os.getenv('REDIS_AUTO_UTC_SET', 'false').lower() == 'true'
            }

            self.redis_manager = RedisManager(**redis_config)

            print(f"✅ Redis manager initialized for NMEA display data")
            print(f"   Host: {redis_config['host']}:{redis_config['port']}")
            print(f"   SSL: {redis_config['ssl']}, Auto-connect: {redis_config['auto_conn']}")

        except Exception as e:
            print(f"⚠️  Redis manager initialization failed: {e}")
            self.redis_manager = None

        # Publish initial state to Redis after initialization
        self._publish_initial_state()

    def _publish_initial_state(self):
        """
        Publish initial simulation state to Redis channels.
        Called once during initialization.
        """
        if not self.redis_manager:
            return

        try:
            # Calculate RPM from lever position using linear interpolation
            lever_value = getattr(self.ship_state, 'lever', 0.0)

            # Get RPM range from ship parameters
            rpm_min = self.ship_param.get('min', 0)
            rpm_max = self.ship_param.get('max', 500)

            # Linear interpolation: lever (0.0-1.0) -> rpm (min-max)
            calculated_rpm = rpm_min + (lever_value * (rpm_max - rpm_min))

            # Create initial state message
            initial_state = {
                "event_type": "simulation_initialized",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "ship_state": {
                    "latitude": self.ship_state.lat,
                    "longitude": self.ship_state.lon,
                    "speed_knots": self.ship_state.SOG,
                    "course_degrees": self.ship_state.COG * 180 / 3.14159 if self.ship_state.COG is not None else None,
                    "heading_degrees": self.ship_state.psi * 180 / 3.14159,
                    "rpm": calculated_rpm,
                    "lever_position": lever_value
                }
            }

            # Publish to simulation events channel
            success = self.redis_manager.PublishRedisMsg("simulation:initial_state", initial_state)

            if success:
                print(f"📡 Published initial state to Redis channel 'simulation:events'")
                print(f"   Position: {self.ship_state.lat:.6f}, {self.ship_state.lon:.6f}")
                print(f"   Speed: {self.ship_state.SOG:.1f} kts, Course: {self.ship_state.COG * 180 / 3.14159:.1f}°")
            else:
                print(f"⚠️  Failed to publish initial state to Redis")

        except Exception as e:
            print(f"⚠️  Error publishing initial state: {e}")

    def _load_speed_table(self) -> List[Tuple[float, float]]:
        """
        Load speed table for current ship type.

        Returns:
            List of (lever, speed_knots) tuples
        """
        try:
            # Get ship type from config
            ship_type = self.config.simulation_set.get('ship_type', 2)
            ship_names = {1: 'ferry', 2: 'container', 3: 'tanker'}

            if ship_type not in ship_names:
                print(f"⚠️  Unknown ship_type {ship_type}, using container")
                ship_type = 2

            ship_name = ship_names[ship_type]

            # Load speed table CSV
            csv_path = os.path.join(os.path.dirname(os.path.dirname(__file__)),
                                  'ship_models', 'speed_table', f'{ship_name}_speed_table.csv')

            if not os.path.exists(csv_path):
                print(f"⚠️  Speed table not found: {csv_path}")
                return [(0.05, 1.25), (1.0, 25.0)]  # Default fallback

            df = pd.read_csv(csv_path)
            speed_table = [(row['lever'], row['speed_knots']) for _, row in df.iterrows()]

            print(f"📊 Loaded {ship_name} speed table: {len(speed_table)} entries")
            return speed_table

        except Exception as e:
            print(f"⚠️  Error loading speed table: {e}")
            return [(0.05, 1.25), (1.0, 25.0)]  # Default fallback



    def _create_initial_ship_state(self) -> Dict[str, Any]:
        """Create initial ship state dictionary."""
        lat, lon, course = self.config.get_initial_position()
        speed = self.config.get_initial_speed()

        # Convert course to radians
        course_rad = course * self.degree_to_radian

        # Convert speed from knots to m/s
        speed_ms = speed * self.config.KNOTS_TO_MS

        # Calculate lever from speed using speed table interpolation
        if speed > 0 and hasattr(self, 'speed_table'):
            lever = interpolate_lever_from_speed(speed, self.speed_table)
            print(f"🎯 Initial speed {speed:.2f} kts → lever {lever:.3f} (interpolated)")
        else:
            # Fallback to config lever if speed is 0 or speed table not loaded
            lever = self.config.get_initial_lever()
            print(f"🎯 Using config lever {lever:.3f} (speed={speed:.2f} kts)")

        # lever_cmd = self.config.get_initial_lever_cmd()

        return {
            "lat": lat,
            "lon": lon,
            "psi": course_rad,
            "u": speed_ms,   # Initial surge velocity (ground speed)
            "v": 0.0,        # Initial sway velocity (ground speed)
            "r": 0.0,
            "x": 0.0,
            "y": 0.0,
            "SOG": speed,
            "COG": course_rad,
            "rudder_angle": 0.0,
            "rudder_cmd": 0.0,
            "uw": speed_ms,  # Initial speed through water
            "vw": 0.0,       # Initial sway velocity through water
            "lever": lever,    # Initial propeller lever position from config
            "lever_cmd": lever # Initial propeller lever command from config
        }
        
    async def run_simulation(self) -> None:
        """
        Run the main simulation loop asynchronously.

        This method handles:
        - RSA message reception and rudder command processing
        - Ship dynamics simulation
        - NMEA sentence generation and multicast transmission
        - Time management
        """
        print("Starting async ship simulation...")
        print(f"Listening for RSA commands on {self.communication.server_ip}:{self.communication.server_port}")
        print(f"Sending NMEA data via multicast to {self.communication.multicast_ip}:{self.communication.parser_port}")

        # Setup communication
        self.communication.setup_communication()

        try:
            # Start both loops in parallel
            message_task = asyncio.create_task(self._message_receiver_loop())
            physics_task = asyncio.create_task(self._physics_simulation_loop())

            # Wait for either task to complete (or both)
            await asyncio.gather(message_task, physics_task)

        except KeyboardInterrupt:
            print("Simulation interrupted by user")
        except Exception as e:
            print(f"Simulation error: {e}")
        finally:
            self.communication.close_communication()
            print("Simulation ended")
    
    async def _message_receiver_loop(self) -> None:
        """Fast message receiver loop (10Hz) - no blocking."""
        print("🚀 Starting 10Hz message receiver loop...")

        while True:
            try:
                # NON-BLOCKING: Just process one message per cycle
                message_data = await self.communication.receive_simulator_message()

                if message_data is not None:
                    # Process message immediately
                    if message_data.get('message_type') == 'RSA':
                        self.ship_state.rudder_cmd = message_data['rudder_angle']
                        # Note: rudder_angle (actual) is updated by ship dynamics model, not immediately
                        self.ship_state.rudder_status = message_data['rudder_status']
                        self.ship_state.rudder_status_port = message_data['rudder_status_port']
                        if 'rudder_angle_port' in message_data:
                            self.ship_state.rudder_angle_port = message_data['rudder_angle_port']
                        print(f"🚢 RSA: Rudder CMD={message_data['rudder_angle']:.1f}° ACT={self.ship_state.rudder_angle:.1f}° [{message_data['rudder_status']}/{message_data['rudder_status_port']}]")

                    elif message_data.get('message_type') == 'RPM':
                        try:
                            rpm_value = message_data['rpm']
                            engine_number = message_data['engine_number']
                            lever_value = self._convert_rpm_to_lever(rpm_value)

                            if engine_number == 1:
                                self.ship_state.lever = lever_value
                                print(f"📊 RPM: E{engine_number}={rpm_value:.0f}RPM → Lever={lever_value:.3f} P={message_data['pitch']:.0f}° [{message_data['status']}]")
                            else:
                                print(f"📊 RPM: E{engine_number}={rpm_value:.0f}RPM P={message_data['pitch']:.0f}° [{message_data['status']}] (not applied)")
                        except Exception as e:
                            print(f"RPM processing error: {e}")

            except Exception as e:
                print(f"Message receiver error: {e}")

            # 10Hz frequency: 100ms delay
            await asyncio.sleep(0.1)

    async def _physics_simulation_loop(self) -> None:
        """Pure physics simulation loop (2Hz) - no message processing."""
        print("⚙️ Starting 2Hz physics simulation loop...")

        # Initialize simulation time
        self.time_manager.reset_time()

        while True:
            # Update simulation time
            self.time_manager.update_time()
            loop_start = time.perf_counter()

            # Pure physics simulation (no message processing)

            # Run ship dynamics simulation
            await self._run_simulation_step()

            # Increment step counter
            self.step_count += 1

            # Generate and send NMEA sentences via multicast
            await self._send_navigation_data()

            # Set NMEA display data to Redis
            await self._set_nmea_display_data()

            # Control simulation rate (10Hz) - use loop timestep, not physics timestep
            sleep_time = max(0, self.loop_timestep - (time.perf_counter() - loop_start))
            await asyncio.sleep(sleep_time)

    async def _send_navigation_data(self) -> None:
        """Generate and send NMEA sentences via multicast."""
        try:
            # Get UTC time information
            utc_time, utc_nmea = self.time_manager.get_utc_time_info()

            # Generate NMEA sentences (convert to dict for legacy function)
            ship_state_dict = self.ship_state.to_dict()

            # Debug: Print GPS coordinates in ship_state_dict
            if self.step_count % 100 == 0:
                print(f"NMEA_DEBUG: Step {self.step_count}")
                print(f"  ship_state_dict lat: {ship_state_dict.get('lat')}")
                print(f"  ship_state_dict lon: {ship_state_dict.get('lon')}")
                print(f"  ship_state.lat: {self.ship_state.lat}")
                print(f"  ship_state.lon: {self.ship_state.lon}")

            simulation_nmea, _ = make_nmea_sentence(
                ship_state_dict, self.env_param, utc_nmea, utc_time
            )

            # Send navigation data via multicast and autopilot unicast (parallel)
            await self.communication.send_navigation_data_async(simulation_nmea)

            # Simulation step counter is updated in _run_simulation_step

            # Print status every 100 steps (10 seconds at 10Hz) - less frequent
            if self.simulation_step % 100 == 0:
                print(f"📡 Step {self.simulation_step}: NMEA data sent | Rudder: {self.ship_state.rudder_cmd:.1f}° | Speed: {self.ship_state.SOG:.1f}kts")

        except Exception as e:
            print(f"Error sending navigation data: {e}")

    async def _set_nmea_display_data(self) -> None:
        """Set NMEA display data to Redis with 30 second TTL."""
        if not self.redis_manager:
            print("Redis manager not available - skipping NMEA display data update")
            return

        try:
            # Get current ship state
            ship_state_dict = self.ship_state.to_dict()

            # Get UTC time
            utc_time, _ = self.time_manager.get_utc_time_info()
            utc_string = utc_time.strftime("%Y-%m-%d %H:%M:%S")

            # Safely extract wind and current data using helper method
            wind_direction = self._safe_get_env_param("wind.direction", None)
            wind_speed = self._safe_get_env_param("wind.speed", None)
            current_direction = self._safe_get_env_param("current.direction", None)
            current_speed = self._safe_get_env_param("current.speed", None)

            # Build NMEA display data structure with safe mapping
            nmea_display_data = {
                "COG": ship_state_dict.get("COG", 0.0) * 180 / math.pi if ship_state_dict.get("COG") is not None else None,  # Convert to degrees
                "Heading": ship_state_dict.get("psi", 0.0) * 180 / math.pi if ship_state_dict.get("psi") is not None else None,  # Convert to degrees
                "SOG": ship_state_dict.get("SOG", None),  # Already in knots
                "ROT": ship_state_dict.get("ROT", None),  # Already in degrees per minute
                "Latitude": ship_state_dict.get("lat", None),  # Decimal degrees
                "Longitude": ship_state_dict.get("lon", None),  # Decimal degrees
                "Depth": None,  # Not available
                "CurrentDirection": current_direction,  # From env_param
                "CurrentSpeed": current_speed,  # From env_param
                "LongitudinalSOG": None,  # Not available
                "LongitudinalSTW": None,  # Not available
                "LongitudinalSternSTW": None,  # Not available
                "TransverseSOG": None,  # Not available
                "TransverseSTW": None,  # Not available
                "TransverseSternSTW": None,  # Not available
                "RPM": {
                    "PortSensor": ship_state_dict.get("rpm", None),
                    "StarboardSensor": None,  # Not available
                    "PortStatus": "A" if ship_state_dict.get("rpm") is not None else None,
                    "StarboardStatus": None
                },
                "Rudder": {
                    "PortSensor": ship_state_dict.get("rudder_angle", None),
                    "StarboardSensor": None,  # Not available
                    "PortStatus": ship_state_dict.get("rudder_status", "A") if ship_state_dict.get("rudder_angle") is not None else None,
                    "StarboardStatus": None
                },
                "WindDirection": wind_direction,  # From env_param
                "WindSpeed": wind_speed,  # From env_param
                "UTC": utc_string
            }

            # Set to Redis with 30 second TTL
            self.redis_manager.SetRedisMsg("nmea:display", nmea_display_data, ttl=30)

            # Debug log every 50 steps (less frequent)
            if self.simulation_step % 50 == 0:
                cog_str = f"{nmea_display_data['COG']:.1f}°" if nmea_display_data['COG'] is not None else "null"
                sog_str = f"{nmea_display_data['SOG']:.1f}kts" if nmea_display_data['SOG'] is not None else "null"
                wind_str = f"W:{nmea_display_data['WindDirection']:.1f}°@{nmea_display_data['WindSpeed']:.1f}" if nmea_display_data['WindDirection'] is not None and nmea_display_data['WindSpeed'] is not None else "W:null"
                current_str = f"C:{nmea_display_data['CurrentDirection']:.1f}°@{nmea_display_data['CurrentSpeed']:.1f}" if nmea_display_data['CurrentDirection'] is not None and nmea_display_data['CurrentSpeed'] is not None else "C:null"
                print(f"📊 Redis: NMEA display updated - COG:{cog_str} SOG:{sog_str} {wind_str} {current_str}")

        except Exception as e:
            print(f"Error setting NMEA display data to Redis: {e}")

    def _safe_get_env_param(self, param_path: str, default=None):
        """
        Safely extract nested parameters from env_param.

        Args:
            param_path: Dot-separated path like "wind.direction" or "current.speed"
            default: Default value if path not found

        Returns:
            Value at the path or default
        """
        try:
            if not self.env_param or not isinstance(self.env_param, dict):
                return default

            keys = param_path.split('.')
            current = self.env_param

            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default

            return current

        except Exception as e:
            print(f"Warning: Error accessing env_param path '{param_path}': {e}")
            return default

    def _safe_init_env_params(self):
        """
        Safely initialize wind and current parameters based on config settings.
        Uses the safe setter method to avoid AttributeError.
        """
        try:
            # Initialize wind parameters
            if not self.config.is_wind_enabled():
                self._safe_set_env_param("wind.speed", 0.0)
                self._safe_set_env_param("wind.direction", 0.0)
                print("🌬️  Wind disabled - set to zero")
            else:
                # Ensure wind structure exists with defaults if not present
                if self._safe_get_env_param("wind.speed", None) is None:
                    self._safe_set_env_param("wind.speed", 0.0)
                if self._safe_get_env_param("wind.direction", None) is None:
                    self._safe_set_env_param("wind.direction", 0.0)
                print("🌬️  Wind enabled")

            # Initialize current parameters
            if not self.config.is_current_enabled():
                self._safe_set_env_param("current.speed", 0.0)
                self._safe_set_env_param("current.direction", 0.0)
                print("🌊 Current disabled - set to zero")
            else:
                # Ensure current structure exists with defaults if not present
                if self._safe_get_env_param("current.speed", None) is None:
                    self._safe_set_env_param("current.speed", 0.0)
                if self._safe_get_env_param("current.direction", None) is None:
                    self._safe_set_env_param("current.direction", 0.0)
                print("🌊 Current enabled")

        except Exception as e:
            print(f"⚠️  Error initializing env_param: {e}")
            # Fallback: create minimal safe structure
            self.env_param = {
                'wind': {'speed': 0.0, 'direction': 0.0},
                'current': {'speed': 0.0, 'direction': 0.0}
            }
            print("🔧 Created fallback env_param structure")

    def _safe_set_env_param(self, param_path: str, value):
        """
        Safely set nested parameters in env_param.

        Args:
            param_path: Dot-separated path like "wind.speed" or "current.direction"
            value: Value to set
        """
        try:
            if not isinstance(self.env_param, dict):
                self.env_param = {}

            keys = param_path.split('.')
            current = self.env_param

            # Navigate to the parent of the target key
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                elif not isinstance(current[key], dict):
                    current[key] = {}
                current = current[key]

            # Set the final value
            current[keys[-1]] = value

        except Exception as e:
            print(f"Warning: Error setting env_param path '{param_path}': {e}")



    
    async def _run_simulation_step(self) -> None:
        """Run one complete simulation step."""
        # Run multiple physics steps for time acceleration
        self.simulation_step += 1

        # Simple debug
        if self.simulation_step == 100:
            print(f"SIMPLE_DEBUG: Step {self.simulation_step} reached!")

        # Update simulation time with accelerated timestep
        self.time_manager.advance_time(self.simulation_timestep)

        # Convert to dict for legacy functions (backward compatibility)
        ship_state_dict = self.ship_state.to_dict()

        # RSA mode: No autopilot control logic needed
        # Rudder commands come directly from RSA messages

        # Note: Local coordinates (x, y) are maintained by ship dynamics model
        # Only convert GPS to local coordinates during initialization
        # The ship dynamics model updates local coordinates directly

        # Get current components for ship dynamics
        if self.config.is_current_enabled():
            tidal_north, tidal_east = self.config.get_current_components()
            # Add current components to simulation_set for 62065 model
            self.simulation_set["tidal_north"] = tidal_north
            self.simulation_set["tidal_east"] = tidal_east
        else:
            self.simulation_set["tidal_north"] = 0.0
            self.simulation_set["tidal_east"] = 0.0

        # Prepare command for ship dynamics model
        command = {
            'rudder': ship_state_dict.get('rudder_cmd', 0.0)
        }

        # Calculate wave effects
        wave_effect = 0.0
        if self.wave_disturbance is not None:
            elapsed_time = self.time_manager.get_elapsed_time()
            wave_effect = self.wave_disturbance.get_Wb(elapsed_time)

        # Calculate tidal effects
        tidal_effects = self._calculate_tidal_effects(self.env_param)

        # Run ship dynamics model with loop timestep and time acceleration
        # The physics model will apply time acceleration internally
        ship_state_dict = self.ship_model.calculate_dynamics(
            ship_state_dict, self.ship_param, command, self.loop_timestep,
            wave_effect, tidal_effects, self.time_acceleration
        )

        # Convert local to global coordinates
        global_lat, global_lon = self.coordinate_transformer.local_to_global(
            ship_state_dict["x"], ship_state_dict["y"]
        )
        ship_state_dict["lat"] = global_lat
        ship_state_dict["lon"] = global_lon

        # Check and update reference point for long-distance navigation (SAFE VERSION)
        # Use simulation step count as time to avoid time calculation issues
        simulation_time = self.simulation_step * self.loop_timestep

        try:
            new_x, new_y, reference_updated = self.coordinate_transformer.check_and_update_reference(
                global_lat, global_lon, ship_state_dict["x"], ship_state_dict["y"], simulation_time
            )

            # Update coordinates if reference was updated
            if reference_updated:
                # CRITICAL: Update all position-related states to maintain physics continuity
                old_x, old_y = ship_state_dict["x"], ship_state_dict["y"]
                ship_state_dict["x"] = new_x
                ship_state_dict["y"] = new_y

                # SAFETY: Validate physics state consistency
                self._validate_physics_state(ship_state_dict)

                print(f"COORD_UPDATE: Physics coordinates updated to ({new_x:.1f}, {new_y:.1f})")
                print(f"COORD_UPDATE: Coordinate shift: ({old_x - new_x:.1f}, {old_y - new_y:.1f})")
                print(f"COORD_UPDATE: Velocities and accelerations remain unchanged for physics continuity")

        except Exception as e:
            print(f"COORD_ERROR: Safe fallback - coordinate update failed: {e}")
            # Continue with existing coordinates - no physics disruption

        # Update ShipState object with new GPS coordinates
        self.ship_state.lat = global_lat
        self.ship_state.lon = global_lon
        self.ship_state.x = ship_state_dict["x"]
        self.ship_state.y = ship_state_dict["y"]



        # Debug coordinate conversion every 100 steps
        if self.simulation_step % 100 == 0:
            # Calculate distance from origin
            distance_from_origin = (ship_state_dict['x']**2 + ship_state_dict['y']**2)**0.5

            # Calculate GPS coordinate changes
            lat_change = global_lat - self.lat_ini
            lon_change = global_lon - self.lon_ini

            # Convert GPS changes to meters for comparison
            lat_change_m = lat_change * 111000  # Approximate meters per degree latitude
            lon_change_m = lon_change * 111000 * math.cos(math.radians(global_lat))  # Longitude varies with latitude
            gps_distance = (lat_change_m**2 + lon_change_m**2)**0.5

            print(f"COORD_DEBUG: Step {self.simulation_step}")
            print(f"  Local: x={ship_state_dict['x']:.3f}m, y={ship_state_dict['y']:.3f}m, dist={distance_from_origin:.3f}m")
            print(f"  Global: lat={global_lat:.8f}°, lon={global_lon:.8f}°")
            print(f"  GPS change: Δlat={lat_change:.8f}° ({lat_change_m:.3f}m), Δlon={lon_change:.8f}° ({lon_change_m:.3f}m)")
            print(f"  GPS distance: {gps_distance:.3f}m vs Local distance: {distance_from_origin:.3f}m")
            print(f"  Coordinate error: {abs(gps_distance - distance_from_origin):.3f}m")
            print(f"  Time: loop_dt={self.loop_timestep:.3f}s, physics_dt={self.simulation_timestep:.3f}s, speed_up={self.time_acceleration}x")



        # Calculate rate of turn in degrees per minute
        ship_state_dict["ROT"] = ship_state_dict["r"] * 180 / math.pi * 60

        # Debug ROT calculation every 50 steps
        if self.simulation_step % 50 == 0:
            hdg_deg = ship_state_dict['psi'] * 180 / math.pi
            cog_deg = ship_state_dict['COG'] * 180 / math.pi
            beta_deg = ship_state_dict['beta'] * 180 / math.pi
            drift = hdg_deg - cog_deg

            print(f"ROT_DEBUG: Step {self.simulation_step}")
            print(f"  r (rad/s): {ship_state_dict['r']:.6f}")
            print(f"  ROT (deg/min): {ship_state_dict['ROT']:.3f}")
            print(f"  HDG (deg): {hdg_deg:.3f}")
            print(f"  COG (deg): {cog_deg:.3f}")
            print(f"  Beta (deg): {beta_deg:.3f}")
            print(f"  Drift (HDG-COG): {drift:.3f}°")
            print(f"  Velocities: ug={ship_state_dict['u']:.3f}, vg={ship_state_dict['v']:.3f}")
            print(f"  Expected ROT from r: {ship_state_dict['r'] * 180 / math.pi * 60:.3f} deg/min")

        # Update ship state object from dict
        self.ship_state.update_from_dict(ship_state_dict)



        # Update derived quantities
        self.ship_state.update_derived_quantities()

    def _validate_physics_state(self, ship_state_dict: Dict[str, Any]) -> None:
        """
        Validate physics state consistency.

        Ensures reasonable values for ship dynamics.
        """
        # Check velocity magnitudes are reasonable
        u, v = ship_state_dict.get("u", 0), ship_state_dict.get("v", 0)
        speed = math.sqrt(u**2 + v**2)

        if speed > 50:  # 50 m/s = ~97 knots (unrealistic for ships)
            print(f"PHYSICS_WARNING: Excessive speed detected: {speed:.1f} m/s")

        # Check angular velocity is reasonable
        r = ship_state_dict.get("r", 0)
        rot_deg_per_sec = abs(r) * 180 / math.pi

        if rot_deg_per_sec > 10:  # 10 deg/s is very fast for ships
            print(f"PHYSICS_WARNING: Excessive rotation after coord update: {rot_deg_per_sec:.1f} deg/s")

        # Basic validation completed
        print(f"PHYSICS_VALIDATION: Speed={speed:.1f}m/s, ROT={rot_deg_per_sec:.1f}deg/s")

    def _convert_rpm_to_lever(self, rpm_value: float) -> float:
        """
        Convert RPM value to lever value using linear fitting.

        Linear mapping: RPM [min, max] → Lever [0.0, 1.0]
        Example: RPM=50 with range [0, 100] → Lever=0.5

        Args:
            rpm_value: RPM value from HIRPM message

        Returns:
            Lever value between 0.0 and 1.0
        """
        # Get RPM range from ship configuration
        ship_params = self.config.get_ship_params()
        rpm_min = ship_params.get('min', 0)
        rpm_max = ship_params.get('max', 500)

        # Clamp RPM value to valid range
        rpm_clamped = max(rpm_min, min(rpm_max, rpm_value))

        # Linear mapping: [rpm_min, rpm_max] → [0.0, 1.0]
        if rpm_max == rpm_min:
            lever_value = 0.0  # Avoid division by zero
        else:
            lever_value = (rpm_clamped - rpm_min) / (rpm_max - rpm_min)

        return lever_value

    def _calculate_tidal_effects(self, env_param) :
        """Calculate tidal current effects."""

        current = env_param.get("current", {})
        if not current.get("enabled", False):
            print("Current disturbance disabled")
            print("No tidal effects applied")
            return {'TN': 0.0, 'TE': 0.0}

        current_direction = current.get("direction", 0.0)
        current_speed = current.get("speed", 0.0)

        # Calculate tidal current components (North/East)
        # Direction is in degrees (0° = North, 90° = East, clockwise)
        import math

        # Convert direction to radians
        direction_rad = math.radians(current_direction)

        # Calculate North and East components
        # North component: speed * cos(direction)
        # East component: speed * sin(direction)
        tidal_north = current_speed * math.cos(direction_rad)
        tidal_east = current_speed * math.sin(direction_rad)

        # Convert from knots to m/s (정답지와 동일)
        KNOTS_TO_MS = 0.5144
        TN = tidal_north * KNOTS_TO_MS
        TE = tidal_east * KNOTS_TO_MS

        # 정답지 방식: 글로벌 좌표계에서 전달, 변환은 ship model 내부에서 수행
        return {'TN': TN, 'TE': TE}

    async def _handle_timing(self, start_time: float) -> None:
        """
        Handle simulation timing and sleep intervals.

        Args:
            start_time: Time when current cycle started
        """
        end_time = time.time()

        interval = end_time - start_time
        sleep_time = max(0, 1.0 - interval)

        # Print debugging information periodically
        if self.simulation_step % 10 == 0:  # Every 10 steps
            self._print_debug_info()
            print(f"[cycle_time] {interval:.3f}s [sleep] {sleep_time:.3f}s")

        await asyncio.sleep(sleep_time)

    def _print_debug_info(self) -> None:
        """Print debugging information using ShipState."""
        print(
            f'Step {self.simulation_step}: '
            f'[hdg]{self.ship_state.get_heading_degrees():.1f}° '
            f'[rud]{self.ship_state.rudder_cmd:.1f}°'
        )
        print(
            f'[rot]{self.ship_state.ROT:.1f} '
            f'[sog]{self.ship_state.SOG:.1f}kts '
            f'[pos]({self.ship_state.lat:.6f}, {self.ship_state.lon:.6f})'
        )
        print("-" * 80)
    
    def get_simulation_status(self) -> Dict[str, Any]:
        """
        Get current simulation status using ShipState.

        Returns:
            Dictionary containing simulation status
        """
        return {
            "simulation_step": self.simulation_step,
            "ship_state": self.ship_state.get_summary(),
            "communication_status": self.communication.get_connection_info(),
            "time_info": self.time_manager.get_time_info(),
            "rsa_interface_status": self.rsa_interface.get_interface_status(),
        }
    
    def stop_simulation(self) -> None:
        """Stop the simulation and cleanup resources."""
        self.communication.close_communication()
        print("Simulation stopped")
