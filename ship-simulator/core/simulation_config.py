"""
Simulation configuration manager for ship simulator.
Loads and manages all simulation parameters from JSON configuration.
"""

import json
import math
import os
from pathlib import Path
from typing import Dict, <PERSON><PERSON>, Any

class SimulationConfig:
    """
    Manages simulation configuration from JSON file.
    
    Loads and provides access to:
    - Simulation settings (ship_type, simulation_speed_up, etc.)
    - Initial conditions (position, speed, course)
    - Environment parameters (wind, current, wave)
    - Ship parameters (loaded from ship_models/configs)
    """
    
    def __init__(self, config_file: str = None):
        """
        Initialize simulation configuration.

        Args:
            config_file: Path to JSON configuration file
        """
        # Configuration file path
        self.config_file = config_file or self._get_default_config_path()

        # Load configuration from JSON
        self.config = {}
        self.simulation_set = {}
        self.initial_param = {}
        self.env_param = {}
        self.ship_param = {}

        self._load_config()

        # Load ship parameters
        self._load_ship_parameters()
        
        # Constants
        self.DEGREE_TO_RADIAN = math.pi / 180.0
        self.RADIAN_TO_DEGREE = 180.0 / math.pi
        self.KNOTS_TO_MS = 0.5144
        self.MS_TO_KNOTS = 1.0 / 0.5144
    
    def _get_default_config_path(self) -> str:
        """Get default configuration file path."""
        current_dir = Path(__file__).parent.parent
        config_path = current_dir / "config" / "simulation_config.json"
        return str(config_path)
    
    def _load_config(self) -> None:
        """Load configuration from JSON file."""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"Config file not found: {self.config_file}")

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)

            self.simulation_set = self.config.get('simulation_set', {})
            self.initial_param = self.config.get('initial', {})
            self.env_param = self.config.get('environment', {})

            # Get simulation mode
            self.simulation_mode = self.simulation_set.get('simulation_mode', 'normal')

            print(f"Loaded simulation config from: {self.config_file}")
            print(f"Simulation mode: {self.simulation_mode}")

        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in config file: {e}")
        except Exception as e:
            raise RuntimeError(f"Error loading simulation config: {e}")
    
    

    
    def _load_ship_parameters(self) -> None:
        """Load ship parameters based on ship_type."""
        ship_type = self.simulation_set.get("ship_type", 2)
        
        try:
            from ship_models.ship_config_loader import ShipConfigLoader
            ship_config_loader = ShipConfigLoader()
            self.ship_param = ship_config_loader.get_ship_parameters(ship_type)
        except Exception as e:
            raise RuntimeError(f"Error loading ship parameters: {e}")
    

    
    # Public interface methods
    def get_simulation_parameters(self) -> Dict[str, Any]:
        """Get all simulation parameters."""
        return {
            "simulation_set": self.simulation_set,
            "env_param": self.env_param,
            "ship_param": self.ship_param
        }
    
    def get_initial_position(self) -> Tuple[float, float, float]:
        """
        Get initial position and course.

        Supports both decimal degrees and DMS format for position.

        Returns:
            Tuple of (latitude, longitude, course) in decimal degrees
        """
        position = self.initial_param.get("position", {})
        course = self.initial_param.get("course", None)

        # Check if position is a string (DMS format)
        if isinstance(position, str):
            try:
                from utils.coordinate_formatter import parse_position_string
                lat, lon = parse_position_string(position)
                print(f"Parsed DMS position '{position}' -> ({lat:.6f}, {lon:.6f})")
                return lat, lon, course
            except Exception as e:
                raise ValueError(f"Error parsing DMS position '{position}': {e}")

        # Handle dictionary format (decimal degrees)
        elif isinstance(position, dict):
            lat = position.get("lat", None)
            lon = position.get("lon", None)

            # Check if lat/lon are strings (individual DMS coordinates)
            if isinstance(lat, str):
                try:
                    from utils.coordinate_formatter import parse_dms_coordinate
                    lat = parse_dms_coordinate(lat)
                    print(f"Parsed DMS latitude: {lat:.6f}")
                except Exception as e:
                    raise ValueError(f"Error parsing DMS latitude '{lat}': {e}")

            if isinstance(lon, str):
                try:
                    from utils.coordinate_formatter import parse_dms_coordinate
                    lon = parse_dms_coordinate(lon)
                    print(f"Parsed DMS longitude: {lon:.6f}")
                except Exception as e:
                    raise ValueError(f"Error parsing DMS longitude '{lon}': {e}")

            return lat, lon, course

        else:
            raise ValueError(f"Invalid position format: {position}. Expected string (DMS) or dict with lat/lon.")
    
    def get_initial_speed(self) -> float:
        """Get initial speed in knots."""
        return self.initial_param.get("speed", None)

    def get_initial_lever(self) -> float:
        """Get initial lever position."""
        return self.initial_param.get("lever", 0.0)
    
    def get_ship_type(self) -> int:
        """Get ship type."""
        return self.simulation_set.get("ship_type", None)
    
    def get_simulation_speed_up(self) -> float:
        """Get simulation speed up factor."""
        return self.simulation_set.get("simulation_speed_up", 1.0)
    
    def get_environment_params(self) -> Dict[str, Any]:
        """Get environment parameters."""
        return self.env_param.copy()
    
    def get_ship_params(self) -> Dict[str, Any]:
        """Get ship parameters."""
        return self.ship_param.copy()
    
    def get_current_components(self) -> Tuple[float, float]:
        """Get current north/east components in knots."""
        if "current" in self.env_param:
            current_config = self.env_param["current"]
            tidal_north = current_config.get("tidal_north", 0.0)
            tidal_east = current_config.get("tidal_east", 0.0)
            return (tidal_north, tidal_east)

        return (0.0, 0.0)
    
    def is_current_enabled(self) -> bool:
        """Check if current is enabled."""
        return self.env_param.get("current", {}).get("enabled", False)
    
    def is_wind_enabled(self) -> bool:
        """Check if wind is enabled."""
        return self.env_param.get("wind", {}).get("enabled", False)
    
    def is_wave_enabled(self) -> bool:
        """Check if wave is enabled."""
        return self.env_param.get("wave", {}).get("enabled", False)

    def get_config_info(self) -> Dict[str, Any]:
        """Get configuration information."""
        return {
            "config_file": self.config_file,
            "simulation_set": self.simulation_set,
            "initial_param": self.initial_param,
            "environment": self.env_param,
            "ship_type": self.get_ship_type(),
            "current_enabled": self.is_current_enabled(),
            "wind_enabled": self.is_wind_enabled(),
            "wave_enabled": self.is_wave_enabled()
        }


# Example usage and testing
if __name__ == "__main__":
    # Test simulation configuration
    config = SimulationConfig()

    print("Simulation Configuration Test")
    print("=" * 40)

    # Test configuration loading
    info = config.get_config_info()
    print(f"Config file: {info['config_file']}")
    print(f"Ship type: {info['ship_type']}")
    print(f"Simulation speed up: {config.get_simulation_speed_up()}")

    # Test initial conditions
    lat, lon, course = config.get_initial_position()
    speed = config.get_initial_speed()
    print(f"Initial position: ({lat:.6f}, {lon:.6f})")
    print(f"Initial speed: {speed} knots")
    print(f"Initial course: {course}°")

    # Test environment
    print(f"Current enabled: {config.is_current_enabled()}")
    print(f"Wind enabled: {config.is_wind_enabled()}")
    print(f"Wave enabled: {config.is_wave_enabled()}")

    # Test current components
    tidal_north, tidal_east = config.get_current_components()
    print(f"Current components: N={tidal_north:.3f}, E={tidal_east:.3f} knots")
