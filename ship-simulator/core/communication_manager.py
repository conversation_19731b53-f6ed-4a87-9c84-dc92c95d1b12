"""
Communication manager for ship simulator.
Handles UDP socket communication using UDPSocketBase.
"""

import asyncio
import json
import os
import socket
from typing import Dict, Optional
from utils.udp_socket_base import UDPSocketBase


class CommunicationManager:
    """
    Manages UDP network communication for the ship simulator using UDPSocketBase.
    
    Handles:
    - Message reception from autopilot (port 4001)
    - NMEA sentence transmission to parser (port 6501)
    - Independent simulator operation
    """
    
    def __init__(self, config_file: str = "config/communication.json"):
        """
        Initialize communication manager with configuration file.

        Args:
            config_file: Path to communication configuration JSON file
        """
        self.config = self._load_config(config_file)
        self.communication_mode = self.config.get("communication_mode", "multicast")

        # Extract configuration based on mode
        if self.communication_mode == "multicast":
            multicast_config = self.config["multicast"]
            self.target_ip = multicast_config["group_ip"]
            self.target_port = multicast_config["port"]
            self.ttl = multicast_config.get("ttl", 10)
            self.interface_ip = multicast_config.get("interface_ip", "***********")
        else:  # unicast
            unicast_config = self.config["unicast"]
            self.target_ip = unicast_config["target_ip"]
            self.target_port = unicast_config["port"]
            self.ttl = None
            self.interface_ip = None

        # Simulator server configuration
        simulator_config = self.config["simulator_server"]
        self.server_ip = simulator_config["ip"]
        self.server_port = simulator_config["port"]

        # Additional unicast configuration for autopilot-simulator
        autopilot_config = self.config.get("autopilot_unicast", {})
        self.autopilot_ip = autopilot_config.get("target_ip", "***********")
        self.autopilot_port = autopilot_config.get("port", 50000)
        self.autopilot_messages = autopilot_config.get("messages", ["GGA", "VTG", "HDT", "ROT", "VBW"])

        print(f"Communication Mode: {self.communication_mode.upper()}")
        print(f"  Target: {self.target_ip}:{self.target_port}")
        if self.communication_mode == "multicast":
            print(f"  Interface: {self.interface_ip}, TTL: {self.ttl}")
        print(f"  Simulator Server: {self.server_ip}:{self.server_port}")
        print(f"  Autopilot Unicast: {self.autopilot_ip}:{self.autopilot_port}")
        print(f"  Autopilot Messages: {', '.join(self.autopilot_messages)}")

        # Socket references
        self.rsa_receiver: Optional[UDPSocketBase] = None
        self.nmea_sender: Optional[UDPSocketBase] = None
        self.autopilot_sender: Optional[UDPSocketBase] = None

        # 호환성을 위한 속성들 (기존 코드에서 사용)
        self.multicast_ip = self.target_ip
        self.parser_port = self.target_port

    def _load_config(self, config_file: str) -> dict:
        """Load communication configuration from JSON file."""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ Config file {config_file} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            print(f"⚠️ Error parsing {config_file}: {e}, using defaults")
            return self._get_default_config()

    def _get_default_config(self) -> dict:
        """Get default configuration."""
        return {
            "communication_mode": "multicast",
            "multicast": {
                "group_ip": "***********",
                "port": 60001,
                "ttl": 10,
                "interface_ip": "***********"
            },
            "unicast": {
                "target_ip": "***********",
                "port": 60001
            },
            "simulator_server": {
                "ip": "***********",
                "port": 59902
            }
        }

    def setup_communication(self) -> None:
        """Setup UDP sockets for simulator command reception and NMEA transmission (multicast or unicast)."""
        # Socket for receiving RSA commands from autopilot
        self.rsa_receiver = UDPSocketBase(
            local_ip=self.server_ip,
            local_port=self.server_port,
            is_receiver=True,
            is_sender=False,
            is_auto_set=True  # Back to auto setup
        )

        # Socket for sending NMEA sentences (multicast or unicast based on config)
        self.nmea_sender = UDPSocketBase(
            remote_ip=self.target_ip,
            remote_port=self.target_port,
            is_receiver=False,
            is_sender=True,
            is_auto_set=True
        )

        # Socket for sending specific NMEA sentences to autopilot via unicast
        print(f"Initializing autopilot sender to {self.autopilot_ip}:{self.autopilot_port}")
        self.autopilot_sender = UDPSocketBase(
            remote_ip=self.autopilot_ip,
            remote_port=self.autopilot_port,
            is_receiver=False,
            is_sender=True,
            is_auto_set=True
        )
        print(f"Autopilot sender initialized successfully")
        
    async def receive_simulator_message(self) -> Optional[dict]:
        """
        Asynchronously receive simulator messages (RSA, RPM, etc.).

        MULTI-MESSAGE SUPPORT: Handles RSA (rudder) and RPM (engine) messages
        from the same socket without losing any messages.

        Returns:
            Dictionary with message data, or None if no valid message
        """
        if not self.rsa_receiver:
            raise RuntimeError("RSA receiver not initialized")

        try:
            # LIGHT FLUSH: Just check 2 times to avoid losing messages
            latest_message = None
            flush_count = 0

            # Quick 2-check flush (minimal to avoid blocking)
            for _ in range(2):
                data = await self.rsa_receiver.receive_data()
                if data:
                    latest_message = data.decode('utf-8').strip()
                    flush_count += 1
                else:
                    break

            # Show flush only if multiple messages
            if flush_count > 1:
                print(f"🔄 LIGHT FLUSH: {flush_count-1} old messages discarded")

            if latest_message:
                # Parse message based on type
                if 'RSA' in latest_message:
                    return self._parse_rsa_message(latest_message)
                elif 'RPM' in latest_message:
                    return self._parse_rpm_message(latest_message)
                else:
                    return None

            return None
        except Exception as e:
            print(f"Error receiving simulator message: {e}")
            return None

    def _parse_rsa_message(self, message: str) -> Optional[dict]:
        """
        Parse RSA NMEA message to extract rudder angle and status.

        RSA format: $AGRSA,starboard_angle,starboard_status,port_angle,port_status*checksum

        Args:
            message: Raw RSA NMEA message

        Returns:
            Dictionary with rudder data, or None if parsing fails
        """
        try:
            # Remove any trailing whitespace and split by comma
            parts = message.strip().split(',')

            # Check if it's an RSA message (rudder angle) - support all common formats
            if len(parts) >= 2 and parts[0] in ['$AGRSA', '$IIRSA', '$YXRSA', '$HIRSA', '$GPRSA', '$HCRSA']:
                # Extract rudder angle and status from fields
                starboard_angle_str = parts[1]
                starboard_status = parts[2] if len(parts) > 2 else "V"
                port_angle_str = parts[3] if len(parts) > 3 else ""
                port_status = parts[4] if len(parts) > 4 else "V"

                # Remove checksum from port_status if present
                if '*' in port_status:
                    port_status = port_status.split('*')[0]

                if starboard_angle_str and starboard_angle_str != '':
                    result = {
                        'message_type': 'RSA',
                        'rudder_angle': float(starboard_angle_str),
                        'rudder_status': starboard_status,
                        'rudder_angle_port': float(port_angle_str) if port_angle_str and port_angle_str != '' else float(starboard_angle_str),
                        'rudder_status_port': port_status
                    }
                    return result

            return None
        except (ValueError, IndexError) as e:
            print(f"Error parsing RSA message '{message}': {e}")
            return None

    def _parse_rpm_message(self, message: str) -> Optional[dict]:
        """
        Parse RPM NMEA message to extract engine data.

        RPM format: $HIRPM,E,engine_number,rpm,pitch,status*checksum
        Example: $HIRPM,E,1,43.5,10.0,A*51

        Args:
            message: Raw RPM NMEA message

        Returns:
            Dictionary with engine data, or None if parsing fails
        """
        try:
            # Remove any trailing whitespace and split by comma
            parts = message.strip().split(',')

            # Check if it's an RPM message (engine data)
            if len(parts) >= 6 and parts[0] in ['$HIRPM']:
                source = parts[1]  # Usually 'E' for Engine
                engine_number_str = parts[2]
                rpm_str = parts[3]
                pitch_str = parts[4]
                status_str = parts[5]

                # Remove checksum from status if present
                if '*' in status_str:
                    status_str = status_str.split('*')[0]

                if rpm_str and rpm_str != '':
                    result = {
                        'message_type': 'RPM',
                        'source': source,
                        'engine_number': int(engine_number_str) if engine_number_str.isdigit() else 1,
                        'rpm': float(rpm_str),
                        'pitch': float(pitch_str) if pitch_str and pitch_str != '' else 0.0,
                        'status': status_str
                    }
                    return result

            return None
        except (ValueError, IndexError) as e:
            print(f"Error parsing RPM message '{message}': {e}")
            return None

    def send_navigation_data_multicast(self, nmea_sentences: Dict[str, str]) -> None:
        """
        Send navigation NMEA sentences via multicast.

        Args:
            nmea_sentences: Dictionary of NMEA sentence types and their content
        """
        if not self.nmea_sender:
            raise RuntimeError("NMEA sender not initialized")

        # Initialize sample tracking
        if not hasattr(self, '_sample_shown'):
            self._sample_shown = set()

        # Send all NMEA sentences via multicast
        for sentence_key, sentence in nmea_sentences.items():
            try:
                # Show sample NMEA 0450 data (once per sentence type)
                if sentence_key not in self._sample_shown:
                    print(f"📡 NMEA 0450 Sample [{sentence_key}]: {sentence.strip()}")
                    self._sample_shown.add(sentence_key)

                self.nmea_sender.send_data(sentence.encode('utf-8'))
            except Exception as e:
                print(f"Error sending {sentence_key} via multicast: {e}")

    def _convert_to_nmea0418(self, nmea_sentence: str) -> str:
        """
        Convert NMEA sentence to NMEA 0418 format for autopilot.

        Args:
            nmea_sentence: Original NMEA sentence

        Returns:
            NMEA 0418 formatted sentence
        """
        # Extract pure NMEA sentence from NMEA 0450 format if needed
        if 'UdPbC' in nmea_sentence and '$' in nmea_sentence:
            # Extract the NMEA part after the UdPbC header
            nmea_part = nmea_sentence[nmea_sentence.find('$'):]
        else:
            nmea_part = nmea_sentence

        # NMEA 0418 format: simple NMEA sentence without additional headers
        return nmea_part.strip()

    def send_navigation_data_to_autopilot(self, nmea_sentences: Dict[str, str]) -> None:
        """
        Send specific NMEA sentences to autopilot via unicast in NMEA 0418 format.
        Optimized for low latency - sends critical messages first.

        Args:
            nmea_sentences: Dictionary of NMEA sentence types and their content
        """
        if not self.autopilot_sender:
            raise RuntimeError("Autopilot sender not initialized")

        # Initialize sample tracking for autopilot
        if not hasattr(self, '_autopilot_sample_shown'):
            self._autopilot_sample_shown = set()

        # Priority order for autopilot messages (most critical first)
        priority_order = ['HDT', 'ROT', 'GGA', 'VTG', 'VBW']

        # Send priority messages first
        sent_keys = set()
        for priority_type in priority_order:
            for sentence_key, sentence in nmea_sentences.items():
                base_type = sentence_key.split('_')[0] if '_' in sentence_key else sentence_key

                if base_type == priority_type and base_type in self.autopilot_messages:
                    try:
                        # Convert to NMEA 0418 format
                        nmea_0418_sentence = self._convert_to_nmea0418(sentence)

                        # Show sample data (once per sentence type)
                        sample_key = f"autopilot_{sentence_key}"
                        if sample_key not in self._autopilot_sample_shown:
                            print(f"Autopilot NMEA 0418 Sample [{sentence_key}]: {nmea_0418_sentence}")
                            self._autopilot_sample_shown.add(sample_key)

                        self.autopilot_sender.send_data(nmea_0418_sentence.encode('utf-8'))
                        sent_keys.add(sentence_key)
                    except Exception as e:
                        print(f"Error sending {sentence_key} to autopilot: {e}")

        # Send remaining messages
        for sentence_key, sentence in nmea_sentences.items():
            if sentence_key not in sent_keys:
                base_type = sentence_key.split('_')[0] if '_' in sentence_key else sentence_key

                if base_type in self.autopilot_messages:
                    try:
                        # Convert to NMEA 0418 format
                        nmea_0418_sentence = self._convert_to_nmea0418(sentence)

                        # Show sample data (once per sentence type)
                        sample_key = f"autopilot_{sentence_key}"
                        if sample_key not in self._autopilot_sample_shown:
                            print(f"Autopilot NMEA 0418 Sample [{sentence_key}]: {nmea_0418_sentence}")
                            self._autopilot_sample_shown.add(sample_key)

                        self.autopilot_sender.send_data(nmea_0418_sentence.encode('utf-8'))
                    except Exception as e:
                        print(f"Error sending {sentence_key} to autopilot: {e}")

    async def send_navigation_data_async(self, nmea_sentences: Dict[str, str]) -> None:
        """
        Send navigation data to both multicast and autopilot in parallel (async).

        Args:
            nmea_sentences: Dictionary of NMEA sentence types and their content
        """
        import asyncio
        import time

        start_time = time.perf_counter()

        tasks = []

        # Create autopilot task (higher priority)
        if hasattr(self, 'autopilot_sender') and self.autopilot_sender:
            tasks.append(asyncio.create_task(self._send_autopilot_async(nmea_sentences)))

        # Create multicast task
        tasks.append(asyncio.create_task(self._send_multicast_async(nmea_sentences)))

        # Execute both tasks in parallel
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Debug timing (only show occasionally)
            if hasattr(self, '_timing_counter'):
                self._timing_counter += 1
            else:
                self._timing_counter = 1

            if self._timing_counter % 100 == 1:  # Every 100 calls
                elapsed = (time.perf_counter() - start_time) * 1000
                print(f"TIMING: Parallel send took {elapsed:.2f}ms")

    async def _send_autopilot_async(self, nmea_sentences: Dict[str, str]) -> None:
        """Async wrapper for autopilot sending."""
        self.send_navigation_data_to_autopilot(nmea_sentences)

    async def _send_multicast_async(self, nmea_sentences: Dict[str, str]) -> None:
        """Async wrapper for multicast sending."""
        self.send_navigation_data_multicast(nmea_sentences)

    def send_navigation_data(self, nmea_sentences: Dict[str, str]) -> None:
        """
        Send navigation data to both multicast and autopilot (synchronous version).
        Autopilot is sent first for lower latency.

        Args:
            nmea_sentences: Dictionary of NMEA sentence types and their content
        """
        # Send to autopilot FIRST (higher priority, lower latency)
        if hasattr(self, 'autopilot_sender') and self.autopilot_sender:
            self.send_navigation_data_to_autopilot(nmea_sentences)

        # Then send multicast
        self.send_navigation_data_multicast(nmea_sentences)
    
    def send_single_sentence(self, nmea_sentence: str) -> None:
        """
        Send a single NMEA sentence to parser port.
        
        Args:
            nmea_sentence: NMEA sentence string to send
        """
        if not self.data_sender:
            raise RuntimeError("Data sender not initialized")
        
        self.data_sender.send_data(nmea_sentence.encode('utf-8'))
    
    def send_to_custom_destination(self, data: str, target_ip: str, target_port: int) -> None:
        """
        Send data to a custom IP and port.
        
        Args:
            data: Data to send
            target_ip: Target IP address
            target_port: Target port number
        """
        if not self.data_sender:
            raise RuntimeError("Data sender not initialized")
        
        self.data_sender.send_data(data.encode('utf-8'), target_ip, target_port)
    
    def close_communication(self) -> None:
        """Close all communication sockets."""
        if self.rsa_receiver:
            self.rsa_receiver.close_socket()
            self.rsa_receiver = None

        if self.nmea_sender:
            self.nmea_sender.close_socket()
            self.nmea_sender = None
    
    def clear_receive_buffer(self) -> None:
        """Clear the receive buffer."""
        if self.rsa_receiver:
            # Note: UDPSocketBase doesn't have clear_buffer method
            # Buffer is automatically cleared on each receive
            pass
    
    def get_connection_info(self) -> Dict[str, any]:
        """
        Get current connection information.

        Returns:
            Dictionary with connection details
        """
        return {
            "multicast_ip": self.multicast_ip,
            "parser_port": self.parser_port,
            "server_ip": self.server_ip,
            "server_port": self.server_port,
            "rsa_receiver_ready": self.rsa_receiver is not None,
            "nmea_sender_ready": self.nmea_sender is not None,
            "receiving_from": f"RSA commands on {self.server_ip}:{self.server_port}",
            "sending_to": f"Multicast {self.multicast_ip}:{self.parser_port}"
        }
    
    def is_ready(self) -> bool:
        """
        Check if communication manager is ready for operation.

        Returns:
            True if both sockets are initialized
        """
        return (self.rsa_receiver is not None and
                self.nmea_sender is not None)
    
    def get_status(self) -> Dict[str, any]:
        """
        Get detailed status information.
        
        Returns:
            Dictionary with status information
        """
        return {
            "ready": self.is_ready(),
            "connection_info": self.get_connection_info(),
            "description": {
                "rsa_receiver": f"Listening on {self.server_ip}:{self.server_port} for RSA commands",
                "nmea_sender": f"Sending NMEA data via multicast to {self.multicast_ip}:{self.parser_port}"
            }
        }
