"""
Core Ship Simulator Package

A modular ship simulation system with the following components:
- SimulationConfig: Manages simulation parameters and initial conditions
- MessageParser: Parses incoming NMEA messages from autopilot
- TimeManager: Handles UTC time tracking and NMEA time formatting
- ShipSimulator: Main orchestrator class that coordinates all components

Usage:
    from core import ShipSimulator

    # Create and run simulator
    simulator = ShipSimulator(lat=37.7749, lon=-122.4194, speed=10, course=90)
    await simulator.run_simulation()
"""

from .ship_simulator import ShipSimulator
from .communication_manager import CommunicationManager
from .simulation_config import SimulationConfig
from .autopilot_interface import AutopilotInterface, MessageParser
from .time_manager import TimeManager
from .ship_state import ShipState
from .navigation_data import NavigationDataGenerator, make_nmea_sentence
from .coordinate_transformer import CoordinateTransformer, initialize_coordinate_transformer

__version__ = "1.0.0"
__author__ = "Ship Simulator Team"

__all__ = [
    "ShipSimulator",
    "CommunicationManager",
    "SimulationConfig",
    "AutopilotInterface",
    "MessageParser",  # Backward compatibility
    "TimeManager",
    "ShipState",
    "NavigationDataGenerator",
    "make_nmea_sentence",
    "CoordinateTransformer",
    "initialize_coordinate_transformer",
]
