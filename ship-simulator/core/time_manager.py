"""
Time management for ship simulator based on GlobalTimer.
Handles simulation time, real-time synchronization, and time-related utilities.
"""

import time
from datetime import datetime, timezone
from typing import Tuple


class GlobalTimer:
    """
    Global timer for simulation with speed control.
    """

    def __init__(self, start_time_us: int, speed_up: float):
        """
        Initialize global timer.

        Args:
            start_time_us: Start time in microseconds
            speed_up: Speed multiplier for simulation
        """
        self.start_time_us = start_time_us
        self.speed_up = speed_up
        self.start_monotonic = time.monotonic()

    def set_start_time(self, start_time_us: int):
        """Set start time in microseconds."""
        self.start_time_us = start_time_us

    def now(self) -> int:
        """
        Get current virtual time in microseconds.

        Returns:
            Virtual time in microseconds
        """
        elapsed_monotonic = time.monotonic() - self.start_monotonic
        time_elapsed_us = elapsed_monotonic * 1_000_000 * self.speed_up
        return int(self.start_time_us + time_elapsed_us)

    def now_datetime(self) -> datetime:
        """
        Get current virtual time as datetime object with UTC timezone.

        Returns:
            Virtual time as UTC datetime
        """
        virtual_us = self.now()
        return datetime.fromtimestamp(virtual_us / 1_000_000, tz=timezone.utc)

    def us_to_datetime(self, us: int) -> datetime:
        """
        Convert microseconds to datetime object.

        Args:
            us: Time in microseconds

        Returns:
            Datetime object with UTC timezone
        """
        return datetime.fromtimestamp(us / 1_000_000, tz=timezone.utc)

    def set_speed(self, speed_up: float):
        """Set simulation speed multiplier."""
        self.speed_up = speed_up

    def get_start_time(self) -> int:
        """Get start time in microseconds."""
        return self.start_time_us

    def get_start_datetime(self) -> datetime:
        """
        Get start time as UTC datetime.

        Returns:
            Start time as UTC datetime
        """
        return datetime.fromtimestamp(self.start_time_us / 1_000_000, tz=timezone.utc)


# Global timer instance
__global_timer = None


def init_global_timer(start_time_us: int, speed_up: float):
    """
    Initialize global timer.

    Args:
        start_time_us: Start time in microseconds
        speed_up: Speed multiplier
    """
    global __global_timer
    __global_timer = GlobalTimer(start_time_us, speed_up)


def get_global_timer() -> GlobalTimer:
    """
    Get global timer instance.

    Returns:
        GlobalTimer instance

    Raises:
        RuntimeError: If timer is not initialized
    """
    if __global_timer is None:
        raise RuntimeError("GlobalTimer가 초기화되지 않았습니다!")
    return __global_timer


class TimeManager:
    """
    Time manager that wraps GlobalTimer for ship simulator.

    Provides simulation time management with speed control and
    NMEA time formatting capabilities.
    """

    def __init__(self, start_time: float = None, time_acceleration: float = 1.0):
        """
        Initialize time manager.

        Args:
            start_time: Starting simulation time (Unix timestamp)
            time_acceleration: Time acceleration factor (1.0 = real-time)
        """
        # Always use current real time for NMEA timestamps
        self.start_time = time.time()  # Current real time
        self.time_acceleration = time_acceleration

        # Initialize global timer with microsecond precision
        start_time_us = int(self.start_time * 1_000_000)
        init_global_timer(start_time_us, time_acceleration)

        self.timer = get_global_timer()
        self.simulation_start = time.time()
        self.paused = False
        self.pause_start_time = 0.0
        self.total_pause_duration = 0.0

    def reset_time(self) -> None:
        """Reset simulation time to start."""
        self.simulation_start = time.time()
        self.total_pause_duration = 0.0
        self.paused = False

        # Reinitialize global timer
        start_time_us = int(self.start_time * 1_000_000)
        init_global_timer(start_time_us, self.time_acceleration)
        self.timer = get_global_timer()

    def update_time(self) -> None:
        """Update current simulation time (handled by GlobalTimer)."""
        # GlobalTimer handles time updates automatically
        pass

    def get_current_time(self) -> float:
        """
        Get current simulation time.

        Returns:
            Current simulation time in seconds
        """
        return self.timer.now() / 1_000_000

    def get_real_time(self) -> float:
        """
        Get current real time.

        Returns:
            Current real time (Unix timestamp)
        """
        return time.time()

    def get_simulation_time(self) -> float:
        """
        Get absolute simulation time.

        Returns:
            Absolute simulation time
        """
        return self.timer.now() / 1_000_000

    def get_elapsed_time(self) -> float:
        """
        Get elapsed time since simulation start.

        Returns:
            Elapsed time in seconds since simulation start
        """
        current_time = self.timer.now() / 1_000_000
        return current_time - self.start_time

    def advance_time(self, delta_time: float) -> None:
        """
        Manually advance simulation time.

        Note: For real-time NMEA timestamps, this doesn't affect the time.
        GlobalTimer handles time progression automatically.

        Args:
            delta_time: Time to advance in seconds (ignored for real-time mode)
        """
        # In real-time mode, time advances automatically
        # No manual adjustment needed since we use real time for NMEA
        pass

    def set_time_acceleration(self, acceleration: float) -> None:
        """
        Set time acceleration factor.

        Args:
            acceleration: New acceleration factor
        """
        self.time_acceleration = acceleration
        self.timer.set_speed(acceleration)

    def pause(self) -> None:
        """Pause simulation time."""
        if not self.paused:
            self.paused = True
            self.pause_start_time = time.time()
            self.timer.set_speed(0.0)  # Stop time progression

    def resume(self) -> None:
        """Resume simulation time."""
        if self.paused:
            self.total_pause_duration += time.time() - self.pause_start_time
            self.paused = False
            self.timer.set_speed(self.time_acceleration)  # Restore speed

    def get_current_utc_time(self) -> datetime:
        """
        Get current UTC time for NMEA messages.
        Uses real time to avoid time acceleration issues in NMEA timestamps.

        Returns:
            Current real UTC datetime
        """
        # Use real time for NMEA timestamps, not accelerated simulation time
        real_time = datetime.now(timezone.utc)

        # Debug: Print time info occasionally
        if hasattr(self, '_time_debug_counter'):
            self._time_debug_counter += 1
        else:
            self._time_debug_counter = 1

        if self._time_debug_counter % 100 == 1:  # Every 100 calls
            virtual_time = self.timer.now_datetime()
            print(f"TIME_DEBUG: Real={real_time.strftime('%H:%M:%S')}, Virtual={virtual_time.strftime('%H:%M:%S')}, Speed={self.time_acceleration}x")

        return real_time

    def get_nmea_time_string(self) -> str:
        """
        Get time string in NMEA format (HHMMSS.sss).

        Returns:
            Time string in NMEA format
        """
        utc_time = self.get_current_utc_time()
        return utc_time.strftime("%H%M%S.%f")[:-3]  # Remove last 3 digits of microseconds

    def get_utc_time_info(self) -> Tuple[datetime, str]:
        """
        Get UTC time information for NMEA generation.

        Returns:
            Tuple of (datetime object, NMEA time string)
        """
        utc_time = self.get_current_utc_time()
        nmea_time = utc_time.strftime("%H%M%S.%f")[:-3]
        return utc_time, nmea_time

    def get_time_info(self) -> dict:
        """
        Get comprehensive time information.

        Returns:
            Dictionary with time information
        """
        utc_time = self.get_current_utc_time()
        return {
            "simulation_time": self.get_current_time(),
            "real_time": self.get_real_time(),
            "simulation_timestamp": self.get_simulation_time(),
            "elapsed_time": self.get_elapsed_time(),
            "utc_time": utc_time.isoformat(),
            "nmea_time": self.get_nmea_time_string(),
            "time_acceleration": self.time_acceleration,
            "paused": self.paused,
            "global_timer_us": self.timer.now()
        }
