"""
Enhanced ship state management for the ship simulator.
Provides a structured way to manage ship state data.
"""

import math
from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class ShipState:
    """
    Comprehensive ship state class that manages all ship-related state variables.
    
    This class provides a structured alternative to the dictionary-based ship_state
    while maintaining backward compatibility through conversion methods.
    """
    
    # Position and orientation
    lat: float = 0.0               # Latitude in degrees
    lon: float = 0.0               # Longitude in degrees
    x: float = 0.0                 # Local X position in meters
    y: float = 0.0                 # Local Y position in meters
    psi: float = 0.0               # Heading in radians
    
    # Velocities (ground reference)
    u: float = 0.0                 # Surge velocity in m/s
    v: float = 0.0                 # Sway velocity in m/s
    r: float = 0.0                 # Yaw rate in rad/s
    
    # Velocities (water reference)
    uw: float = 0.0                # Surge velocity relative to water in m/s
    vw: float = 0.0                # Sway velocity relative to water in m/s
    
    # Derived motion quantities
    SOG: float = 0.0               # Speed over ground in knots
    STW: float = 0.0               # Speed through water in knots
    COG: float = 0.0               # Course over ground in radians
    ROT: float = 0.0               # Rate of turn in degrees per minute
    beta: float = 0.0              # Drift angle in radians
    
    # Control states
    rudder_angle: float = 0.0      # Current rudder angle in degrees
    rudder_angle_port: float = 0.0 # Current port rudder angle in degrees
    rudder_cmd: float = 0.0        # Commanded rudder angle in degrees
    rudder_status: str = "V"       # Rudder status: A=Valid, V=Invalid
    rudder_status_port: str = "V"  # Port rudder status: A=Valid, V=Invalid
    lever: float = 0.0             # Current propeller lever position (-1 to 1)
    lever_cmd: float = 0.0         # Commanded propeller lever position
    uc: float = 0.0                # Commanded surge velocity
    rpm: float = 50.0              # Engine RPM
    
    # Dynamic states
    du: float = 0.0                # Surge acceleration in m/s²
    
    # System states
    time: float = 0.0              # Simulation time
    
    def __post_init__(self):
        """Post-initialization to handle special field names."""
        pass
    
    @classmethod
    def from_dict(cls, state_dict: Dict[str, Any]) -> 'ShipState':
        """
        Create ShipState from dictionary (backward compatibility).
        
        Args:
            state_dict: Dictionary containing ship state data
            
        Returns:
            ShipState instance
        """
        # Create instance with default values
        ship_state = cls()
        
        # Update with values from dictionary
        for key, value in state_dict.items():
            if key == 'rudder_cmd':
                ship_state.rudder_cmd = value
            elif hasattr(ship_state, key):
                setattr(ship_state, key, value)
        
        return ship_state
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert ShipState to dictionary (backward compatibility).
        
        Returns:
            Dictionary containing all ship state data
        """
        result = {}
        
        for field_name in self.__dataclass_fields__:
            if field_name == 'rudder_angle':
                result['rudder_angle'] = getattr(self, field_name)
            elif field_name == 'rudder_cmd':
                result['rudder_cmd'] = getattr(self, field_name)
            # lat and lon fields are used directly
            else:
                result[field_name] = getattr(self, field_name)
        
        return result
    
    def update_from_dict(self, state_dict: Dict[str, Any]) -> None:
        """
        Update current state from dictionary.
        
        Args:
            state_dict: Dictionary with updated values
        """
        for key, value in state_dict.items():
            if key == 'rudder_cmd':
                self.rudder_cmd = value
            elif hasattr(self, key):
                setattr(self, key, value)
    
    def get_position(self) -> tuple:
        """Get current position as (lat, lon)."""
        return (self.lat, self.lon)
    
    def get_local_position(self) -> tuple:
        """Get current local position as (x, y)."""
        return (self.x, self.y)
    
    def get_heading_degrees(self) -> float:
        """Get heading in degrees."""
        return math.degrees(self.psi)
    
    def get_cog_degrees(self) -> float:
        """Get course over ground in degrees."""
        return math.degrees(self.COG)
    
    def get_velocity_vector(self) -> tuple:
        """Get velocity vector as (u, v, r)."""
        return (self.u, self.v, self.r)
    
    def get_speed_knots(self) -> float:
        """Get speed over ground in knots."""
        return self.SOG
    
    def set_position(self, lat: float, lon: float) -> None:
        """Set global position."""
        self.lat = lat
        self.lon = lon
    
    def set_local_position(self, x: float, y: float) -> None:
        """Set local position."""
        self.x = x
        self.y = y
    
    def set_heading(self, heading_rad: float) -> None:
        """Set heading in radians."""
        self.psi = heading_rad
    
    def set_velocity(self, u: float, v: float, r: float) -> None:
        """Set velocity components."""
        self.u = u
        self.v = v
        self.r = r
    
    def update_derived_quantities(self) -> None:
        """Update derived quantities from basic state variables."""
        # Update speed over ground
        self.SOG = math.sqrt(self.u**2 + self.v**2) / 0.5144  # Convert m/s to knots
        
        # Update speed through water
        self.STW = math.sqrt(self.uw**2 + self.vw**2) / 0.5144  # Convert m/s to knots
        
        # Update rate of turn in degrees per minute
        self.ROT = self.r * 180 / math.pi * 60
        
        # Update drift angle
        if math.sqrt(self.u**2 + self.v**2) > 0:
            self.beta = math.atan2(self.v, self.u)
        else:
            self.beta = 0.0
        
        # Update course over ground
        self.COG = self.psi + self.beta
    
    def print_state(self) -> None:
        """Print current ship state in a readable format."""
        print("Ship State:")
        print(f"  Position: ({self.lat:.6f}°, {self.lon:.6f}°)")
        print(f"  Local Pos: ({self.x:.2f}m, {self.y:.2f}m)")
        print(f"  Heading: {self.get_heading_degrees():.1f}°")
        print(f"  Speed: {self.SOG:.2f} knots")
        print(f"  COG: {self.get_cog_degrees():.1f}°")
        print(f"  ROT: {self.ROT:.1f} deg/min")
        print(f"  Rudder: {self.rudder_angle:.1f}° (cmd: {self.rudder_cmd:.1f}°)")
        print(f"  Lever: {self.lever:.2f} (cmd: {self.lever_cmd:.2f})")
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of key state variables."""
        return {
            'position': {'lat': self.lat, 'lon': self.lon},
            'local_position': {'x': self.x, 'y': self.y},
            'motion': {
                'heading_deg': self.get_heading_degrees(),
                'speed_knots': self.SOG,
                'cog_deg': self.get_cog_degrees(),
                'rot_deg_min': self.ROT
            },
            'control': {
                'rudder_deg': self.rudder_angle,
                'rudder_cmd_deg': self.rudder_cmd,
                'lever': self.lever,
                'lever_cmd': self.lever_cmd
            }
        }
