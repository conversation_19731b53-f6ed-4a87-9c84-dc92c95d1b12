# pylint:disable=C0301,R0914,R0915
"""
Navigation data generator for ship simulator.
Generates NMEA 0450 sentences for navigation systems with enhanced precision and multi-GNSS support.
"""

import math
import os
import time
import threading
from datetime import datetime
from typing import Dict, Optional, Callable
from utils.coordinate_formatter import format_nmea_coordinates, format_coordinates_with_precision
from utils.nmea_config import get_nmea_config, get_sentence_header


class NavigationDataGenerator:
    """
    Generates NMEA 0450 sentences for ship navigation data with enhanced precision.

    Provides methods to create NMEA 0450 sentences including:
    - GGA: Global Positioning System Fix Data (enhanced precision)
    - GNS: GNSS Fix Data (multi-constellation)
    - RMC: Recommended Minimum Navigation Information (enhanced)
    - VTG: Track Made Good and Ground Speed (high precision)
    - HDT: Heading - True (enhanced precision)
    - ROT: Rate of Turn (enhanced precision)
    - VBW: Dual Ground/Water Speed (enhanced precision)
    - ZDA: Time & Date (enhanced)
    - MWV: Wind Speed and Angle (enhanced precision)
    - DPT: Depth of Water (enhanced precision)
    - GST: GNSS Pseudorange Error Statistics (new in 0450)
    """

    def __init__(self, config_file: str = "config/nmea_talkers.json",
                 auto_reload: bool = True, reload_callback: Optional[Callable] = None):
        """
        Initialize the navigation data generator.

        Args:
            config_file: Path to the NMEA talkers configuration file
            auto_reload: Enable automatic config file reloading when changed
            reload_callback: Optional callback function called when config is reloaded
        """
        self.config_file = config_file
        self.auto_reload = auto_reload
        self.reload_callback = reload_callback
        self._config_lock = threading.Lock()
        self._file_watcher_thread = None
        self._stop_watching = False
        self._last_modified = 0

        # Load initial configuration
        self._reload_config()

        # Start file watcher if auto_reload is enabled
        if self.auto_reload:
            self._start_file_watcher()

    def _reload_config(self):
        """Reload configuration from file."""
        with self._config_lock:
            try:
                self.config = self._load_config(self.config_file)
                self.talkers = self.config.get("talkers", {})

                # Check communication mode to determine NMEA version
                self.nmea_version = self._determine_nmea_version()
                self.default_source = self.config.get("default_source", "EI0001")

                # NMEA 0450 sequence tracking
                if not hasattr(self, 'sequence_numbers'):
                    self.sequence_numbers = {}  # Track sequence numbers per source

                print(f"🔄 Config reloaded - NMEA Version: {self.nmea_version}")
                if self.nmea_version == "0450":
                    print(f"🔄 Default Source: {self.default_source}")

                # Call reload callback if provided
                if self.reload_callback:
                    self.reload_callback(self.config)

            except Exception as e:
                print(f"❌ Failed to reload config: {e}")

    def _start_file_watcher(self):
        """Start file watcher thread for config changes."""
        if not os.path.exists(self.config_file):
            print(f"⚠️  Config file {self.config_file} not found, auto-reload disabled")
            return

        self._last_modified = os.path.getmtime(self.config_file)
        self._file_watcher_thread = threading.Thread(target=self._watch_config_file, daemon=True)
        self._file_watcher_thread.start()
        print(f"👁️  Started watching config file: {self.config_file}")

    def _watch_config_file(self):
        """Watch config file for changes and reload when modified."""
        while not self._stop_watching:
            try:
                if os.path.exists(self.config_file):
                    current_modified = os.path.getmtime(self.config_file)
                    if current_modified > self._last_modified:
                        print(f"📁 Config file changed, reloading...")
                        self._last_modified = current_modified
                        time.sleep(0.1)  # Small delay to ensure file write is complete
                        self._reload_config()

                time.sleep(1)  # Check every second
            except Exception as e:
                print(f"❌ Error watching config file: {e}")
                time.sleep(5)  # Wait longer on error

    def stop_auto_reload(self):
        """Stop automatic config file reloading."""
        self._stop_watching = True
        if self._file_watcher_thread and self._file_watcher_thread.is_alive():
            self._file_watcher_thread.join(timeout=2)
        print("🛑 Stopped config auto-reload")

    def reload_config_now(self):
        """Manually reload configuration immediately."""
        print("🔄 Manual config reload requested")
        self._reload_config()

    def _determine_nmea_version(self) -> str:
        """Determine NMEA version based on communication mode."""
        try:
            import json
            with open('config/communication.json', 'r') as f:
                comm_config = json.load(f)
                communication_mode = comm_config.get("communication_mode", "unicast")

                # multicast = NMEA 0450, unicast = NMEA 0183
                if communication_mode == "multicast":
                    return "0450"
                else:
                    return "0183"
        except Exception:
            # Default to NMEA 0183 if communication config not found
            return "0183"

    def _load_config(self, config_file: str) -> dict:
        """Load NMEA talkers configuration from JSON file."""
        try:
            import json
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ Config file {config_file} not found, using defaults")
            return {"talkers": {}, "nmea_version": "0183", "default_source": "EI0001"}
        except json.JSONDecodeError as e:
            print(f"⚠️ Error parsing {config_file}: {e}, using defaults")
            return {"talkers": {}, "nmea_version": "0183", "default_source": "EI0001"}

    def get_sources(self, sentence_type: str) -> list:
        """Get list of source configurations for a sentence type."""
        talker_configs = self.talkers.get(sentence_type, [])
        if not talker_configs:
            return [{"source": self.default_source, "talker_id": "GP", "enabled": True}]

        # Filter only enabled configurations
        return [config for config in talker_configs if config.get("enabled", True)]

    def get_source(self, sentence_type: str, source_id: str = None) -> str:
        """Get source identifier for a sentence type (backward compatibility)."""
        sources = self.get_sources(sentence_type)
        if source_id:
            # Find specific source
            for config in sources:
                if config.get("source") == source_id:
                    return config.get("source", self.default_source)

        # Return first enabled source
        if sources:
            return sources[0].get("source", self.default_source)
        return self.default_source

    def create_udpbc_header(self, sentence_type: str, source: str = None) -> str:
        """Create UdPbC header for NMEA 0450."""
        if source is None:
            source = self.get_source(sentence_type)

        # Get or initialize sequence number for this source
        if source not in self.sequence_numbers:
            self.sequence_numbers[source] = 1
        else:
            self.sequence_numbers[source] += 1

        if self.sequence_numbers[source] > 999:
            self.sequence_numbers[source] = 1

        seq_num = self.sequence_numbers[source]

        # Generate timestamp (milliseconds since epoch)
        import time
        timestamp = int(time.time() * 1000)

        # NMEA 0450 UdPbC format: UdPbC.\s:SOURCE,n:seq,c:timestamp*checksum\
        udpbc_header = f"UdPbC.\\s:{source},n:{seq_num},c:{timestamp}"
        udpbc_checksum = self.calculate_checksum(udpbc_header[6:])  # Remove "UdPbC.\" prefix

        return f"{udpbc_header}*{udpbc_checksum}\\"

    def format_sentence_header(self, sentence_type: str) -> str:
        """Format sentence header - deprecated, use create_udpbc_header for NMEA 0450."""
        return get_sentence_header(sentence_type)

    def format_complete_sentence(self, sentence_type: str, nmea_body: str, source: str = None) -> str:
        """Format complete sentence with UdPbC header for NMEA 0450 or plain for NMEA 0183."""
        nmea_sentence = f"{nmea_body}*{NavigationDataGenerator.calculate_checksum(nmea_body)}"

        if self.nmea_version == "0450":
            udpbc_header = self.create_udpbc_header(sentence_type, source)
            return f"{udpbc_header}{nmea_sentence}\r\n"
        else:
            return f"{nmea_sentence}\r\n"

    def format_multiple_sentences(self, sentence_type: str, ship_state: dict, env_param: dict, utc_nmea: str = None, utc_time: datetime = None) -> Dict[str, str]:
        """Generate multiple sentences for different sources."""
        sources = self.get_sources(sentence_type)
        sentences = {}

        for source_config in sources:
            if not source_config.get("enabled", True):
                continue

            source = source_config.get("source")
            talker_id = source_config.get("talker_id", "GP")

            # Generate sentence for any type
            sentence = self._make_sentence_for_source(sentence_type, ship_state, env_param, utc_nmea, source, talker_id, utc_time)
            if not sentence:  # Skip if sentence generation failed
                continue

            # Use unique key for multiple sources
            key = f"{sentence_type}_{source}" if len(sources) > 1 else sentence_type
            sentences[key] = sentence

        return sentences

    def _make_sentence_for_source(self, sentence_type: str, ship_state: dict, env_param: dict, utc_nmea: str = None, source: str = None, talker_id: str = "GP", utc_time: datetime = None) -> str:
        """Generate sentence for specific source and talker ID."""
        # Create custom header with specified talker ID
        custom_header = f"${talker_id}{sentence_type}"

        if sentence_type == "GGA":
            lat_gga, lon_gga = self.get_coordinates(ship_state)
            quality = ship_state.get("gps_quality", 3)
            num_sats = ship_state.get("num_satellites", 8)
            hdop = ship_state.get("hdop", 1.7)
            altitude = ship_state.get("altitude", 98.9)
            geoid_sep = ship_state.get("geoid_separation", "")
            dgps_age = ship_state.get("dgps_age", "")
            dgps_id = ship_state.get("dgps_station_id", "0000")

            nmea_body = f"{custom_header},{utc_nmea},{lat_gga},{lon_gga},{quality},{num_sats:02d},{hdop},{altitude},M,{geoid_sep},M,{dgps_age},{dgps_id}"

        elif sentence_type == "RMC":
            if utc_time:
                day = utc_time.day
                month = utc_time.month
                year = utc_time.year - 2000
                date_str = f"{day:02d}{month:02d}{year:02d}"
            else:
                date_str = ""

            lat_gga, lon_gga = self.get_coordinates(ship_state)
            sog = ship_state.get("SOG")
            sog_str = f"{sog:.1f}" if sog is not None else ""

            cog = ship_state.get("COG")
            if cog is not None:
                cog_deg = cog * 180 / math.pi
                cog_deg = self.normalize_angle(cog_deg)
                cog_str = f"{cog_deg:.1f}"
            else:
                cog_str = ""

            status = ship_state.get("gps_status", "A")
            mag_var = ship_state.get("magnetic_variation", "")
            mag_var_dir = ship_state.get("mag_var_direction", "")
            mode = ship_state.get("positioning_mode", "")

            nmea_body = f"{custom_header},{utc_nmea},{status},{lat_gga},{lon_gga},{sog_str},{cog_str},{date_str},{mag_var},{mag_var_dir},{mode}"

        elif sentence_type == "VTG":
            cog = ship_state.get("COG")
            if cog is not None:
                cog_deg = cog * 180 / math.pi
                cog_deg = self.normalize_angle(cog_deg)
                cog_str = f"{cog_deg:.1f}"
            else:
                cog_str = ""

            sog = ship_state.get("SOG")
            if sog is not None:
                sog_str = f"{sog:.1f}"
                kmh = sog * 0.5144 * 3.6
                kmh_str = f"{kmh:.1f}"
            else:
                sog_str = ""
                kmh_str = ""

            mag_course = ship_state.get("magnetic_course", cog_str)
            mode = ship_state.get("positioning_mode", "A")
            nmea_body = f"{custom_header},{cog_str},T,{mag_course},M,{sog_str},N,{kmh_str},K,{mode}"

        elif sentence_type == "HDT":
            psi = ship_state.get("psi")
            if psi is not None:
                hdg_deg = psi * 180 / math.pi
                hdg_deg = self.normalize_angle(hdg_deg)
                hdg_str = f"{hdg_deg:.1f}"
            else:
                hdg_str = ""
            nmea_body = f"{custom_header},{hdg_str},T"

        elif sentence_type == "ROT":
            r = ship_state.get("r")
            if r is not None:
                rot = r * 180 / math.pi * 60
                rot_str = f"{rot:.1f}"
            else:
                rot_str = ""
            status = ship_state.get("rot_status", "A")
            nmea_body = f"{custom_header},{rot_str},{status}"

        elif sentence_type == "VBW":
            stw = ship_state.get("STW")
            stw_str = f"{stw:.1f}" if stw is not None else ""

            long_water_speed = stw_str
            trans_water_speed = ship_state.get("transverse_water_speed", stw_str)
            water_status = ship_state.get("water_speed_status", "A")
            long_ground_speed = ship_state.get("longitudinal_ground_speed", stw_str)
            trans_ground_speed = ship_state.get("transverse_ground_speed", stw_str)
            ground_status = ship_state.get("ground_speed_status", "A")
            stern_water_speed = ship_state.get("stern_water_speed", "")
            stern_status = ship_state.get("stern_water_status", "A")
            stern_ground_speed = ship_state.get("stern_ground_speed", "")
            stern_ground_status = ship_state.get("stern_ground_status", "A")

            nmea_body = f"{custom_header},{long_water_speed},{trans_water_speed},{water_status},{long_ground_speed},{trans_ground_speed},{ground_status},{stern_water_speed},{stern_status},{stern_ground_speed},{stern_ground_status}"

        elif sentence_type == "ZDA":
            if utc_time:
                day = f"{utc_time.day:02d}"
                month = f"{utc_time.month:02d}"
                year = f"{utc_time.year:04d}"
            else:
                day = month = year = ""

            local_zone_hh = "00"  # UTC timezone offset hours
            local_zone_mm = "00"  # UTC timezone offset minutes
            nmea_body = f"{custom_header},{utc_nmea},{day},{month},{year},{local_zone_hh},{local_zone_mm}"

        elif sentence_type == "MWV":
            wind_angle = env_param.get("wind").get("direction", 0.0)
            wind_speed = env_param.get("wind").get("speed", 0.0)
            reference = "T"
            speed_units = ship_state.get("wind_speed_units", "N")
            status = ship_state.get("wind_status", "A")
            nmea_body = f"{custom_header},{wind_angle:.1f},{reference},{wind_speed:.1f},{speed_units},{status}"

        elif sentence_type == "DPT":
            depth = ship_state.get("water_depth", 200.3)
            offset = ship_state.get("depth_offset", 0.0)
            max_range = ship_state.get("depth_max_range", "")
            nmea_body = f"{custom_header},{depth:.1f},{offset:.1f},{max_range}"

        elif sentence_type == "THS":
            psi = ship_state.get("psi")
            if psi is not None:
                heading_deg = psi * 180 / math.pi
                heading_deg = self.normalize_angle(heading_deg)
                heading = f"{heading_deg:.1f}"
            else:
                heading = ""
            status = ship_state.get("heading_status", "A")
            nmea_body = f"{custom_header},{heading},{status}"

        elif sentence_type == "RSA":
            rudder_angle = ship_state.get("rudder_angle", None)
            rudder_angle_port = ship_state.get("rudder_angle_port", None)
            rudder_status = ship_state.get("rudder_status", "V")
            rudder_status_port = ship_state.get("rudder_status_port", "V")

            starboard_angle = f"{rudder_angle:.1f}" if rudder_angle is not None else ""
            port_angle = f"{rudder_angle_port:.1f}" if rudder_angle_port is not None else ""
            nmea_body = f"{custom_header},{starboard_angle},{rudder_status},{port_angle},{rudder_status_port}"
        
        elif sentence_type == "OSD":
            # OSD - Own Ship Data
            # Format: $--OSD,x.x,A,x.x,a,x.x,a,x.x,x.x,a*hh
            # Fields: Heading, Status, Vessel Course, Course Reference, Vessel Speed, Speed Reference, Vessel Set, Vessel Drift, Speed Units

            heading = ship_state.get("heading", 0.0)
            course = ship_state.get("course", heading)  # Course over ground
            speed_knots = ship_state.get("speed", 0.0) * 1.944  # m/s to knots

            # Calculate set and drift (Radar Set To do)
            vessel_set = 0.0  ## RADAR  To do 
            vessel_drift = 0.0 ## RADAR To do 

            # Status: A = Valid, V = Invalid
            status = "A"

            nmea_body = f"{custom_header},{heading:.2f},{status},{course:.2f},P,{speed_knots:.2f},P,{vessel_set:.1f},{vessel_drift:.2f},N"

        elif sentence_type == "RSD":
            # RARSD - Radar System Data
            # Format based on example: RARSD 0.000,0.0,,223.5,,,,,5.532,187.4,6.000,N,N*71
            # Most fields filled with 0 as requested

            # Extract some basic values, rest filled with 0
            heading = ship_state.get("heading", 0.0)
            speed_knots = ship_state.get("speed", 0.0) * 1.944

            # nmea_body = f"{custom_header},0.000,0.0,,{heading:.1f},,,,,0.000,0.0,{speed_knots:.3f},N,N"

            nmea_body = f"{custom_header},0.000,0.0,,223.5,,,,,5.532,187.4,6.000,N,N"


        elif sentence_type == "VDR":
            # VDR - Set and Drift
            # Format: $--VDR,x.x,T,x.x,M,x.x,N*hh
            # Fields: Direction True, T, Direction Magnetic, M, Current speed knots, N

            # Current/drift direction and speed
            current_direction_true = env_param.get("current").get("direction", 0.0)
            current_speed_knots = env_param.get("current").get("speed", 0.0)
            nmea_body = f"{custom_header},{current_direction_true:.1f},T,{current_direction_true:.1f},M,{current_speed_knots:.1f},N"

        elif sentence_type == "RSA":
            rpm = ship_state.get("rpm", 0.0)
            nmea_body = f"{custom_header},{rpm:.1f}"

        else:
            return ""

        return self.format_complete_sentence(sentence_type, nmea_body, source)

    def get_coordinates(self, ship_state: dict) -> tuple:
        """Get formatted coordinates based on NMEA version."""
        lat = ship_state.get("lat")
        lon = ship_state.get("lon")

        if lat is None or lon is None:
            return ("", "")

        if self.nmea_version == "0450":
            return format_coordinates_with_precision(lat, lon, precision=5)
        else:
            return format_nmea_coordinates(lat, lon)

    @staticmethod
    def normalize_angle(angle_deg: float) -> float:
        """Normalize angle to 0-359.999 range (360.0 becomes 0.0)."""
        if angle_deg is None:
            return None

        # Normalize to 0-360 range
        normalized = angle_deg % 360.0

        # Convert 360.0 to 0.0
        if abs(normalized - 360.0) < 0.001:  # Handle floating point precision
            normalized = 0.0

        return normalized

    @staticmethod
    def calculate_checksum(sentence: str) -> str:
        """
        Calculate NMEA checksum for a sentence.
        
        Args:
            sentence: NMEA sentence string starting with $
            
        Returns:
            Hexadecimal checksum string
        """
        checksum = 0
        for char in sentence[1:]:
            if char == "*":
                break
            checksum ^= ord(char)
        return f"{checksum:02X}"

    def make_gga_sentence(self, ship_state: dict, utc_nmea: str) -> str:
        """Generate GGA sentence (Global Positioning System Fix Data)."""
        lat_gga, lon_gga = self.get_coordinates(ship_state)

        # GGA fields: time, lat, lat_dir, lon, lon_dir, quality, num_sats, hdop, altitude, alt_unit, geoid_sep, geoid_unit, dgps_age, dgps_id
        quality = ship_state.get("gps_quality", 3)  # 3 = PPS fix
        num_sats = ship_state.get("num_satellites", 8)
        hdop = ship_state.get("hdop", 1.7)
        altitude = ship_state.get("altitude", 98.9)
        geoid_sep = ship_state.get("geoid_separation", "")
        dgps_age = ship_state.get("dgps_age", "")
        dgps_id = ship_state.get("dgps_station_id", "0000")

        # Create complete sentence with UdPbC header if needed
        nmea_header = get_sentence_header("GGA")
        nmea_body = f"{nmea_header},{utc_nmea},{lat_gga},{lon_gga},{quality},{num_sats:02d},{hdop},{altitude},M,{geoid_sep},M,{dgps_age},{dgps_id}"
        return self.format_complete_sentence("GGA", nmea_body)

    def make_rmc_sentence(self, ship_state: dict, utc_nmea: str, utc_time: datetime) -> str:
        """Generate RMC sentence (Recommended Minimum Navigation Information)."""
        if utc_time:
            day = utc_time.day
            month = utc_time.month
            year = utc_time.year - 2000
            date_str = f"{day:02d}{month:02d}{year:02d}"
        else:
            date_str = ""

        lat_gga, lon_gga = self.get_coordinates(ship_state)

        sog = ship_state.get("SOG")
        sog_str = f"{sog:.1f}" if sog is not None else ""

        cog = ship_state.get("COG")
        if cog is not None:
            cog_deg = cog * 180 / math.pi
            cog_deg = self.normalize_angle(cog_deg)
            cog_str = f"{cog_deg:.1f}"
        else:
            cog_str = ""

        # RMC fields: time, status, lat, lat_dir, lon, lon_dir, speed, course, date, mag_var, mag_var_dir, mode
        status = ship_state.get("gps_status", "A")  # A = Active, V = Void
        mag_var = ship_state.get("magnetic_variation", "")
        mag_var_dir = ship_state.get("mag_var_direction", "")
        mode = ship_state.get("positioning_mode", "")

        # Create complete sentence with UdPbC header if needed
        nmea_header = get_sentence_header("RMC")
        nmea_body = f"{nmea_header},{utc_nmea},{status},{lat_gga},{lon_gga},{sog_str},{cog_str},{date_str},{mag_var},{mag_var_dir},{mode}"
        return self.format_complete_sentence("RMC", nmea_body)

    def make_vtg_sentence(self, ship_state: dict) -> str:
        """Generate VTG sentence (Track Made Good and Ground Speed)."""
        cog = ship_state.get("COG")
        if cog is not None:
            cog_deg = cog * 180 / math.pi
            cog_deg = self.normalize_angle(cog_deg)
            cog_str = f"{cog_deg:.1f}"
        else:
            cog_str = ""

        sog = ship_state.get("SOG")
        if sog is not None:
            sog_str = f"{sog:.1f}"
            kmh = sog * 0.5144 * 3.6
            kmh_str = f"{kmh:.1f}"
        else:
            sog_str = ""
            kmh_str = ""

        # VTG fields: true_course, T, mag_course, M, speed_knots, N, speed_kmh, K, mode
        mag_course = ship_state.get("magnetic_course", cog_str)
        mode = ship_state.get("positioning_mode", "A")

        nmea_header = get_sentence_header("VTG")
        nmea_body = f"{nmea_header},{cog_str},T,{mag_course},M,{sog_str},N,{kmh_str},K,{mode}"
        return self.format_complete_sentence("VTG", nmea_body)

    def make_hdt_sentence(self, ship_state: dict) -> str:
        """Generate HDT sentence (Heading - True)."""
        psi = ship_state.get("psi")
        if psi is not None:
            hdg_deg = psi * 180 / math.pi
            hdg_deg = self.normalize_angle(hdg_deg)
            hdg_str = f"{hdg_deg:.1f}"
        else:
            hdg_str = ""

        # Create complete sentence with UdPbC header if needed
        nmea_header = get_sentence_header("HDT")
        nmea_body = f"{nmea_header},{hdg_str},T"
        return self.format_complete_sentence("HDT", nmea_body)

    def make_rot_sentence(self, ship_state: dict) -> str:
        """Generate ROT sentence (Rate of Turn)."""
        r = ship_state.get("r")
        if r is not None:
            rot = r * 180 / math.pi * 60
            rot_str = f"{rot:.1f}"
        else:
            rot_str = ""

        # ROT fields: rate_of_turn, status
        status = ship_state.get("rot_status", "A")  # A = Valid, V = Invalid
        nmea_header = get_sentence_header("ROT")
        nmea_body = f"{nmea_header},{rot_str},{status}"
        return self.format_complete_sentence("ROT", nmea_body)

    def make_vbw_sentence(self, ship_state: dict) -> str:
        """Generate VBW sentence (Dual Ground/Water Speed)."""
        stw = ship_state.get("STW")
        stw_str = f"{stw:.1f}" if stw is not None else ""

        # VBW fields: long_water_speed, trans_water_speed, water_status, long_ground_speed, trans_ground_speed, ground_status, stern_water_speed, stern_status, stern_ground_speed, stern_ground_status
        long_water_speed = stw_str
        trans_water_speed = ship_state.get("transverse_water_speed", stw_str)
        water_status = ship_state.get("water_speed_status", "A")
        long_ground_speed = ship_state.get("longitudinal_ground_speed", stw_str)
        trans_ground_speed = ship_state.get("transverse_ground_speed", stw_str)
        ground_status = ship_state.get("ground_speed_status", "A")
        stern_water_speed = ship_state.get("stern_water_speed", "")
        stern_status = ship_state.get("stern_water_status", "A")
        stern_ground_speed = ship_state.get("stern_ground_speed", "")
        stern_ground_status = ship_state.get("stern_ground_status", "A")

        nmea_header = get_sentence_header("VBW")
        nmea_body = f"{nmea_header},{long_water_speed},{trans_water_speed},{water_status},{long_ground_speed},{trans_ground_speed},{ground_status},{stern_water_speed},{stern_status},{stern_ground_speed},{stern_ground_status}"
        return self.format_complete_sentence("VBW", nmea_body)

    def make_zda_sentence(self, utc_nmea: str, utc_time: datetime) -> str:
        """Generate ZDA sentence (Time & Date)."""
        if utc_time:
            day = f"{utc_time.day:02d}"
            month = f"{utc_time.month:02d}"
            year = f"{utc_time.year:04d}"
        else:
            day = month = year = ""

        # ZDA fields: time, day, month, year, local_zone_hours, local_zone_minutes
        local_zone_hh = "00"  # UTC timezone offset hours
        local_zone_mm = "00"  # UTC timezone offset minutes

        nmea_header = get_sentence_header("ZDA")
        nmea_body = f"{nmea_header},{utc_nmea},{day},{month},{year},{local_zone_hh},{local_zone_mm}"
        return self.format_complete_sentence("ZDA", nmea_body)

    def make_mwv_sentence(self, ship_state: dict) -> str:
        """Generate MWV sentence (Wind Speed and Angle)."""
        # MWV fields: wind_angle, reference, wind_speed, speed_units, status
        wind_angle = ship_state.get("wind_angle", 0.0)
        reference = ship_state.get("wind_reference", "R")  # R = Relative, T = True
        wind_speed = ship_state.get("wind_speed", 0.0)
        speed_units = ship_state.get("wind_speed_units", "N")  # N = Knots, M = m/s, K = km/h
        status = ship_state.get("wind_status", "A")  # A = Valid, V = Invalid

        nmea_header = get_sentence_header("MWV")
        nmea_body = f"{nmea_header},{wind_angle:.1f},{reference},{wind_speed:.1f},{speed_units},{status}"
        return self.format_complete_sentence("MWV", nmea_body)

    def make_dpt_sentence(self, ship_state: dict) -> str:
        """Generate DPT sentence (Depth of Water)."""
        # DPT fields: depth, offset, max_range
        depth = ship_state.get("water_depth", 200.3)
        offset = ship_state.get("depth_offset", 0.0)
        max_range = ship_state.get("depth_max_range", "")

        nmea_header = get_sentence_header("DPT")
        nmea_body = f"{nmea_header},{depth:.1f},{offset:.1f},{max_range}"
        return self.format_complete_sentence("DPT", nmea_body)

    def make_ths_sentence(self, ship_state: dict) -> str:
        """Generate THS sentence (True Heading and Status)."""
        # THS fields: heading, status
        psi = ship_state.get("psi")
        if psi is not None:
            heading_deg = psi * 180 / math.pi
            heading_deg = self.normalize_angle(heading_deg)
            heading = f"{heading_deg:.1f}"
        else:
            heading = ""

        status = ship_state.get("heading_status", "A")  # A = Autonomous, M = Manual, V = Invalid

        nmea_header = get_sentence_header("THS")
        nmea_body = f"{nmea_header},{heading},{status}"
        return self.format_complete_sentence("THS", nmea_body)

    def make_rsa_sentence(self, ship_state: dict) -> str:
        """Generate RSA sentence (Rudder Sensor Angle)."""
        # RSA fields: starboard_rudder_angle, starboard_status, port_rudder_angle, port_status

        # Get rudder angle from autopilot interface (stored in ship_state)
        rudder_angle = ship_state.get("rudder_angle", None)  # From autopilot_interface
        rudder_angle_port = ship_state.get("rudder_angle_port", None)  # From autopilot_interface
        rudder_status = ship_state.get("rudder_status", "V")  # A = Valid, V = Invalid
        rudder_status_port = ship_state.get("rudder_status_port", "V")  # A = Valid, V = Invalid


        # For single rudder ships, use same angle for both starboard and port
        starboard_angle = f"{rudder_angle:.1f}" if rudder_angle is not None else ""
        port_angle = f"{rudder_angle_port:.1f}" if rudder_angle_port is not None else ""

        # Status: A = Valid, V = Invalid
        starboard_status = rudder_status
        port_status = rudder_status_port

        nmea_header = get_sentence_header("RSA")
        nmea_body = f"{nmea_header},{starboard_angle},{starboard_status},{port_angle},{port_status}"
        return self.format_complete_sentence("RSA", nmea_body)

    def generate_navigation_sentences(self, ship_state: dict, env_param: dict, utc_nmea: str, utc_time: datetime) -> Dict[str, str]:
        """
        Generate all navigation NMEA sentences.
        Now supports multiple sources per sentence type.

        Args:
            ship_state: Current ship state dictionary
            utc_nmea: UTC time in NMEA format
            utc_time: UTC datetime object

        Returns:
            Dictionary of NMEA sentence types and their content
        """
        navigation_sentences = {}

        # Generate all sentences based on JSON configuration
        for sentence_type in self.talkers.keys():
            sentences = self.format_multiple_sentences(sentence_type, ship_state, env_param, utc_nmea, utc_time)
            navigation_sentences.update(sentences)

        return navigation_sentences


# Global instance for backward compatibility
_navigation_generator = None

def get_navigation_generator():
    """Get or create global NavigationDataGenerator instance."""
    global _navigation_generator
    if _navigation_generator is None:
        _navigation_generator = NavigationDataGenerator()
    return _navigation_generator

# Backward compatibility function
def make_nmea_sentence(ship_state: dict, env_param: dict, utc_nmea: str, utc_time: datetime) -> tuple:
    """
    Backward compatibility function for make_nmea_sentence.
    Only generates navigation NMEA sentences.

    Args:
        ship_state: Ship state dictionary
        utc_nmea: UTC time in NMEA format
        utc_time: UTC datetime object

    Returns:
        Tuple of (navigation_sentences, empty_dict) for backward compatibility
    """
    generator = get_navigation_generator()
    navigation_sentences = generator.generate_navigation_sentences(ship_state, env_param, utc_nmea, utc_time)

    # Return empty dict for autopilot sentences (no longer needed)
    autopilot_sentences = {}

    return navigation_sentences, autopilot_sentences
