"""
RSA interface for ship simulator.
Handles parsing and processing of RSA (Rudder Sensor Angle) messages only.
"""

from typing import Optional
import math


class RSAInterface:
    """
    Simple interface for RSA (Rudder Sensor Angle) messages.

    Since the simulator now uses direct RSA commands instead of
    complex autopilot messages, this interface is greatly simplified.
    """

    def __init__(self):
        """Initialize RSA interface."""
        self.message_count = 0
        self.last_rudder_angle = 0.0

    def parse_rsa_message(self, message: str) -> Optional[float]:
        """
        Parse RSA NMEA message to extract rudder angle.

        RSA format: $AGRSA,rudder_angle,A,rudder_angle,V*checksum

        Args:
            message: Raw RSA NMEA message

        Returns:
            Rudder angle in degrees, or None if parsing fails
        """
        if not message:
            return None

        try:
            # Remove any trailing whitespace and split by comma
            parts = message.strip().split(',')

            # Check if it's an RSA message
            if len(parts) >= 2 and parts[0] in ['$AGRSA', '$IIRSA', '$HCRSA']:
                # Extract rudder angle from second field
                rudder_str = parts[1]
                if rudder_str and rudder_str != '':
                    rudder_angle = float(rudder_str)
                    self.last_rudder_angle = rudder_angle
                    self.message_count += 1
                    return rudder_angle

            return None

        except (ValueError, IndexError) as e:
            print(f"Error parsing RSA message '{message}': {e}")
            return None

    def get_last_rudder_angle(self) -> float:
        """
        Get the last received rudder angle.

        Returns:
            Last rudder angle in degrees
        """
        return self.last_rudder_angle

    def get_message_count(self) -> int:
        """
        Get the total number of processed RSA messages.

        Returns:
            Total message count
        """
        return self.message_count

    def reset_message_count(self) -> None:
        """Reset the message counter."""
        self.message_count = 0

    def get_interface_status(self) -> dict:
        """
        Get current RSA interface status.

        Returns:
            Dictionary containing interface status information
        """
        return {
            "message_count": self.message_count,
            "last_rudder_angle": self.last_rudder_angle,
        }


# Backward compatibility classes
class AutopilotInterface(RSAInterface):
    """Backward compatibility alias for RSAInterface."""

    def __init__(self, degree_to_radian: float = math.pi / 180.0):
        """Backward compatibility constructor."""
        super().__init__()
        self.degree_to_radian = degree_to_radian

    def process_command_message(self, message: str, ship_state: dict) -> tuple:
        """
        Backward compatibility method for processing messages.

        Args:
            message: Raw message string
            ship_state: Ship state dictionary (unused in RSA mode)

        Returns:
            Tuple of (processed, error_message)
        """
        rudder_angle = self.parse_rsa_message(message)
        if rudder_angle is not None:
            return True, None
        return False, "Not a valid RSA message"


class MessageParser(AutopilotInterface):
    """Backward compatibility alias for AutopilotInterface."""

    def parse_message(self, message: str, ship_state: dict) -> tuple:
        """Backward compatibility method."""
        return self.process_command_message(message, ship_state)

    def is_message_set_complete(self, expected_messages: int) -> bool:
        """Backward compatibility method."""
        return self.message_count % expected_messages == 0

    def get_parser_status(self) -> dict:
        """Backward compatibility method."""
        return self.get_interface_status()
