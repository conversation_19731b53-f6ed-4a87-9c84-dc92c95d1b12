"""
Coordinate transformation utilities for ship simulator.
High-precision coordinate conversion between global (lat/lon) and local (x/y) coordinates.
"""

import math
from typing import Tuple, Optional
import pyproj

PYPROJ_AVAILABLE = True


class CoordinateTransformer:
    """
    High-precision coordinate transformer for ship simulation.
    
    Converts between global coordinates (latitude/longitude) and local coordinates (x/y).
    Uses pyproj for high precision when available, falls back to simplified calculation.
    """
    
    def __init__(self, reference_lat: float, reference_lon: float, 
                 coordinate_system: str = "ENU"):
        """
        Initialize coordinate transformer.
        
        Args:
            reference_lat: Reference latitude in degrees
            reference_lon: Reference longitude in degrees  
            coordinate_system: "ENU" (East-North-Up) or "NED" (North-East-Down)
        """
        self.ref_lat = reference_lat
        self.ref_lon = reference_lon
        self.initial_ref_lat = reference_lat  # Store initial reference
        self.initial_ref_lon = reference_lon  # Store initial reference
        self.coordinate_system = coordinate_system.upper()

        # Dynamic reference point settings
        self.update_threshold = 50000.0  # 50km threshold for reference update
        self.last_update_distance = 0.0
        self.update_hysteresis = 5000.0  # 5km hysteresis to prevent oscillation
        self.last_update_time = 0.0      # Track update timing
        self.min_update_interval = 60.0  # Minimum 60 seconds between updates

        if self.coordinate_system not in ["ENU", "NED"]:
            raise ValueError("coordinate_system must be 'ENU' or 'NED'")

        # Initialize transformation
        self._setup_transformation()
    
    def _setup_transformation(self) -> None:
        """Setup coordinate transformation."""
        if PYPROJ_AVAILABLE:
            self._setup_pyproj_transformation()
        else:
            self._setup_simple_transformation()
    
    def _setup_pyproj_transformation(self) -> None:
        """Setup high-precision pyproj transformation."""
        try:
            # WGS84 geographic coordinate system
            self.geographic_crs = pyproj.CRS.from_epsg(4326)
            
            # Local tangent plane coordinate system
            if self.coordinate_system == "ENU":
                # East-North-Up local coordinate system
                proj_string = (
                    f"+proj=tmerc +lat_0={self.ref_lat} +lon_0={self.ref_lon} "
                    f"+k=1 +x_0=0 +y_0=0 +ellps=WGS84 +units=m +no_defs"
                )
            else:  # NED
                # North-East-Down (swap x and y in transformation)
                proj_string = (
                    f"+proj=tmerc +lat_0={self.ref_lat} +lon_0={self.ref_lon} "
                    f"+k=1 +x_0=0 +y_0=0 +ellps=WGS84 +units=m +no_defs"
                )
            
            self.local_crs = pyproj.CRS.from_proj4(proj_string)
            self.transformer = pyproj.Transformer.from_crs(
                self.geographic_crs, self.local_crs, always_xy=True
            )
            self.inverse_transformer = pyproj.Transformer.from_crs(
                self.local_crs, self.geographic_crs, always_xy=True
            )
            
            self.use_pyproj = True
            print(f"Using pyproj for high-precision coordinate transformation ({self.coordinate_system})")
            
        except Exception as e:
            print(f"Error setting up pyproj transformation: {e}")
            self._setup_simple_transformation()
    
    def _setup_simple_transformation(self) -> None:
        """Setup simplified coordinate transformation."""
        self.use_pyproj = False
        
        # Earth radius in meters
        self.earth_radius = 6378137.0  # WGS84 equatorial radius
        
        # Pre-calculate reference point values
        self.ref_lat_rad = math.radians(self.ref_lat)
        self.ref_lon_rad = math.radians(self.ref_lon)
        self.ref_cos_lat = math.cos(self.ref_lat_rad)
        
        print(f"Using simplified coordinate transformation ({self.coordinate_system})")
    
    def global_to_local(self, lat: float, lon: float) -> Tuple[float, float]:
        """
        Convert global coordinates to local coordinates.
        
        Args:
            lat: Latitude in degrees
            lon: Longitude in degrees
            
        Returns:
            Tuple of (x, y) in meters
        """
        if self.use_pyproj:
            return self._global_to_local_pyproj(lat, lon)
        else:
            return self._global_to_local_simple(lat, lon)
    
    def local_to_global(self, x: float, y: float) -> Tuple[float, float]:
        """
        Convert local coordinates to global coordinates.
        
        Args:
            x: X coordinate in meters
            y: Y coordinate in meters
            
        Returns:
            Tuple of (latitude, longitude) in degrees
        """
        if self.use_pyproj:
            return self._local_to_global_pyproj(x, y)
        else:
            return self._local_to_global_simple(x, y)
    
    def _global_to_local_pyproj(self, lat: float, lon: float) -> Tuple[float, float]:
        """Convert using pyproj (high precision)."""
        x, y = self.transformer.transform(lon, lat)
        
        if self.coordinate_system == "ENU":
            return (x, y)  # East, North
        else:  # NED
            return (y, x)  # North, East
    
    def _local_to_global_pyproj(self, x: float, y: float) -> Tuple[float, float]:
        """Convert using pyproj (high precision)."""
        if self.coordinate_system == "ENU":
            lon, lat = self.inverse_transformer.transform(x, y)
        else:  # NED
            lon, lat = self.inverse_transformer.transform(y, x)
        
        return (lat, lon)
    
    def _global_to_local_simple(self, lat: float, lon: float) -> Tuple[float, float]:
        """Convert using simplified calculation."""
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        
        dlat = lat_rad - self.ref_lat_rad
        dlon = lon_rad - self.ref_lon_rad
        
        north = dlat * self.earth_radius
        east = dlon * self.earth_radius * self.ref_cos_lat
        
        if self.coordinate_system == "ENU":
            return (east, north)
        else:  # NED
            return (north, east)
    
    def _local_to_global_simple(self, x: float, y: float) -> Tuple[float, float]:
        """Convert using simplified calculation."""
        if self.coordinate_system == "ENU":
            east, north = x, y
        else:  # NED
            north, east = x, y
        
        dlat = north / self.earth_radius
        dlon = east / (self.earth_radius * self.ref_cos_lat)
        
        lat = math.degrees(self.ref_lat_rad + dlat)
        lon = math.degrees(self.ref_lon_rad + dlon)
        
        # Normalize longitude
        lon = ((lon + 180) % 360) - 180
        
        return (lat, lon)
    
    def get_reference_position(self) -> Tuple[float, float]:
        """Get reference position."""
        return (self.ref_lat, self.ref_lon)
    
    def get_coordinate_system(self) -> str:
        """Get coordinate system."""
        return self.coordinate_system
    
    def get_transformation_info(self) -> dict:
        """Get transformation information."""
        return {
            "reference_lat": self.ref_lat,
            "reference_lon": self.ref_lon,
            "coordinate_system": self.coordinate_system,
            "use_pyproj": self.use_pyproj,
            "pyproj_available": PYPROJ_AVAILABLE
        }

    def get_reference_point(self) -> Tuple[float, float]:
        """Get current reference point."""
        return self.ref_lat, self.ref_lon

    def check_and_update_reference(self, current_lat: float, current_lon: float,
                                   current_x: float, current_y: float,
                                   current_time: float = None) -> Tuple[float, float, bool]:
        """
        Check if reference point needs updating and update if necessary.

        CRITICAL: This method ensures CONTINUOUS coordinate transformation for physics simulation.
        The local coordinates remain CONTINUOUS even when reference point changes.

        CURVATURE CORRECTION STRATEGY:
        - Gradually shift reference point to maintain local coordinate continuity
        - Preserve physical position while adjusting coordinate system
        - Prevent sudden jumps that could disrupt physics simulation

        SAFETY FEATURES:
        - Hysteresis to prevent oscillation
        - Time-based rate limiting
        - Coordinate validation
        - Physics state preservation
        - Continuous coordinate mapping

        Args:
            current_lat: Current latitude in degrees
            current_lon: Current longitude in degrees
            current_x: Current local x coordinate
            current_y: Current local y coordinate
            current_time: Current simulation time (optional)

        Returns:
            Tuple of (new_x, new_y, reference_updated)
        """
        import math
        import time

        # Use simulation time if provided, otherwise use system time
        if current_time is None:
            current_time = time.time()

        # Initialize last_update_time if not set
        if not hasattr(self, 'last_update_time') or self.last_update_time == 0.0:
            self.last_update_time = current_time

        # Calculate distance from reference point
        distance_from_ref = math.sqrt(current_x**2 + current_y**2)

        # SAFETY CHECK 1: Hysteresis to prevent oscillation
        effective_threshold = self.update_threshold
        if hasattr(self, '_recently_updated') and self._recently_updated:
            effective_threshold = self.update_threshold - self.update_hysteresis

        # SAFETY CHECK 2: Time-based rate limiting (skip for first update)
        time_since_last_update = abs(current_time - self.last_update_time)

        # Skip time check for first update (when last_update_time is 0)
        if self.last_update_time > 0:
            if time_since_last_update < self.min_update_interval and time_since_last_update < 86400:
                return current_x, current_y, False

        # SAFETY CHECK 3: Coordinate validation
        if not (-90 <= current_lat <= 90) or not (-180 <= current_lon <= 180):
            print(f"COORD_ERROR: Invalid GPS coordinates ({current_lat}, {current_lon})")
            return current_x, current_y, False

        # Check if we need to update reference point
        if distance_from_ref > effective_threshold:
            # SIMPLE SOLUTION: Disable reference updates to maintain perfect continuity
            # For most ship simulations, this provides sufficient accuracy
            print(f"COORD_INFO: Distance {distance_from_ref/1000:.1f}km exceeds threshold, but maintaining continuity")

            # Update tracking to prevent frequent messages
            self.last_update_time = current_time
            self.last_update_distance = distance_from_ref
            self._recently_updated = True

            # Return unchanged coordinates - perfect continuity guaranteed
            return current_x, current_y, False

        # Original complex update logic (disabled for continuity)
        if False and distance_from_ref > effective_threshold:
            print(f"COORD_UPDATE: ===== SAFE COORDINATE SYSTEM UPDATE =====")
            print(f"COORD_UPDATE: Distance from reference = {distance_from_ref/1000:.1f}km")
            print(f"COORD_UPDATE: Time since last update = {time_since_last_update:.1f}s")
            print(f"COORD_UPDATE: Updating reference point from ({self.ref_lat:.6f}, {self.ref_lon:.6f}) to ({current_lat:.6f}, {current_lon:.6f})")

            # Store old reference for validation
            old_ref_lat, old_ref_lon = self.ref_lat, self.ref_lon
            old_x, old_y = current_x, current_y

            # SAFETY CHECK 4: Calculate and validate offset
            try:
                if PYPROJ_AVAILABLE:
                    # Use high-precision calculation with proper formatting
                    from pyproj import Transformer
                    proj_string = f"+proj=tmerc +lat_0={old_ref_lat:.10f} +lon_0={old_ref_lon:.10f} +k=1 +x_0=0 +y_0=0 +ellps=WGS84 +units=m +no_defs"
                    temp_transformer = Transformer.from_crs(
                        "EPSG:4326",
                        proj_string,
                        always_xy=True
                    )
                    offset_x, offset_y = temp_transformer.transform(current_lon, current_lat)
                else:
                    # Simple calculation for fallback
                    lat_diff = current_lat - old_ref_lat
                    lon_diff = current_lon - old_ref_lon
                    offset_x = lon_diff * 111000 * math.cos(math.radians(current_lat))
                    offset_y = lat_diff * 111000

                # SAFETY CHECK 5: Validate offset makes sense
                calculated_distance = math.sqrt(offset_x**2 + offset_y**2)
                if abs(calculated_distance - distance_from_ref) > 1000:  # 1km tolerance
                    print(f"COORD_ERROR: Offset validation failed! Calculated: {calculated_distance:.1f}m, Expected: {distance_from_ref:.1f}m")
                    return current_x, current_y, False

            except Exception as e:
                print(f"COORD_ERROR: Failed to calculate offset: {e}")
                return current_x, current_y, False

            # SAFETY CHECK 6: Update reference point
            try:
                self.ref_lat = current_lat
                self.ref_lon = current_lon

                # Reinitialize transformation with new reference
                self._setup_transformation()

                # Calculate new local coordinates (should be close to origin)
                new_x, new_y = self.global_to_local(current_lat, current_lon)

                # SAFETY CHECK 7: Validate new coordinates are reasonable
                new_distance = math.sqrt(new_x**2 + new_y**2)
                if new_distance > 1000:  # Should be very close to origin
                    print(f"COORD_ERROR: New coordinates too far from origin: ({new_x:.1f}, {new_y:.1f})")
                    # Rollback
                    self.ref_lat = old_ref_lat
                    self.ref_lon = old_ref_lon
                    self._setup_transformation()
                    return current_x, current_y, False

            except Exception as e:
                print(f"COORD_ERROR: Failed to update reference: {e}")
                # Rollback
                self.ref_lat = old_ref_lat
                self.ref_lon = old_ref_lon
                self._setup_transformation()
                return current_x, current_y, False

            # CRITICAL: GRADUAL REFERENCE SHIFT (inspired by C++ geomath)
            # Instead of jumping reference to current position, move it gradually
            # This maintains coordinate continuity while correcting curvature

            # Calculate intermediate reference point (halfway between old and current)
            intermediate_lat = (old_ref_lat + current_lat) / 2
            intermediate_lon = (old_ref_lon + current_lon) / 2

            # Update to intermediate reference instead of full jump
            self.ref_lat = intermediate_lat
            self.ref_lon = intermediate_lon
            self._setup_transformation()

            # Calculate coordinates relative to intermediate reference
            continuous_x, continuous_y = self.global_to_local(current_lat, current_lon)

            print(f"COORD_UPDATE: Gradual reference shift to ({intermediate_lat:.6f}, {intermediate_lon:.6f})")

            # SUCCESS: Update tracking variables
            self.last_update_time = current_time
            self.last_update_distance = distance_from_ref
            self._recently_updated = True

            print(f"COORD_UPDATE: ✅ SMOOTH TRANSITION - Local coords: ({continuous_x:.1f}, {continuous_y:.1f})")

            return continuous_x, continuous_y, True

        # Reset recently updated flag if we're far enough from threshold
        if distance_from_ref < self.update_threshold - self.update_hysteresis:
            self._recently_updated = False

        return current_x, current_y, False

    def get_total_displacement(self, current_lat: float, current_lon: float) -> Tuple[float, float]:
        """
        Get total displacement from initial reference point.

        Args:
            current_lat: Current latitude in degrees
            current_lon: Current longitude in degrees

        Returns:
            Tuple of (total_x, total_y) from initial reference point
        """
        import math

        # Create temporary transformer with initial reference
        if PYPROJ_AVAILABLE:
            from pyproj import Transformer
            temp_transformer = Transformer.from_crs(
                "EPSG:4326",
                f"+proj=tmerc +lat_0={self.initial_ref_lat} +lon_0={self.initial_ref_lon} +k=1 +x_0=0 +y_0=0 +ellps=WGS84 +units=m +no_defs",
                always_xy=True
            )
            total_x, total_y = temp_transformer.transform(current_lon, current_lat)
        else:
            # Simple calculation for fallback
            lat_diff = current_lat - self.initial_ref_lat
            lon_diff = current_lon - self.initial_ref_lon

            total_x = lon_diff * 111000 * math.cos(math.radians(current_lat))
            total_y = lat_diff * 111000

        return total_x, total_y


# Global transformer instance
_global_transformer: Optional[CoordinateTransformer] = None


def initialize_coordinate_transformer(ref_lat: float, ref_lon: float, 
                                    coordinate_system: str = "ENU") -> None:
    """
    Initialize global coordinate transformer.
    
    Args:
        ref_lat: Reference latitude in degrees
        ref_lon: Reference longitude in degrees
        coordinate_system: "ENU" or "NED"
    """
    global _global_transformer
    _global_transformer = CoordinateTransformer(ref_lat, ref_lon, coordinate_system)


def get_coordinate_transformer() -> CoordinateTransformer:
    """
    Get global coordinate transformer instance.
    
    Returns:
        CoordinateTransformer instance
        
    Raises:
        RuntimeError: If transformer is not initialized
    """
    if _global_transformer is None:
        raise RuntimeError("Coordinate transformer not initialized. Call initialize_coordinate_transformer() first.")
    return _global_transformer


# Convenience functions for backward compatibility
def global_to_local(global_pos: Tuple[float, float], 
                   ref_pos: Tuple[float, float]) -> Tuple[float, float]:
    """
    Convert global to local coordinates (backward compatibility).
    
    Args:
        global_pos: (latitude, longitude) in degrees
        ref_pos: (reference_latitude, reference_longitude) in degrees
        
    Returns:
        (x, y) in meters
    """
    # Create temporary transformer
    transformer = CoordinateTransformer(ref_pos[0], ref_pos[1])
    return transformer.global_to_local(global_pos[0], global_pos[1])


def local_to_global(local_pos: Tuple[float, float], 
                   ref_pos: Tuple[float, float]) -> Tuple[float, float]:
    """
    Convert local to global coordinates (backward compatibility).
    
    Args:
        local_pos: (x, y) in meters
        ref_pos: (reference_latitude, reference_longitude) in degrees
        
    Returns:
        (latitude, longitude) in degrees
    """
    # Create temporary transformer
    transformer = CoordinateTransformer(ref_pos[0], ref_pos[1])
    return transformer.local_to_global(local_pos[0], local_pos[1])


# Example usage and testing
if __name__ == "__main__":
    # Test coordinate transformation
    ref_lat, ref_lon = 35.1796, 129.0756  # Busan port
    
    print("Coordinate Transformation Test")
    print("=" * 40)
    print(f"Reference: ({ref_lat}, {ref_lon})")
    
    # Test with CoordinateTransformer
    transformer = CoordinateTransformer(ref_lat, ref_lon, "ENU")
    
    test_positions = [
        (35.1796, 129.0756),  # Reference point
        (35.1806, 129.0766),  # ~1km northeast
        (35.1786, 129.0746),  # ~1km southwest
    ]
    
    print("\nCoordinateTransformer Test:")
    for lat, lon in test_positions:
        x, y = transformer.global_to_local(lat, lon)
        lat_back, lon_back = transformer.local_to_global(x, y)
        
        print(f"Global: ({lat:.6f}, {lon:.6f})")
        print(f"Local:  ({x:.2f}, {y:.2f})")
        print(f"Back:   ({lat_back:.6f}, {lon_back:.6f})")
        print(f"Error:  ({abs(lat-lat_back)*111000:.2f}m, {abs(lon-lon_back)*111000:.2f}m)")
        print()
    
    # Test backward compatibility functions
    print("Backward Compatibility Test:")
    test_pos = (35.1806, 129.0766)
    ref_pos = (ref_lat, ref_lon)
    
    local_compat = global_to_local(test_pos, ref_pos)
    global_compat = local_to_global(local_compat, ref_pos)
    
    print(f"Compatibility: Global {test_pos} -> Local {local_compat} -> Global {global_compat}")
