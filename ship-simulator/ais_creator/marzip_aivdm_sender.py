import time
import async<PERSON>
import json
import os
from typing import Dict, Optional
from pyais.encode import encode_dict
from core.time_manager import init_global_timer, get_global_timer
from utils.udp_socket_base import UDPSocketBase


class MarzipAIS_Sender:
    """
    AIS message sender that uses CommunicationManager-style configuration.
    Sends AIVDM messages via unicast to specified target.
    """

    def __init__(self, config_file: str = "config/communication.json"):
        """
        Initialize AIS sender with configuration file.

        Args:
            config_file: Path to communication configuration JSON file
        """
        self.config = self._load_config(config_file)

        # Use unicast configuration for AIVDM messages
        unicast_config = self.config["unicast"]
        self.target_ip = unicast_config["target_ip"]
        self.target_port = unicast_config["port"]

        print(f"🚢 AIS Sender Configuration:")
        print(f"  Target: {self.target_ip}:{self.target_port}")

        # Socket for sending AIVDM messages
        self.aivdm_sender: Optional[UDPSocketBase] = None

    def _load_config(self, config_file: str) -> dict:
        """Load communication configuration from JSON file."""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ Config file {config_file} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            print(f"⚠️ Error parsing {config_file}: {e}, using defaults")
            return self._get_default_config()

    def _get_default_config(self) -> dict:
        """Get default configuration."""
        return {
            "unicast": {
                "target_ip": "***********",
                "port": 6503
            }
        }

    def setup_communication(self) -> None:
        """Setup UDP socket for AIVDM message transmission."""
        self.aivdm_sender = UDPSocketBase(
            remote_ip=self.target_ip,
            remote_port=self.target_port,
            is_receiver=False,
            is_sender=True,
            is_auto_set=True
        )
        print(f"✅ AIVDM sender initialized to {self.target_ip}:{self.target_port}")

    async def start_sending(self, send_queue):
        """
        Send AIVDM messages from queue using CommunicationManager-style UDP socket.

        Args:
            send_queue: List of AIVDM message objects with relative_timestamp
        """
        if not self.aivdm_sender:
            raise RuntimeError("AIVDM sender not initialized. Call setup_communication() first.")

        if not send_queue:
            print("⚠️ No messages in send queue")
            return

        # Normalize timestamps to start from 0
        if send_queue:
            base_timestamp = min(msg.relative_timestamp for msg in send_queue)
            for msg in send_queue:
                msg.relative_timestamp = (msg.relative_timestamp - base_timestamp)

        print(f"🚀 Starting AIVDM transmission - {len(send_queue)} messages queued")
        start_time = int(time.time() * 1000000)  # microseconds
        prev_msg_timestamp = None
        messages_sent = 0

        while send_queue:
            try:
                current_relative_time = int(time.time() * 1000000) - start_time

                # Send all messages that are due
                while send_queue and send_queue[0].relative_timestamp <= current_relative_time:
                    msg = send_queue.pop(0)

                    # Update message timestamp using global timer
                    try:
                        msg.second = get_global_timer().now_datetime().second
                    except RuntimeError:
                        # If global timer not initialized, use current time
                        msg.second = int(time.time()) % 60

                    # Encode AIVDM message
                    target_message_dict = msg.__dict__
                    encoded_data = encode_dict(
                        target_message_dict, radio_channel="A", talker_id="AIVDM"
                    )[0]

                    # Send via UDP socket (CommunicationManager style)
                    message_with_newline = encoded_data + "\r\n"
                    self.aivdm_sender.send_data(message_with_newline.encode('utf-8'))
                    messages_sent += 1

                    # Calculate and display transmission interval
                    if prev_msg_timestamp is not None and send_queue:
                        next_msg_timestamp = send_queue[0].relative_timestamp
                        interval_us = next_msg_timestamp - prev_msg_timestamp
                        time_interval = interval_us / 1000000
                        print(f"📡 AIVDM [{messages_sent:03d}]: {encoded_data} <Interval: {time_interval:.2f}s>")
                    else:
                        print(f"📡 AIVDM [{messages_sent:03d}]: {encoded_data} <Interval: -s>")

                    prev_msg_timestamp = msg.relative_timestamp
                    current_relative_time = int(time.time() * 1000000) - start_time

                # If no more messages, finish
                if not send_queue:
                    print(f"✅ All {messages_sent} AIVDM messages sent successfully")
                    break

            except Exception as ex:
                print(f"❌ Error during AIVDM transmission: {ex}")
                break

            # Sleep until next message is due
            if send_queue:
                sleep_duration_us = send_queue[0].relative_timestamp - (int(time.time() * 1000000) - start_time)
                if sleep_duration_us > 0:
                    sleep_duration_s = sleep_duration_us / 1_000_000
                    await asyncio.sleep(sleep_duration_s)

        print(f"🏁 AIVDM transmission completed. Total messages sent: {messages_sent}")

    def send_single_aivdm(self, aivdm_message: str) -> None:
        """
        Send a single AIVDM message immediately.

        Args:
            aivdm_message: AIVDM message string to send
        """
        if not self.aivdm_sender:
            raise RuntimeError("AIVDM sender not initialized")

        message_with_newline = aivdm_message + "\r\n"
        self.aivdm_sender.send_data(message_with_newline.encode('utf-8'))
        print(f"📡 Single AIVDM sent: {aivdm_message}")

    def close_communication(self) -> None:
        """Close AIVDM sender socket."""
        if self.aivdm_sender:
            self.aivdm_sender.close_socket()
            self.aivdm_sender = None
            print("🔌 AIVDM sender socket closed")

    def is_ready(self) -> bool:
        """Check if sender is ready for operation."""
        return self.aivdm_sender is not None

    def get_status(self) -> Dict[str, any]:
        """Get sender status information."""
        return {
            "ready": self.is_ready(),
            "target": f"{self.target_ip}:{self.target_port}"
        }


# Convenience functions for backward compatibility (removed event management)
def start_ais_sender():
    """Start the AIS sender (deprecated - no longer needed)"""
    pass

def stop_ais_sender():
    """Stop the AIS sender (deprecated - no longer needed)"""
    pass

def reset_ais_sender():
    """Reset the AIS sender events (deprecated - no longer needed)"""
    pass




