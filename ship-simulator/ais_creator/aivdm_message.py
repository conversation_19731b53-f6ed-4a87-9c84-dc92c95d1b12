import time
from datetime import datetime, timedelta

class AIVDM_Message:
    def __init__(self):
        self.msg_type = 1
        self.repeat = 1
        self.is_detected = True
        self.id = 0
        self.mmsi = 0
        self.status = 0
        self.turn = 0
        self.speed = 0
        self.accuracy = 0
        self.lat = 0.0
        self.lon = 0.0
        self.course = 0.0
        self.heading = 0.0
        self.second = time.gmtime().tm_sec
        self.maneuver = 0
        self.spare_1 = b""
        self.raim = 0
        self.radio = 0
        self.event = []
        self.event_index = 0
        self.name = ""
        self.ship_type = ""
        self.length = 0.0
        self.width = 0.0
        self.timestamp = datetime(2024, 1, 1, 0, 0, 0)

    def to_dict(self):
        return self.__dict__