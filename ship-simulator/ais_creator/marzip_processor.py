"""
Marzip file processor that combines file extraction and data parsing functionality.
Handles .marzip files containing timeseries and static ship data, and converts them to AIS messages.
"""

import os
import json
from datetime import datetime
import random
import time
from typing import Dict, List, Optional

from utils.utils import MathUtils

try:
    from ais_creator.aivdm_message import AIVDM_Message
    AIVDM_AVAILABLE = True
except ImportError:
    AIVDM_AVAILABLE = False

try:
    from ais_creator.rattd_message import RATTD_Message
    RATTD_AVAILABLE = True
except ImportError:
    RATTD_AVAILABLE = False


class MarzipProcessor:
    """
    Combined processor for .marzip files that extracts data and converts to AIS messages.
    
    Handles:
    1. Extraction of .marzip archive files
    2. Reading Arrow format timeseries and static data
    3. Parsing and converting data to AIVDM messages
    4. Time-based sorting and relative timestamp calculation
    """
    
    def __init__(self, file_path=None, marzip_data=None):
        """
        Initialize processor with either file path or pre-loaded data.

        Args:
            file_path: Path to .marzip file
            marzip_data: Pre-loaded marzip data dict with 'timeseries_dataset' and 'static_dataset'
        """
        self.file_path = file_path
        self.simulation_result = {}
        self.generated_mmsi = {}

        if marzip_data:
            self.timeseries_dataset = marzip_data["timeseries_dataset"]
            self.static_dataset = marzip_data["static_dataset"]
        else:
            self.timeseries_dataset = []
            self.static_dataset = []

    def extract_and_read_marzip(self):
        """
        Extract .marzip file and read timeseries/static data.
        Uses utils.marzip_reader for extraction.

        Returns:
            dict: Contains 'timeseries_dataset' and 'static_dataset'
        """
        if not self.file_path:
            raise ValueError("No file path provided")

        try:
            from utils.marzip_reader import extract_marzip_for_ais
            marzip_data = extract_marzip_for_ais(self.file_path)

            if marzip_data:
                self.timeseries_dataset = marzip_data["timeseries_dataset"]
                self.static_dataset = marzip_data["static_dataset"]
                return marzip_data
            else:
                raise RuntimeError("Failed to extract marzip data")

        except ImportError:
            raise ImportError("utils.marzip_reader not available. Install pyarrow package.")

    def process_marzip_file(self):
        """
        Complete processing: extract file and convert to AIS messages.
        
        Returns:
            dict: Grouped AIS messages by sensor type and ID
        """
        # Extract data if not already loaded
        if not self.timeseries_dataset and not self.static_dataset:
            self.extract_and_read_marzip()
        
        # Convert to AIS messages
        return self.sort_data_with_relative_time()

    def sort_data_with_relative_time(self) -> dict:
        """
        전체 타겟 데이터를 시간순으로 정렬한 후,
        각 타겟의 Timeseries 데이터에 대해 전체 최소 타임스탬프를 기준으로 상대시간(relative_timestamp)을 계산하여 저장하고,
        이후 AIVDM_Message 객체로 매핑하고,
        id별로 그룹화하여 딕셔너리로 리턴합니다.

        예시)
            id 1의 타임스탬프: 100, 200, 300, 400  →  상대시간: 0, 100, 200, 300
            id 2의 타임스탬프: 200, 300, 400, 500  →  상대시간: 100, 200, 300, 400

        :return: { id: [AIVDM_Message, ...], ... } 형태의 딕셔너리
        """
        # 1. Static/Timeseries 데이터를 매칭하여 정렬된 리스트를 얻음
        organized_target = self._reorganize_target_data()
        sorted_data = self._sort_timeseries(organized_target)

        # 2. 모든 타겟의 절대 타임스탬프(float형)를 모아서 최소값을 기준(base_timestamp)으로 사용
        all_timestamps = []
        for target in sorted_data:
            ts = target["timeseries"].get("timestamp") or target["timeseries"].get("timeStamp")
            # timestamp가 datetime이면 float로 변환
            if isinstance(ts, datetime):
                ts_value = ts.timestamp()
            else:
                ts_value = ts
            if ts_value is not None:
                all_timestamps.append(ts_value)
        base_timestamp = min(all_timestamps) if all_timestamps else 0

        # 3. 각 타겟 데이터에 대해 상대 타임스탬프를 계산하여 저장
        for target in sorted_data:
            ts = target["timeseries"].get("timestamp") or target["timeseries"].get("timeStamp")
            if isinstance(ts, datetime):
                ts_value = ts.timestamp()
            else:
                ts_value = ts
            if ts_value is not None:
                relative_ts = ts_value - base_timestamp
            else:
                relative_ts = 0
            target["timeseries"]["relative_timestamp"] = relative_ts

        # 4. sensor 값에 따라 데이터를 분기하여 그룹화
        grouped_data = {"ais": [], "radar": []}
        for target in sorted_data:
            grouped_data["ais"].append(target)

        # 5. 각 그룹별 데이터를 각각 매핑하여 메시지 객체를 생성 (msg가 2종류로 생성됨)
        messages = {"ais": [], "radar": []}
        # "ais" 그룹 처리
        for target in grouped_data["ais"]:
            msg = self._mapping_targetShip_data_VDM_Message(target)
            # 매핑 함수가 절대 타임스탬프를 사용한다면, 상대 타임스탬프로 덮어쓰기
            rel_ts = target["timeseries"].get("relative_timestamp")
            if rel_ts is not None:
                msg.relative_timestamp = rel_ts
            messages["ais"].append(msg)

        # 6. sensor 별 메시지 객체를 id 별로 그룹화
        grouped_messages = {}
        for sensor, msg_list in messages.items():
            # sensor 별로 딕셔너리를 초기화합니다.
            grouped_messages[sensor] = {}
            for msg in msg_list:
                grouped_messages[sensor].setdefault(msg.id, []).append(msg)

        return grouped_messages

    def _reorganize_target_data(self) -> list:
        """
        Timeseries Dataset에서 ownShip이 False이거나 id가 0이 아닌 항목을 필터링하여,
        기존 포맷에 맞춰 {"timeseries": ts} 구조로 반환합니다.
        """
        matched_data = [
            {"timeseries": ts} for ts in self.timeseries_dataset
            if not ts.get("ownShip", True) or ts.get("id", -1) != 0
        ]
        return matched_data

    def _sort_timeseries(self, matched_data) -> list:
        """
        매칭된 데이터를 timeseries의 timeStamp를 기준으로 시간순으로 정렬합니다.
        :param matched_data: 매칭된 데이터 리스트
        :return: 정렬된 데이터 리스트
        """
        sorted_data = sorted(
            matched_data,
            key=lambda target: target["timeseries"].get("timestamp") or target["timeseries"].get("timeStamp")
        )
        return sorted_data

    def _mapping_targetShip_data_VDM_Message(self, targetShipInfo):
        """
        targetShipInfo 데이터를 AIVDM_Message 객체로 매핑합니다.
        :param targetShipInfo: { "static": static_data, "timeseries": timeseries_data }
        :return: AIVDM_Message 객체
        """
        random.seed(time.time())
        msg = AIVDM_Message()

        # Static 데이터 매핑
        static = targetShipInfo.get("static", {})
        timeseries = targetShipInfo.get("timeseries", {})
        msg_id = static.get("id")
        if msg_id is None:
            msg_id = timeseries.get("id", 0)  # -1은 미정 또는 오류 ID로 활용 가능

        msg.id = msg_id

        # MMSI 매핑: MMSI 값이 없으면, 해당 id에 대해 한 번만 랜덤 MMSI를 생성하여 할당
        mmsi = static.get("mmsi")
        if msg.id not in self.generated_mmsi:
            mmsi = str(random.randint(100000000, 999999999))
            self.generated_mmsi[msg.id] = mmsi
        else:
            mmsi = self.generated_mmsi[msg.id]

        msg.mmsi = mmsi
        msg.name = static.get("name", "Unknown")
        msg.ship_type = static.get("shipType", "Unknown")
        msg.length = static.get("length", 0.0)
        msg.width = static.get("width", 0.0)

        # Timeseries 데이터 매핑
        timeseries = targetShipInfo.get("timeseries", {})
        msg.lat = timeseries.get("lat", 0.0)
        msg.lon = timeseries.get("lon", 0.0)
        msg.speed = timeseries.get("sog", 0.0)
        msg.course = MathUtils.convert_as_from_0_to_360(timeseries.get("cog", 0.0))
        msg.heading = MathUtils.convert_as_from_0_to_360(timeseries.get("heading", 0.0))
        msg.timestamp = timeseries.get("timestamp", None) or timeseries.get("timeStamp", None)

        return msg


# Convenience functions for backward compatibility
class TargetsFromMarzip:
    """Backward compatibility class - use utils.marzip_reader.MarzipConfigExtractor instead."""

    def __init__(self, file_path):
        self.file_path = file_path

    def extract_and_read_marzip(self):
        """Extract marzip data using utils.marzip_reader."""
        try:
            from utils.marzip_reader import extract_marzip_for_ais
            return extract_marzip_for_ais(self.file_path)
        except ImportError:
            raise ImportError("utils.marzip_reader not available. Install pyarrow package.")


class MarzipParser(MarzipProcessor):
    """Backward compatibility class - use MarzipProcessor instead."""

    def __init__(self, marzip):
        super().__init__(marzip_data=marzip)


if __name__ == "__main__":
    # Example usage
    file_path = "scenarios/marzip/output.marzip"  # Replace with your file path
    try:
        processor = MarzipProcessor(file_path)
        target_information = processor.extract_and_read_marzip()
        ais_messages = processor.sort_data_with_relative_time()
        print(f"Processed {len(target_information['timeseries_dataset'])} timeseries records")
        print(f"Generated AIS messages for {len(ais_messages.get('ais', {}))} targets")
    except Exception as e:
        print(f"Failed to process the file: {e}")
