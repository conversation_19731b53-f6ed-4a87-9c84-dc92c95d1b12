# Communication Configuration

## 설정 파일: `communication.json`

### 멀티캐스트 모드 사용
```json
{
    "communication_mode": "multicast",
    ...
}
```

### 유니캐스트 모드 사용
```json
{
    "communication_mode": "unicast", 
    ...
}
```

## 설정 항목

- **communication_mode**: `"multicast"` 또는 `"unicast"`
- **multicast.group_ip**: 멀티캐스트 그룹 IP (예: `"***********"`)
- **multicast.port**: 멀티캐스트 포트 (예: `60001`)
- **multicast.ttl**: 멀티캐스트 TTL (예: `1`)
- **multicast.interface_ip**: 송신 인터페이스 IP (예: `"***********"`)
- **unicast.target_ip**: 유니캐스트 대상 IP (예: `"***********"`)
- **unicast.port**: 유니캐스트 포트 (예: `60001`)
- **simulator_server.ip**: 시뮬레이터 서버 IP (예: `"***********"`)
- **simulator_server.port**: 시뮬레이터 서버 포트 (예: `59902`)

## 사용법

1. `config/communication.json`에서 `communication_mode`를 원하는 모드로 변경
2. 해당 모드의 설정값들을 환경에 맞게 수정
3. 시뮬레이터 실행: `python run_simulator.py`
