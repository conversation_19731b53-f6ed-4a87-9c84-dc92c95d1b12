# Scenario Manager

간단한 해양 시뮬레이션 시나리오 관리 도구

## 📁 파일 구조

```
scenario_tools/
├── scenario_manager.py    # 메인 도구 (이것만 있으면 됨!)
└── README.md             # 이 파일
```

## 🚀 사용법

### 1. 명령줄 사용

```bash
# 현재 상태 확인 (marzip 파일 자동 찾기)
python scenario_tools/scenario_manager.py

# 특정 marzip 파일 지정해서 상태 확인
python scenario_tools/scenario_manager.py --marzip ais_creator/scenario_AA.marzip

# 설정 업데이트 (marzip에서 추출)
python scenario_tools/scenario_manager.py update
python scenario_tools/scenario_manager.py --marzip path/to/scenario.marzip update

# 시나리오 모드로 변경
python scenario_tools/scenario_manager.py scenario
python scenario_tools/scenario_manager.py -m path/to/scenario.marzip scenario

# 일반 모드로 변경
python scenario_tools/scenario_manager.py normal

# 다른 디렉토리에서 실행 (절대 경로 사용)
python /app/scenario_tools/scenario_manager.py \
  --marzip /app/ais_creator/scenario_AA.marzip \
  --config /app/config/simulation_config.json \
  update
```

#### CLI 옵션

- `--marzip`, `-m`: marzip 파일 경로 지정
- `--config`, `-c`: simulation_config.json 파일 경로 지정 (기본값: config/simulation_config.json)
- `--help`, `-h`: 도움말 표시

### 2. Python 코드에서 사용

```python
import sys
sys.path.append('scenario_tools')
from scenario_manager import ScenarioManager

# 기본 사용 (marzip 파일 자동 찾기)
manager = ScenarioManager()
manager.update_config()
manager.set_mode('scenario')

# 특정 marzip 파일 지정
manager = ScenarioManager("ais_creator/scenario_AA.marzip")
manager.update_config()
manager.set_mode('scenario')

# 상태 확인
manager.print_status()
current_mode = manager.get_current_mode()
```

## 📦 입력 파일

도구는 다음 순서로 marzip 파일을 찾습니다:

1. **CLI 옵션으로 지정된 파일** (`--marzip` 옵션)
2. **config 파일의 marzip_name** (config 디렉토리 기준 상대 경로)
3. **자동 검색** (fallback):
   - 현재 디렉토리의 `*.marzip`
   - `ais_creator/` 디렉토리의 `*.marzip`

**marzip 내부 파일**:
- `configuration.yml` - 환경 설정 (바람, 해류, 파도)
- `scenario_AA.json` - 시나리오 데이터 (선박 위치, 타겟 선박)

**자동 저장**: 설정 업데이트 시 사용된 marzip 파일 경로가 `simulation_config.json`의 `marzip_name` 필드에 자동 저장됩니다.

## 🎯 출력

`config/simulation_config.json` 파일을 업데이트합니다:

- **환경 설정**: 바람, 해류, 파도 설정
- **초기 조건**: 선박 위치, 속도, 코스
- **타겟 선박**: 시나리오 표시용 데이터
- **시뮬레이션 모드**: normal/scenario 모드

## 💡 특징

✅ **간단함**: 파일 하나로 모든 기능  
✅ **자동 감지**: marzip 파일 자동 찾기  
✅ **안전함**: 자동 백업 생성  
✅ **유연함**: CLI와 Python 코드 모두 지원  

## 🔧 주요 메서드

- `update_config()` - marzip에서 설정 업데이트
- `set_mode(mode)` - 시뮬레이션 모드 변경
- `get_current_mode()` - 현재 모드 확인
- `print_status()` - 상태 출력

## 📋 예제

자세한 사용 예제는 `run_scenario_example.py` 파일을 참고하세요.

```bash
python run_scenario_example.py
```
