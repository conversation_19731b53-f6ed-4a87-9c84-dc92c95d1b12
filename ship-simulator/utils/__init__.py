"""
Utilities package for ship simulator.

This package contains various utility modules:
- utils: Mathematical utilities and helper functions
- global_local_convertor: Professional coordinate conversion utilities
- udp_socket_base: UDP socket communication base class
- lat_lon_string: Latitude/longitude string formatting utilities
"""

from .utils import MathUtils
# Coordinate conversion moved to core/coordinate_transformer.py
from .udp_socket_base import UDPSocketBase
from .coordinate_formatter import format_nmea_coordinates, lat_lon_string, parse_dms_coordinate, parse_position_string
from .nmea_config import get_nmea_config, get_sentence_header, is_sentence_enabled
# Simulation configuration moved to core/simulation_config.py

__version__ = "1.0.0"
__author__ = "Ship Simulator Team"

__all__ = [
    'MathUtils',
    'UDPSocketBase',
    'format_nmea_coordinates',
    'lat_lon_string',
    'parse_dms_coordinate',
    'parse_position_string',
    'get_nmea_config',
    'get_sentence_header',
    'is_sentence_enabled'
]

__all__ = [
    "MathUtils",
    "CoordinateConverter", 
    "global_to_local",
    "local_to_global",
    "UDPSocketBase",
]
