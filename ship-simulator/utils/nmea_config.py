"""
NMEA configuration manager for talker IDs and sentence formatting.
Handles loading and managing NMEA talker configurations from JSON files.
"""

import json
import os
from typing import Dict, Optional, List
from pathlib import Path


class NMEAConfig:
    """
    Manages NMEA talker ID configuration and sentence formatting.
    
    Loads configuration from JSON file and provides methods to:
    - Get talker IDs for specific sentence types
    - Format complete NMEA sentence headers
    - Validate and manage talker configurations
    """
    
    def __init__(self, config_file: str = None):
        """
        Initialize NMEA configuration.
        
        Args:
            config_file: Path to JSON configuration file
        """
        self.config_file = config_file or self._get_default_config_path()
        self.config = {}
        self.talkers = {}
        self.talker_definitions = {}
        self.load_config()
    
    def _get_default_config_path(self) -> str:
        """Get default configuration file path."""
        # Look for config file relative to project root
        current_dir = Path(__file__).parent.parent
        config_path = current_dir / "config" / "nmea_talkers.json"
        return str(config_path)
    
    def load_config(self) -> None:
        """Load configuration from JSON file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)

                self.talkers = self.config.get('talkers', {})
                print(f"Loaded NMEA config from: {self.config_file}")
            else:
                print(f"Config file not found: {self.config_file}")
                self._create_default_config()
                
        except Exception as e:
            print(f"Error loading NMEA config: {e}")
            self._create_default_config()
    
    def _create_default_config(self) -> None:
        """Create default configuration if file doesn't exist."""
        self.talkers = {
            "GGA": {"talker_id": "GP", "enabled": True},
            "RMC": {"talker_id": "GP", "enabled": True},
            "VTG": {"talker_id": "GP", "enabled": True},
            "HDT": {"talker_id": "HE", "enabled": True},
            "ROT": {"talker_id": "HE", "enabled": True},
            "VBW": {"talker_id": "VD", "enabled": True},
            "ZDA": {"talker_id": "GP", "enabled": True},
            "MWV": {"talker_id": "WI", "enabled": True},
            "DPT": {"talker_id": "SD", "enabled": True},
        }

        print("Using default NMEA configuration")
    
    def get_talker_id(self, sentence_type: str) -> str:
        """
        Get talker ID for a specific sentence type.

        Args:
            sentence_type: NMEA sentence type (e.g., "GGA", "RMC")

        Returns:
            Talker ID (e.g., "GP", "HE")
        """
        if sentence_type in self.talkers:
            talker_config = self.talkers[sentence_type]

            # Handle new array format
            if isinstance(talker_config, list):
                if talker_config and len(talker_config) > 0:
                    return talker_config[0].get('talker_id', 'GP')
            # Handle old dict format
            elif isinstance(talker_config, dict):
                return talker_config.get('talker_id', 'GP')

        return 'GP'  # Default to GPS
    
    def get_full_sentence_id(self, sentence_type: str) -> str:
        """
        Get complete NMEA sentence identifier.
        
        Args:
            sentence_type: NMEA sentence type (e.g., "GGA")
            
        Returns:
            Complete sentence ID (e.g., "GPGGA")
        """
        talker_id = self.get_talker_id(sentence_type)
        return f"{talker_id}{sentence_type}"
    
    def is_sentence_enabled(self, sentence_type: str) -> bool:
        """
        Check if a sentence type is enabled.

        Args:
            sentence_type: NMEA sentence type

        Returns:
            True if sentence is enabled
        """
        if sentence_type in self.talkers:
            talker_config = self.talkers[sentence_type]

            # Handle new array format
            if isinstance(talker_config, list):
                # Return True if any configuration is enabled
                return any(config.get('enabled', True) for config in talker_config)
            # Handle old dict format
            elif isinstance(talker_config, dict):
                return talker_config.get('enabled', True)

        return True  # Default to enabled
    
    def set_talker_id(self, sentence_type: str, talker_id: str) -> None:
        """
        Set talker ID for a sentence type.
        
        Args:
            sentence_type: NMEA sentence type
            talker_id: New talker ID
        """
        if sentence_type not in self.talkers:
            self.talkers[sentence_type] = {}

        self.talkers[sentence_type]['talker_id'] = talker_id
    
    def enable_sentence(self, sentence_type: str, enabled: bool = True) -> None:
        """
        Enable or disable a sentence type.
        
        Args:
            sentence_type: NMEA sentence type
            enabled: True to enable, False to disable
        """
        if sentence_type not in self.talkers:
            self.talkers[sentence_type] = {}
        
        self.talkers[sentence_type]['enabled'] = enabled
    
    def get_enabled_sentences(self) -> List[str]:
        """
        Get list of enabled sentence types.
        
        Returns:
            List of enabled sentence types
        """
        enabled = []
        for sentence_type, config in self.talkers.items():
            if config.get('enabled', True):
                enabled.append(sentence_type)
        return enabled
    

    
    def save_config(self) -> None:
        """Save current configuration to JSON file."""
        try:
            # Update the config dictionary
            self.config['talkers'] = self.talkers
            
            # Ensure config directory exists
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            # Save to file
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            print(f"NMEA config saved to: {self.config_file}")
            
        except Exception as e:
            print(f"Error saving NMEA config: {e}")
    
    def get_config_info(self) -> Dict:
        """
        Get basic configuration information.

        Returns:
            Dictionary with configuration details
        """
        return {
            "config_file": self.config_file,
            "total_sentences": len(self.talkers),
            "enabled_sentences": len(self.get_enabled_sentences()),
            "sentences": {
                sentence: {
                    "talker_id": config.get('talker_id', 'GP'),
                    "full_sentence": self.get_full_sentence_id(sentence),
                    "enabled": config.get('enabled', True)
                }
                for sentence, config in self.talkers.items()
            }
        }


# Global configuration instance
_nmea_config = None


def get_nmea_config() -> NMEAConfig:
    """
    Get global NMEA configuration instance.
    
    Returns:
        NMEAConfig instance
    """
    global _nmea_config
    if _nmea_config is None:
        _nmea_config = NMEAConfig()
    return _nmea_config


def reload_nmea_config() -> None:
    """Reload NMEA configuration from file."""
    global _nmea_config
    _nmea_config = None
    _nmea_config = NMEAConfig()


# Convenience functions
def get_sentence_header(sentence_type: str) -> str:
    """
    Get complete NMEA sentence header with $ prefix.
    
    Args:
        sentence_type: NMEA sentence type (e.g., "GGA")
        
    Returns:
        Complete header (e.g., "$GPGGA")
    """
    config = get_nmea_config()
    full_id = config.get_full_sentence_id(sentence_type)
    return f"${full_id}"


def is_sentence_enabled(sentence_type: str) -> bool:
    """
    Check if sentence type is enabled.
    
    Args:
        sentence_type: NMEA sentence type
        
    Returns:
        True if enabled
    """
    config = get_nmea_config()
    return config.is_sentence_enabled(sentence_type)


# Example usage and testing
if __name__ == "__main__":
    # Test NMEA configuration
    config = NMEAConfig()

    print("NMEA Configuration Test")
    print("=" * 40)

    # Test sentence headers
    sentences = ["GGA", "RMC", "VTG", "HDT", "ROT"]
    for sentence in sentences:
        header = get_sentence_header(sentence)
        enabled = is_sentence_enabled(sentence)
        talker = config.get_talker_id(sentence)

        print(f"{sentence:3s} -> {header:7s} (Enabled: {enabled})")

    print("\nConfiguration Info:")
    info = config.get_config_info()
    print(f"Total sentences: {info['total_sentences']}")
    print(f"Enabled sentences: {info['enabled_sentences']}")

    # Test configuration changes
    print("\nTesting configuration changes...")
    config.set_talker_id("GGA", "II")  # Change to Integrated Instrumentation
    print(f"Changed GGA talker: {get_sentence_header('GGA')}")

    config.enable_sentence("MWV", False)  # Disable wind data
    print(f"MWV enabled: {is_sentence_enabled('MWV')}")