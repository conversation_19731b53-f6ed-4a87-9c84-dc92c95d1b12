import math


class MathUtils:
    """
    A utility class containing mathematical functions for angle conversion,
    radian normalization, and value clamping.
    """

    @staticmethod
    def convert_as_from_0_to_360(angle):
        """
        Convert any angle to the range 0 to 360 degrees.

        Parameters:
            angle (float): The input angle in degrees (can be any value).

        Returns:
            float: The angle converted to the range 0 to 360 degrees.
        """
        return (angle % 360 + 360) % 360

    @staticmethod
    def logic_bound_to_pi(radian):
        """
        Normalize radian value to the range [0, 2π].

        Args:
            radian (float): The input radian value.

        Returns:
            float: The radian value normalized to [0, 2π] range.
        """
        if radian < 0:
            return radian + 2 * math.pi
        if radian > 2 * math.pi:
            return radian - 2 * math.pi
        return radian

    @staticmethod
    def logic_bound_abs_pi(radian):
        """
        Normalize radian value to the range [-π, π].

        Args:
            radian (float): The input radian value.

        Returns:
            float: The radian value normalized to [-π, π] range.
        """
        if radian < -math.pi:
            return radian + 2 * math.pi
        if radian > math.pi:
            return radian - 2 * math.pi
        return radian

    @staticmethod
    def logic_min_max_value(min_val, val, max_val):
        """
        Clamp a value between minimum and maximum bounds.

        Args:
            min_val (float): The minimum allowed value.
            val (float): The value to be clamped.
            max_val (float): The maximum allowed value.

        Returns:
            float: The clamped value within [min_val, max_val] range.
        """
        return min(max(val, min_val), max_val)


def main() -> None:
    """Example usage of MathUtils class methods."""
    # 예제 사용
    radian = -1.0
    radian_new = MathUtils.logic_bound_to_pi(radian)
    print(f"New radian: {radian_new}")

    # 예제 사용
    radian = 3.5
    radian_new = MathUtils.logic_bound_abs_pi(radian)
    print(f"New radian: {radian_new}")

    min_value = 5
    max_value = 10
    ret = MathUtils.logic_min_max_value(min_value, 4, max_value)
    print(f"Logic Bound: {ret}")

    ret = MathUtils.logic_min_max_value(min_value, 12, max_value)
    print(f"Logic Bound: {ret}")

    # 각도 변환 예제 추가
    angle = -45
    normalized_angle = MathUtils.convert_as_from_0_to_360(angle)
    print(f"Angle {angle}° normalized to 0-360°: {normalized_angle}°")


if __name__ == "__main__":
    main()