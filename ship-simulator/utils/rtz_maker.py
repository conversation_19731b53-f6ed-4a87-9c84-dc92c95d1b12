#!/usr/bin/env python3
"""
RTZ Maker - Convert marzip files to RTZ route files

Recursively scans directories for .marzip files and converts them to RTZ format.
RTZ files are created with the same name as the marzip file.

Usage:
    python utils/rtz_maker.py [directory]

    If no directory is specified, scans current directory recursively.
"""

import os
import sys
import json
import zipfile
import tempfile
import shutil
import xml.etree.ElementTree as ET
from xml.dom import minidom
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional


class RTZMaker:
    """Convert marzip files to RTZ route files."""

    def __init__(self):
        """Initialize RTZ maker."""
        self.processed_count = 0
        self.error_count = 0
        self.temp_dirs = []

    def find_marzip_files(self, directory: str) -> List[str]:
        """
        Find all .marzip files recursively in directory.

        Args:
            directory: Directory to search

        Returns:
            List of marzip file paths
        """
        marzip_files = []

        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.lower().endswith('.marzip'):
                    marzip_files.append(os.path.join(root, file))

        return marzip_files

    def extract_marzip(self, marzip_path: str) -> Optional[str]:
        """
        Extract marzip file and find scenario_AA.json.

        Args:
            marzip_path: Path to marzip file

        Returns:
            Path to extracted scenario_AA.json or None if not found
        """
        try:
            # Create temporary directory
            temp_dir = tempfile.mkdtemp(prefix="rtz_marzip_")
            self.temp_dirs.append(temp_dir)

            # Extract marzip file
            with zipfile.ZipFile(marzip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)

            # Find any .json file (scenario data)
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    if file.lower().endswith('.json'):
                        return os.path.join(root, file)

            return None

        except Exception as e:
            print(f"❌ Error extracting {marzip_path}: {e}")
            return None

    def load_scenario_data(self, json_path: str) -> Optional[Dict[str, Any]]:
        """
        Load scenario data from JSON file.

        Args:
            json_path: Path to scenario_AA.json

        Returns:
            Scenario data dictionary or None if error
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading JSON {json_path}: {e}")
            return None

    def create_rtz_xml(self, scenario_data: Dict[str, Any]) -> str:
        """
        Create RTZ XML from scenario data.

        Args:
            scenario_data: Scenario data dictionary

        Returns:
            RTZ XML string
        """
        # Extract waypoints
        traffic_situation = scenario_data.get('trafficSituation', {})
        own_ship = traffic_situation.get('ownShip', {})
        waypoints = own_ship.get('waypoints', [])

        if not waypoints:
            raise ValueError("No waypoints found in scenario data")

        # Create RTZ XML root
        rtz = ET.Element('route', version='1.0', xmlns='http://www.cirm.org/RTZ/1/0')

        # Route info header
        header = ET.SubElement(rtz, 'routeInfo')
        route_name = traffic_situation.get('title', 'Converted Route')
        ET.SubElement(header, 'routeName').text = route_name
        ET.SubElement(header, 'routeType').text = '1'
        ET.SubElement(header, 'startTime').text = datetime.utcnow().isoformat() + "Z"
        ET.SubElement(header, 'version').text = '1'

        # Waypoints table
        wp_table = ET.SubElement(rtz, 'waypoints')

        for i, wp in enumerate(waypoints):
            wpt = ET.SubElement(wp_table, 'waypoint', id=str(i+1))

            # Position
            pos = wp.get("position", {})
            lat = pos.get("latitude", 0.0)
            lon = pos.get("longitude", 0.0)
            ET.SubElement(wpt, 'position', lat=str(lat), lon=str(lon))

            # Route parameters
            ET.SubElement(wpt, 'turnRadius').text = str(wp.get("turnRadius", 0))
            ET.SubElement(wpt, 'wheelOver').text = 'radius'
            ET.SubElement(wpt, 'speed').text = str(wp.get("sog", 0))
            ET.SubElement(wpt, 'rpm').text = str(wp.get("rpm", 0))
            ET.SubElement(wpt, 'xtdPort').text = str(wp.get("xtl_port", 0))
            ET.SubElement(wpt, 'xtdStarboard').text = str(wp.get("xtl_stbd", 0))

        # Format XML
        xml_str = ET.tostring(rtz, encoding='utf-8')
        pretty_xml = minidom.parseString(xml_str).toprettyxml(indent="  ")

        return pretty_xml

    def convert_marzip_to_rtz(self, marzip_path: str) -> bool:
        """
        Convert single marzip file to RTZ.

        Args:
            marzip_path: Path to marzip file

        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"🔄 Processing: {marzip_path}")

            # Extract marzip
            json_path = self.extract_marzip(marzip_path)
            if not json_path:
                print(f"❌ No .json file found in {marzip_path}")
                return False

            # Load scenario data
            scenario_data = self.load_scenario_data(json_path)
            if not scenario_data:
                return False

            # Create RTZ XML
            rtz_xml = self.create_rtz_xml(scenario_data)

            # Generate output path (same name as marzip, but .rtz extension)
            marzip_name = os.path.splitext(os.path.basename(marzip_path))[0]
            marzip_dir = os.path.dirname(marzip_path)
            rtz_path = os.path.join(marzip_dir, f"{marzip_name}.rtz")

            # Save RTZ file
            with open(rtz_path, 'w', encoding='utf-8') as f:
                f.write(rtz_xml)

            print(f"✅ Created: {rtz_path}")
            return True

        except Exception as e:
            print(f"❌ Error converting {marzip_path}: {e}")
            return False

    def cleanup(self):
        """Clean up temporary directories."""
        for temp_dir in self.temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except Exception:
                    pass
        self.temp_dirs.clear()

    def process_directory(self, directory: str) -> None:
        """
        Process all marzip files in directory recursively.

        Args:
            directory: Directory to process
        """
        try:
            print("🎯 RTZ MAKER - Marzip to RTZ Converter")
            print("=" * 50)
            print(f"📂 Scanning directory: {os.path.abspath(directory)}")

            # Find all marzip files
            marzip_files = self.find_marzip_files(directory)

            if not marzip_files:
                print("⚠️  No .marzip files found")
                return

            print(f"📦 Found {len(marzip_files)} marzip files")
            print()

            # Process each marzip file
            for marzip_file in marzip_files:
                if self.convert_marzip_to_rtz(marzip_file):
                    self.processed_count += 1
                else:
                    self.error_count += 1
                print()

            # Summary
            print("=" * 50)
            print("📊 CONVERSION SUMMARY")
            print(f"✅ Successfully converted: {self.processed_count}")
            print(f"❌ Failed conversions: {self.error_count}")
            print(f"📁 Total files processed: {len(marzip_files)}")

            if self.processed_count > 0:
                print()
                print("🎉 RTZ files created successfully!")
                print("💡 RTZ files are saved in the same directory as the marzip files")

        finally:
            self.cleanup()


def main():
    """Main function."""
    # Check for help
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print("RTZ Maker - Convert marzip files to RTZ route files")
        print()
        print("Usage:")
        print("  python utils/rtz_maker.py [directory]")
        print()
        print("Arguments:")
        print("  directory    Directory to scan for .marzip files (default: current directory)")
        print()
        print("Description:")
        print("  Recursively scans the specified directory for .marzip files and converts")
        print("  them to RTZ route files. RTZ files are created with the same name as the")
        print("  marzip file in the same directory.")
        print()
        print("Examples:")
        print("  python utils/rtz_maker.py                    # Scan current directory")
        print("  python utils/rtz_maker.py config/scenarios   # Scan specific directory")
        print("  python utils/rtz_maker.py /path/to/marzips   # Scan absolute path")
        return

    # Get directory from command line argument or use current directory
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        directory = "."

    # Check if directory exists
    if not os.path.exists(directory):
        print(f"❌ Directory not found: {directory}")
        sys.exit(1)

    if not os.path.isdir(directory):
        print(f"❌ Not a directory: {directory}")
        sys.exit(1)

    # Create RTZ maker and process directory
    rtz_maker = RTZMaker()

    try:
        rtz_maker.process_directory(directory)
    except KeyboardInterrupt:
        print("\n🛑 Cancelled by user")
        rtz_maker.cleanup()
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        rtz_maker.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    main()
