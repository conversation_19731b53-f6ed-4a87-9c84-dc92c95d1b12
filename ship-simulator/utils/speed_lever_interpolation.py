#!/usr/bin/env python3
"""
Speed-Lever Interpolation Utilities

이 모듈은 선박의 lever 값과 속도 간의 선형 보간 기능을 제공합니다.
speed table을 사용하여 lever ↔ speed 변환을 수행합니다.
"""

from typing import List, <PERSON><PERSON>


def interpolate_speed_from_lever(lever: float, table: List[Tuple[float, float]]) -> float:
    """선형 인터폴레이션을 통해 레버값으로 속도(knot)를 예측."""
    for i in range(len(table) - 1):
        l1, s1 = table[i]
        l2, s2 = table[i + 1]
        if l1 <= lever <= l2:
            # 선형 보간
            ratio = (lever - l1) / (l2 - l1)
            return s1 + ratio * (s2 - s1)
    # 범위 벗어나면 클램프
    return table[0][1] if lever < table[0][0] else table[-1][1]


def interpolate_lever_from_speed(speed_knots: float, table: List[Tuple[float, float]]) -> float:
    """선형 인터폴레이션을 통해 속도(knot)로 레버값을 예측."""
    for i in range(len(table) - 1):
        l1, s1 = table[i]
        l2, s2 = table[i + 1]
        if s1 <= speed_knots <= s2:
            # 선형 보간
            ratio = (speed_knots - s1) / (s2 - s1)
            return l1 + ratio * (l2 - l1)
    # 범위 벗어나면 클램프
    return table[0][0] if speed_knots < table[0][1] else table[-1][0]
