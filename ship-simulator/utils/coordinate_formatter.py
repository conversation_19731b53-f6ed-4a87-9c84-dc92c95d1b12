"""
Coordinate formatting utilities for NMEA sentences.
Provides functions to format latitude and longitude coordinates.
Also includes parsing functions for various coordinate formats.
"""

import re


def format_nmea_coordinates(lat: float, lon: float) -> tuple:
    """
    Convert latitude and longitude to NMEA format strings.
    
    NMEA coordinate format:
    - Latitude: DDMM.MMMM,N/S (degrees + minutes with 4 decimal places)
    - Longitude: DDDMM.MMMM,E/W (degrees + minutes with 4 decimal places)
    
    Args:
        lat: Latitude in decimal degrees
        lon: Longitude in decimal degrees
        
    Returns:
        Tuple of (lat_string, lon_string) in NMEA format
        
    Example:
        >>> format_nmea_coordinates(37.7749, -122.4194)
        ('3746.4940,N', '12225.1640,W')
    """
    # Determine the hemisphere for latitude
    lat_pos = "N" if lat >= 0 else "S"

    # Determine the hemisphere for longitude
    lon_pos = "E" if lon >= 0 else "W"

    # Calculate absolute values for degrees
    lat_abs = abs(lat)
    lon_abs = abs(lon)

    # Format degrees with leading zeros if necessary
    lat_deg = f"{int(lat_abs):02}"
    lon_deg = f"{int(lon_abs):03}"

    # Calculate minutes
    lat_min = 60 * (lat_abs - int(lat_abs))
    lon_min = 60 * (lon_abs - int(lon_abs))

    # Format minutes to 4 decimal places with leading zeros if necessary
    lat_min_str = f"{lat_min:07.4f}"
    lon_min_str = f"{lon_min:07.4f}"

    # Concatenate the parts into the final strings
    lat_gga = f"{lat_deg}{lat_min_str},{lat_pos}"
    lon_gga = f"{lon_deg}{lon_min_str},{lon_pos}"

    return lat_gga, lon_gga


def format_coordinates_with_precision(lat: float, lon: float, precision: int = 4) -> tuple:
    """
    Convert coordinates to NMEA format with custom precision.
    
    Args:
        lat: Latitude in decimal degrees
        lon: Longitude in decimal degrees
        precision: Number of decimal places for minutes (default: 4)
        
    Returns:
        Tuple of (lat_string, lon_string) in NMEA format
    """
    # Determine hemispheres
    lat_pos = "N" if lat >= 0 else "S"
    lon_pos = "E" if lon >= 0 else "W"

    # Calculate absolute values
    lat_abs = abs(lat)
    lon_abs = abs(lon)

    # Extract degrees and minutes
    lat_deg = int(lat_abs)
    lon_deg = int(lon_abs)
    lat_min = (lat_abs - lat_deg) * 60
    lon_min = (lon_abs - lon_deg) * 60

    # Format with custom precision
    lat_deg_str = f"{lat_deg:02d}"
    lon_deg_str = f"{lon_deg:03d}"
    
    # Calculate format width for minutes (2 digits + decimal point + precision)
    min_width = 3 + precision
    lat_min_str = f"{lat_min:0{min_width}.{precision}f}"
    lon_min_str = f"{lon_min:0{min_width}.{precision}f}"

    # Create final strings
    lat_string = f"{lat_deg_str}{lat_min_str},{lat_pos}"
    lon_string = f"{lon_deg_str}{lon_min_str},{lon_pos}"

    return lat_string, lon_string


def parse_nmea_coordinates(lat_string: str, lon_string: str) -> tuple:
    """
    Parse NMEA coordinate strings back to decimal degrees.
    
    Args:
        lat_string: NMEA latitude string (e.g., "3746.4940,N")
        lon_string: NMEA longitude string (e.g., "12225.1640,W")
        
    Returns:
        Tuple of (latitude, longitude) in decimal degrees
        
    Raises:
        ValueError: If coordinate strings are invalid
    """
    try:
        # Parse latitude
        lat_parts = lat_string.split(',')
        if len(lat_parts) != 2:
            raise ValueError("Invalid latitude format")
        
        lat_coord, lat_dir = lat_parts
        lat_deg = int(lat_coord[:2])
        lat_min = float(lat_coord[2:])
        lat_decimal = lat_deg + lat_min / 60
        
        if lat_dir == 'S':
            lat_decimal = -lat_decimal
        elif lat_dir != 'N':
            raise ValueError("Invalid latitude direction")
        
        # Parse longitude
        lon_parts = lon_string.split(',')
        if len(lon_parts) != 2:
            raise ValueError("Invalid longitude format")
        
        lon_coord, lon_dir = lon_parts
        lon_deg = int(lon_coord[:3])
        lon_min = float(lon_coord[3:])
        lon_decimal = lon_deg + lon_min / 60
        
        if lon_dir == 'W':
            lon_decimal = -lon_decimal
        elif lon_dir != 'E':
            raise ValueError("Invalid longitude direction")
        
        return lat_decimal, lon_decimal
        
    except (ValueError, IndexError) as e:
        raise ValueError(f"Error parsing coordinates: {e}")


def validate_coordinates(lat: float, lon: float) -> bool:
    """
    Validate latitude and longitude values.
    
    Args:
        lat: Latitude in decimal degrees
        lon: Longitude in decimal degrees
        
    Returns:
        True if coordinates are valid
    """
    return -90 <= lat <= 90 and -180 <= lon <= 180


# Backward compatibility functions
def lat_lon_string(lat: float, lon: float) -> tuple:
    """
    Backward compatibility function for lat_lon_string.
    
    Args:
        lat: Latitude in decimal degrees
        lon: Longitude in decimal degrees
        
    Returns:
        Tuple of (lat_string, lon_string) in NMEA format
    """
    return format_nmea_coordinates(lat, lon)


# Example usage and testing
if __name__ == "__main__":
    # Test coordinate formatting
    test_coords = [
        (37.7749, -122.4194),  # San Francisco
        (35.1796, 129.0756),   # Busan
        (0.0, 0.0),            # Equator/Prime Meridian
        (-33.8688, 151.2093),  # Sydney
    ]
    
    print("Coordinate Formatting Test")
    print("=" * 50)
    
    for lat, lon in test_coords:
        lat_str, lon_str = format_nmea_coordinates(lat, lon)
        print(f"({lat:8.4f}, {lon:9.4f}) -> {lat_str:12s} {lon_str:13s}")
        
        # Test parsing back
        parsed_lat, parsed_lon = parse_nmea_coordinates(lat_str, lon_str)
        print(f"  Parsed back: ({parsed_lat:8.4f}, {parsed_lon:9.4f})")
        print(f"  Error: ({abs(lat - parsed_lat):.6f}, {abs(lon - parsed_lon):.6f})")
        print()
    
    # Test validation
    print("Coordinate Validation Test")
    print("-" * 30)
    invalid_coords = [(91, 0), (0, 181), (-91, 0), (0, -181)]
    for lat, lon in invalid_coords:
        valid = validate_coordinates(lat, lon)
        print(f"({lat:3d}, {lon:4d}) -> Valid: {valid}")


def parse_dms_coordinate(coord_str: str) -> float:
    """
    Parse degree-minute-second coordinate string to decimal degrees.

    Supports various formats:
    - "33°10.776'N" or "33°10.776N"
    - "129°4.536'E" or "129°4.536E"
    - "33d10.776m N" or "33d10.776mN"
    - "33 10.776 N" or "33 10.776N"
    - "1°56.234'E" or "1°56.234E"

    Args:
        coord_str: Coordinate string in DMS format

    Returns:
        Decimal degrees (positive for N/E, negative for S/W)

    Raises:
        ValueError: If coordinate string format is invalid

    Examples:
        >>> parse_dms_coordinate("33°10.776'N")
        33.1796
        >>> parse_dms_coordinate("129°4.536'E")
        129.0756
        >>> parse_dms_coordinate("1°56.234'E")
        1.9372333333333334
    """
    if not coord_str or not isinstance(coord_str, str):
        raise ValueError("Coordinate string cannot be empty")

    coord_str = coord_str.strip().upper()

    # Pattern to match various DMS formats
    # Captures: degrees, minutes, hemisphere
    patterns = [
        r'(\d+)°(\d+\.?\d*)[\'M]?\s*([NSEW])',  # 33°10.776'N or 33°10.776N
        r'(\d+)[D°]\s*(\d+\.?\d*)[M\']?\s*([NSEW])',  # 33d10.776m N or 33°10.776 N
        r'(\d+)\s+(\d+\.?\d*)\s+([NSEW])',  # 33 10.776 N (with spaces)
    ]

    match = None
    for pattern in patterns:
        match = re.match(pattern, coord_str)
        if match:
            break

    if not match:
        raise ValueError(f"Invalid coordinate format: {coord_str}")

    degrees_str, minutes_str, hemisphere = match.groups()

    try:
        degrees = float(degrees_str)
        minutes = float(minutes_str)
    except ValueError:
        raise ValueError(f"Invalid numeric values in coordinate: {coord_str}")

    # Validate ranges
    if hemisphere in ['N', 'S']:
        if degrees > 90:
            raise ValueError(f"Invalid latitude degrees: {degrees}")
    elif hemisphere in ['E', 'W']:
        if degrees > 180:
            raise ValueError(f"Invalid longitude degrees: {degrees}")

    if minutes >= 60:
        raise ValueError(f"Invalid minutes: {minutes}")

    # Convert to decimal degrees
    decimal_degrees = degrees + minutes / 60.0

    # Apply hemisphere (negative for S/W)
    if hemisphere in ['S', 'W']:
        decimal_degrees = -decimal_degrees

    return decimal_degrees


def parse_position_string(position_str: str) -> tuple:
    """
    Parse position string containing both latitude and longitude.

    Supports formats:
    - "33°10.776'N 129°4.536'E"
    - "33°10.776N, 129°4.536E"
    - "33 10.776 N, 129 4.536 E"

    Args:
        position_str: Position string containing lat and lon

    Returns:
        Tuple of (latitude, longitude) in decimal degrees

    Examples:
        >>> parse_position_string("33°10.776'N 129°4.536'E")
        (33.1796, 129.0756)
        >>> parse_position_string("1°56.234'E 2°30.456'N")  # Note: order doesn't matter
        (2.5076, 1.9372333333333334)
    """
    if not position_str or not isinstance(position_str, str):
        raise ValueError("Position string cannot be empty")

    # Split by common separators
    parts = re.split(r'[,\s]+', position_str.strip())

    # Filter out empty parts
    parts = [part for part in parts if part]

    if len(parts) < 2:
        raise ValueError(f"Position string must contain both latitude and longitude: {position_str}")

    lat = None
    lon = None

    # Parse each part and determine if it's lat or lon based on hemisphere
    for part in parts:
        try:
            coord = parse_dms_coordinate(part)
            hemisphere = part.strip().upper()[-1]

            if hemisphere in ['N', 'S']:
                if lat is not None:
                    raise ValueError(f"Multiple latitude values found in: {position_str}")
                lat = coord
            elif hemisphere in ['E', 'W']:
                if lon is not None:
                    raise ValueError(f"Multiple longitude values found in: {position_str}")
                lon = coord
        except ValueError as e:
            raise ValueError(f"Error parsing coordinate '{part}': {e}")

    if lat is None:
        raise ValueError(f"No latitude found in position string: {position_str}")
    if lon is None:
        raise ValueError(f"No longitude found in position string: {position_str}")

    return lat, lon
