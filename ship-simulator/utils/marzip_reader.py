#!/usr/bin/env python3
"""
Marzip file reader for extracting initial configuration and ship data.

This module handles the initial reading and parsing of .marzip files to extract:
- Ship initial conditions (position, speed, course)
- Configuration data for scenario setup
- Raw marzip data for further processing by ais_creator modules

The actual AIS message processing is handled by ais_creator.marzip_processor.
"""

import os
import json
import zipfile
import shutil
from typing import Dict, List, Tuple, Optional
import pyarrow as pa
import pyarrow.ipc as ipc



class MarzipConfigExtractor:
    """
    Extract initial configuration from marzip files for scenario mode.
    This class focuses on extracting ship initial conditions from marzip data.
    """
    
    def __init__(self, file_path: str):
        """
        Initialize extractor with marzip file path.
        
        Args:
            file_path: Path to .marzip file
        """
        self.file_path = file_path
        self.timeseries_dataset = []
        self.static_dataset = []
        self.simulation_result = {}
        
    def _read_arrow_file(self, file_path):
        """Read Arrow format file (IPC File or Streaming format)."""
            
        with open(file_path, 'rb') as f:
            try:
                # Attempt to read as IPC File Format
                reader = ipc.RecordBatchFileReader(f)
                return reader.read_all()
            except pa.lib.ArrowInvalid:
                # If failed, attempt to read as IPC Streaming Format
                f.seek(0)  # Reset file pointer
                try:
                    reader = ipc.RecordBatchStreamReader(f)
                    return reader.read_all()
                except pa.lib.ArrowInvalid as e:
                    raise e

    def extract_marzip_data(self) -> Dict:
        """
        Extract .marzip file and read timeseries/static data.
        
        Returns:
            dict: Contains 'timeseries_dataset' and 'static_dataset'
        """
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"Marzip file not found: {self.file_path}")
            
        extracted_files = []

        # Check if the file is a ZIP archive
        if zipfile.is_zipfile(self.file_path):
            with zipfile.ZipFile(self.file_path, 'r') as zip_ref:
                extract_dir = os.path.splitext(self.file_path)[0]  # Remove .marzip extension
                zip_ref.extractall(extract_dir)
                extracted_files = [os.path.join(extract_dir, name) for name in zip_ref.namelist()]
        else:
            raise ValueError("Provided file is not a valid .marzip archive.")

        # Read each file in the extracted contents
        timeseries_dataset = []
        static_dataset = []
        
        for file in extracted_files:
            if file.endswith('timeseries.arrow'):
                try:
                    table = self._read_arrow_file(file)
                    for row in table.to_pylist():
                        timeseries_info = {key: value for key, value in row.items()}
                        timeseries_dataset.append(timeseries_info)
                except Exception as e:
                    print(f"Error reading timeseries file {file}: {e}")
                    
            elif file.endswith('static.arrow'):
                try:
                    table = self._read_arrow_file(file)
                    for row in table.to_pylist():
                        static_info = {key: value for key, value in row.items()}
                        static_dataset.append(static_info)
                except Exception as e:
                    print(f"Error reading static file {file}: {e}")
                    
            elif file.endswith('.json'):
                try:
                    with open(file, 'r') as json_file:
                        self.simulation_result = json.load(json_file)
                except Exception as e:
                    print(f"Error reading JSON file {file}: {e}")

        # Clean up extracted files
        if extract_dir and os.path.exists(extract_dir):
            try:
                shutil.rmtree(extract_dir)
            except Exception as e:
                print(f"Failed to delete extracted directory: {e}")

        # Store data in instance
        self.timeseries_dataset = timeseries_dataset
        self.static_dataset = static_dataset

        return {
            "timeseries_dataset": timeseries_dataset,
            "static_dataset": static_dataset
        }
    
    def get_ownship_initial_config(self) -> Optional[Dict]:
        """
        Extract ownship initial configuration from marzip data.
        
        Returns:
            dict: Initial configuration for ownship or None if not found
        """
        if not self.timeseries_dataset:
            print("⚠️ No timeseries data available")
            return None
        
        # Find ownship data (ownShip = True)
        ownship_data = [item for item in self.timeseries_dataset if item.get('ownShip', False)]
        
        if not ownship_data:
            print("⚠️ No ownship data found in marzip file")
            return None
        
        # Get the first ownship record for initial conditions
        first_record = ownship_data[0]
        
        # Convert to simulation config format
        initial_config = {
            "position": {
                "lat": first_record.get('lat', 0.0),
                "lon": first_record.get('lon', 0.0)
            },
            "speed": first_record.get('sog', 0.0),  # Speed over ground
            "course": first_record.get('cog', 0.0),  # Course over ground
            "heading": first_record.get('heading', first_record.get('cog', 0.0))
        }
        
        print(f"📍 Extracted ownship initial config:")
        print(f"   Position: {initial_config['position']['lat']:.6f}°, {initial_config['position']['lon']:.6f}°")
        print(f"   Speed: {initial_config['speed']:.1f} knots")
        print(f"   Course: {initial_config['course']:.1f}°")
        
        return initial_config
    

def extract_marzip_for_ais(file_path: str) -> Optional[Dict]:
    """
    Extract marzip data for AIS message generation.

    Args:
        file_path: Path to marzip file

    Returns:
        dict: Marzip data for AIS processing or None if failed
    """
    try:
        extractor = MarzipConfigExtractor(file_path)
        return extractor.extract_marzip_data()
    except Exception as e:
        print(f"❌ Error extracting marzip for AIS: {e}")
        return None


