#!/usr/bin/env python3
"""
Speed Table Generator for IEC 62065 Ship Model

Generates speed tables by varying lever position from 0.1 to 1.0
and running high-speed simulations to find steady-state speeds.

Usage:
    python utils/speed_table_generator.py [options]
"""

import os
import sys
import json
import time
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.simulation_config import SimulationConfig
from ship_models.shipmodel_62065 import ShipModel62065
from ship_models.wave_disturbance import DynamicWaveDisturbance


class SpeedTableGenerator:
    """Generate speed tables for different lever positions using IEC 62065 model."""
    
    def __init__(self, config_path: str = "config/simulation_config.json"):
        """
        Initialize speed table generator.
        
        Args:
            config_path: Path to simulation configuration file
        """
        self.config = SimulationConfig(config_path)
        self.results = []
        
        # Simulation parameters
        self.lever_range = np.arange(0.05, 1.01, 0.05)  # 0.05 to 1.0 in 0.05 steps
        self.simulation_time = 300.0  # 5 minutes to reach steady state
        self.timestep = 0.5  # 2Hz simulation
        self.time_acceleration = 20.0  # 20x speed up
        self.steady_state_threshold = 0.005  # tighter threshold: 0.005 knots/s
        self.steady_state_window = 60  # more steps to check for steady state
        
        print("🎯 SPEED TABLE GENERATOR - IEC 62065 Model")
        print("=" * 60)
        print(f"📊 Lever range: {self.lever_range[0]:.1f} - {self.lever_range[-1]:.1f}")
        print(f"⏱️  Simulation time: {self.simulation_time}s")
        print(f"🚀 Time acceleration: {self.time_acceleration}x")
        print(f"📈 Timestep: {self.timestep}s")
        print()
    
    def _create_initial_ship_state(self) -> Dict[str, Any]:
        """Create initial ship state dictionary."""
        sim_params = self.config.get_simulation_parameters()
        ship_param = sim_params['ship_param']

        # Get initial position
        lat_ini, lon_ini, _ = self.config.get_initial_position()

        # Calculate Izz if not present (using standard formula)
        Lpp = ship_param['Lpp']
        Mass = ship_param['Mass']
        Izz = ship_param.get('Izz', Mass * (Lpp / 4.0) ** 2)  # Standard approximation

        return {
            # Position and orientation
            'lat': lat_ini,
            'lon': lon_ini,
            'x': 0.0,
            'y': 0.0,
            'N': 0.0,   # North position (for compatibility)
            'E': 0.0,   # East position (for compatibility)
            'Hdg': 0.0,  # degrees
            'psi': 0.0,  # heading (for compatibility)
            'COG': 0.0,  # degrees
            'beta': 0.0,  # degrees

            # Velocities (m/s) - both formats for compatibility
            'u': 0.0,   # surge velocity (original format)
            'v': 0.0,   # sway velocity (original format)
            'ug': 0.0,  # surge velocity
            'vg': 0.0,  # sway velocity
            'r': 0.0,   # yaw rate (rad/s)
            'uw': 0.0,  # surge velocity through water
            'vw': 0.0,  # sway velocity through water
            'spd': 0.0, # speed (m/s)

            # Control surfaces
            'rudder_angle': 0.0,  # degrees
            'del': 0.0,  # rudder angle (backward compatibility)
            'lever': 0.0,  # lever position
            'lever_cmd': 0.0,  # lever command (for compatibility)

            # Ship parameters (from config)
            'Lpp': Lpp,
            'Mass': Mass,
            'Izz': Izz,

            # Derived quantities
            'SOG': 0.0,  # Speed over ground (knots)
            'STW': 0.0,  # Speed through water (knots)
        }
    
    def _is_steady_state(self, speed_history: List[float]) -> bool:
        """
        Check if ship has reached steady state.
        
        Args:
            speed_history: List of recent speed values
            
        Returns:
            True if steady state reached
        """
        if len(speed_history) < self.steady_state_window:
            return False
        
        recent_speeds = speed_history[-self.steady_state_window:]
        speed_change = max(recent_speeds) - min(recent_speeds)
        
        return speed_change < self.steady_state_threshold
    
    async def simulate_lever_position(self, lever: float) -> Dict[str, Any]:
        """
        Simulate ship dynamics for a specific lever position.
        
        Args:
            lever: Lever position (0.1 to 1.0)
            
        Returns:
            Dictionary with simulation results
        """
        print(f"🔄 Simulating lever position: {lever:.1f}")
        
        # Get simulation parameters
        sim_params = self.config.get_simulation_parameters()
        ship_param = sim_params['ship_param']

        # Use disabled environment to avoid wind coefficient issues
        env_param = {
            'wind': {'speed': 0.0, 'direction': 0.0, 'enabled': False},
            'current': {'speed': 0.0, 'direction': 0.0, 'enabled': False},
            'wave': {'sea_state': 0, 'enabled': False}
        }

        # Initialize ship model
        ship_model = ShipModel62065(env_param)
        
        # Initialize wave disturbance if enabled
        wave_disturbance = None
        if self.config.is_wave_enabled():
            sea_state = env_param.get("wave", {}).get("sea_state", 0)
            wave_disturbance = DynamicWaveDisturbance(sea_state)
        
        # Create initial state
        ship_state = self._create_initial_ship_state()
        
        # Command with fixed lever position
        command = {
            'rudder': 0.0,      # No rudder input
            'rudder_cmd': 0.0,  # No rudder input (alternative key)
            'lever': lever,     # Fixed lever position
            'lever_cmd': lever  # Fixed lever position (alternative key)
        }
        
        # Simulation variables
        current_time = 0.0
        speed_history = []
        max_speed = 0.0
        steady_state_speed = 0.0
        steady_state_time = 0.0
        steps = 0
        
        start_time = time.perf_counter()
        
        # Run simulation loop
        while current_time < self.simulation_time:
            # Update ship state with current lever command
            ship_state['lever'] = lever
            ship_state['lever_cmd'] = lever

            # Get wave effect
            wave_effect = 0.0
            if wave_disturbance:
                wave_effect = wave_disturbance.get_Wb(current_time)

            # No tidal effects for speed table generation
            tidal_effects = {
                'ux': 0.0, 'vy': 0.0,  # Original format
                'Tx': 0.0, 'Ty': 0.0, 'TN': 0.0, 'TE': 0.0  # Extended format
            }

            # Run ship dynamics
            ship_state = ship_model.calculate_dynamics(
                ship_state, ship_param, command, self.timestep,
                wave_effect, tidal_effects, self.time_acceleration
            )
            
            # Record speed
            current_speed = ship_state['SOG']
            speed_history.append(current_speed)
            max_speed = max(max_speed, current_speed)
            
            # Check for steady state
            if self._is_steady_state(speed_history):
                if steady_state_time == 0.0:  # First time reaching steady state
                    steady_state_speed = np.mean(speed_history[-self.steady_state_window:])
                    steady_state_time = current_time
                    print(f"  ✅ Steady state reached at {current_time:.1f}s: {steady_state_speed:.2f} knots")
                    break
            
            # Update time and step
            current_time += self.timestep
            steps += 1
            
            # Progress indicator every 5 seconds
            if steps % int(5.0 / self.timestep) == 0:
                print(f"  ⏱️  {current_time:.1f}s: {current_speed:.2f} knots")
        
        simulation_time = time.perf_counter() - start_time
        
        # If steady state not reached, use final speed
        if steady_state_time == 0.0:
            steady_state_speed = np.mean(speed_history[-self.steady_state_window:]) if len(speed_history) >= self.steady_state_window else speed_history[-1]
            steady_state_time = current_time
            print(f"  ⚠️  Steady state not reached, using final speed: {steady_state_speed:.2f} knots")
        
        return {
            'lever': lever,
            'steady_state_speed': steady_state_speed,
            'max_speed': max_speed,
            'steady_state_time': steady_state_time,
            'total_simulation_time': current_time,
            'computation_time': simulation_time,
            'steps': steps,
            'final_state': {
                'SOG': ship_state['SOG'],
                'STW': ship_state['STW'],
                'ug': ship_state['ug'],
                'vg': ship_state['vg'],
                'r': ship_state['r']
            }
        }
    
    async def generate_speed_table(self) -> pd.DataFrame:
        """
        Generate complete speed table for all lever positions.
        
        Returns:
            DataFrame with speed table results
        """
        print("🚀 Starting speed table generation...")
        print()
        
        total_start_time = time.perf_counter()
        
        # Run simulations for each lever position
        for i, lever in enumerate(self.lever_range):
            print(f"📊 Progress: {i+1}/{len(self.lever_range)} ({(i+1)/len(self.lever_range)*100:.1f}%)")
            
            result = await self.simulate_lever_position(lever)
            self.results.append(result)
            
            print(f"  📈 Result: Lever {lever:.1f} → {result['steady_state_speed']:.2f} knots")
            print()
        
        total_time = time.perf_counter() - total_start_time
        
        # Create DataFrame
        df = pd.DataFrame(self.results)
        
        # Summary
        print("=" * 60)
        print("📊 SPEED TABLE GENERATION COMPLETE")
        print(f"⏱️  Total computation time: {total_time:.2f}s")
        print(f"📈 Speed range: {df['steady_state_speed'].min():.2f} - {df['steady_state_speed'].max():.2f} knots")
        print()
        
        return df
    
    def save_results(self, df: pd.DataFrame, output_dir: str = "output") -> None:
        """
        Save results to files.
        
        Args:
            df: DataFrame with results
            output_dir: Output directory
        """
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save CSV
        csv_file = os.path.join(output_dir, f"speed_table_{timestamp}.csv")
        df.to_csv(csv_file, index=False)
        print(f"💾 CSV saved: {csv_file}")
        
        # Save JSON with detailed results
        json_file = os.path.join(output_dir, f"speed_table_detailed_{timestamp}.json")
        detailed_results = {
            'metadata': {
                'generation_time': timestamp,
                'lever_range': self.lever_range.tolist(),
                'simulation_time': self.simulation_time,
                'timestep': self.timestep,
                'time_acceleration': self.time_acceleration,
                'config_file': self.config.config_file
            },
            'results': self.results
        }
        
        with open(json_file, 'w') as f:
            json.dump(detailed_results, f, indent=2)
        print(f"💾 JSON saved: {json_file}")
        
        # Print summary table
        print()
        print("📋 SPEED TABLE SUMMARY")
        print("-" * 40)
        print(f"{'Lever':<8} {'Speed (kts)':<12} {'Time (s)':<10}")
        print("-" * 40)
        for _, row in df.iterrows():
            print(f"{row['lever']:<8.1f} {row['steady_state_speed']:<12.2f} {row['steady_state_time']:<10.1f}")
        print("-" * 40)


async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate speed table for IEC 62065 ship model")
    parser.add_argument("--config", "-c", default="config/simulation_config.json",
                       help="Path to simulation config file")
    parser.add_argument("--output", "-o", default="output",
                       help="Output directory for results")
    
    args = parser.parse_args()
    
    try:
        # Create generator
        generator = SpeedTableGenerator(args.config)
        
        # Generate speed table
        df = await generator.generate_speed_table()
        
        # Save results
        generator.save_results(df, args.output)
        
        print("🎉 Speed table generation completed successfully!")
        
    except KeyboardInterrupt:
        print("\n🛑 Cancelled by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
