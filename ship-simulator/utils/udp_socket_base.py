import socket
import asyncio
import logging
import time
import threading
from typing import Optional, Callable

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class UDPSocketBase:
    def __init__(
        self,
        local_ip: str = None,
        local_port: int = None,
        remote_ip: str = None,
        remote_port: int = None,
        is_receiver: bool = False,
        is_sender: bool = True,
        is_auto_set: bool = True,
        auto_recovery: bool = True,
        recovery_interval: float = 5.0,
        recovery_callback: Optional[Callable] = None
    ):
        """
        UDP 소켓을 관리하는 클래스 (자동 복구 기능 포함).

        기본 동작은 송신 전용(is_sender=True, is_receiver=False)입니다.
        옵션으로 수신 기능만 사용하는(수신 전용: is_receiver=True, is_sender=False)
        또는 양방향 통신(is_receiver=True, is_sender=True) 모드로 사용할 수 있습니다.

        네트워크 연결이 끊어지면 자동으로 소켓을 재생성하여 복구합니다.

        :param local_ip: 소켓의 로컬 IP 주소 (수신 시 바인딩용)
        :param local_port: 소켓의 로컬 포트 번호 (수신 시 바인딩용)
        :param remote_ip: 송신 대상 IP (송신 시 기본 대상)
        :param remote_port: 송신 대상 포트 (송신 시 기본 대상)
        :param is_receiver: 수신 기능 활성화 여부 (True이면 로컬 주소에 바인딩)
        :param is_sender: 송신 기능 활성화 여부 (False이면 send_data() 호출 시 동작하지 않음)
        :param is_auto_set: 객체 생성 시 자동으로 소켓을 설정할지 여부
        :param auto_recovery: 자동 복구 기능 활성화 여부
        :param recovery_interval: 복구 시도 간격 (초)
        :param recovery_callback: 복구 완료 시 호출할 콜백 함수
        """
        self.local_ip = local_ip
        self.local_port = local_port
        self.remote_ip = remote_ip
        self.remote_port = remote_port
        self.is_receiver = is_receiver  # 수신 기능 활성화 여부
        self.is_sender = is_sender      # 송신 기능 활성화 여부
        self.sock = None
        self.sndbuf = 65536
        self.rcvbuf = 65536

        # Auto recovery settings
        self.auto_recovery = auto_recovery
        self.recovery_interval = recovery_interval
        self.recovery_callback = recovery_callback
        self._recovery_thread = None
        self._stop_recovery = False
        self._socket_healthy = True
        self._last_error_time = 0

        if is_auto_set:
            self.setup_socket()

    def setup_socket(self):
        """소켓을 설정합니다. 자동 복구 시에도 호출됩니다."""
        if self.sock:
            try:
                self.sock.close()
            except Exception as e:
                logger.error(f"Error closing existing socket: {e}")

        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

            # 주소 재사용 설정
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, self.sndbuf)
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, self.rcvbuf)

            # Low-latency options for real-time applications
            try:
                # Disable Nagle's algorithm for immediate sending
                self.sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            except:
                pass  # TCP_NODELAY not applicable to UDP

            try:
                # Set socket priority for low latency (Linux)
                self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_PRIORITY, 6)
            except:
                pass  # Not available on all systems

            self.sock.setblocking(False)

            if self.is_receiver:
                self.sock.bind((self.local_ip, self.local_port))
                logger.info(f"Socket bound to {self.local_ip}:{self.local_port} for receiver.")
            else:
                # 멀티캐스트 송신 설정
                if self.remote_ip and self.remote_ip.startswith('239.'):  # 멀티캐스트 주소인 경우
                    # 호스트 IP 찾기
                    host_ip = self._get_host_ip()
                    logger.info(f"🔍 Found host IP: {host_ip}")

                    # 참조 코드와 동일한 소켓 생성 (UDP)
                    self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, socket.IPPROTO_UDP)

                    # 멀티캐스트 인터페이스 설정
                    self.sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_IF, socket.inet_aton(host_ip))
                    logger.info(f"✅ Set multicast interface to {host_ip}")

                    # 멀티캐스트 TTL 설정
                    import struct
                    MULTICAST_TTL = 1
                    self.sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, struct.pack("b", MULTICAST_TTL))
                    logger.info(f"✅ Set multicast TTL to {MULTICAST_TTL}")

                    # 소켓 바인딩
                    self.sock.bind((host_ip, 0))
                    logger.info(f"✅ Bound socket to {host_ip}:0")

                    logger.info(f"🚀 Multicast sender configured for {self.remote_ip}:{self.remote_port}")

                logger.info(f"Socket initialized for sender to {self.remote_ip}:{self.remote_port}")

            # 소켓 설정 성공
            self._socket_healthy = True
            logger.info("✅ Socket setup completed successfully")

            # 자동 복구 스레드 시작
            if self.auto_recovery and not self._recovery_thread:
                self._start_recovery_monitor()

        except Exception as e:
            logger.error(f"❌ Failed to setup socket: {e}")
            self._socket_healthy = False
            self._last_error_time = time.time()

            # 자동 복구가 활성화되어 있으면 복구 시도
            if self.auto_recovery:
                self._start_recovery_monitor()
            else:
                raise

    def _get_host_ip(self):
        """호스트의 실제 IP 주소를 찾습니다. communication.json에서 설정된 값을 사용합니다."""
        # communication.json에서 설정된 interface_ip 사용
        try:
            import json
            with open('config/communication.json', 'r') as f:
                config = json.load(f)
                if config.get("communication_mode") == "multicast":
                    interface_ip = config["multicast"].get("interface_ip", "***********")
                    logger.info(f"Using configured interface IP: {interface_ip}")
                    return interface_ip
        except Exception as e:
            logger.debug(f"Could not load config: {e}")

        # 기본값
        default_ip = '***********'
        logger.info(f"Using default IP: {default_ip}")
        return default_ip

    def _start_recovery_monitor(self):
        """자동 복구 모니터링 스레드를 시작합니다."""
        if self._recovery_thread and self._recovery_thread.is_alive():
            return

        self._stop_recovery = False
        self._recovery_thread = threading.Thread(target=self._recovery_monitor, daemon=True)
        self._recovery_thread.start()
        logger.info("🔄 Auto recovery monitor started")

    def _recovery_monitor(self):
        """소켓 상태를 모니터링하고 필요시 복구를 시도합니다."""
        while not self._stop_recovery:
            try:
                if not self._socket_healthy:
                    logger.info("🔧 Attempting socket recovery...")

                    # 복구 시도
                    try:
                        self.setup_socket()
                        if self._socket_healthy:
                            logger.info("✅ Socket recovery successful!")
                            if self.recovery_callback:
                                self.recovery_callback()
                    except Exception as e:
                        logger.error(f"❌ Socket recovery failed: {e}")

                time.sleep(self.recovery_interval)

            except Exception as e:
                logger.error(f"❌ Error in recovery monitor: {e}")
                time.sleep(self.recovery_interval)

    def _mark_socket_unhealthy(self, error: Exception):
        """소켓을 비정상 상태로 표시합니다."""
        if self._socket_healthy:
            logger.warning(f"🚨 Socket marked as unhealthy: {error}")
            self._socket_healthy = False
            self._last_error_time = time.time()

    def send_data(self, data: bytes, remote_ip: str = None, remote_port: int = None):
        """
        데이터를 지정된 IP와 포트로 전송합니다.
        기본적으로 생성 시 지정된 remote_ip, remote_port를 사용하며,
        호출 시 인자로 전달된 값이 있으면 이를 우선 사용합니다.
        
        송신 기능이 비활성화(is_sender=False)된 경우에는 동작하지 않습니다.
        
        :param data: 전송할 데이터 (bytes)
        :param remote_ip: 전송 대상 IP (옵션)
        :param remote_port: 전송 대상 포트 (옵션)
        """
        if not self.is_sender:
            logger.error("Socket is configured as receive-only. Sending data is disabled.")
            return

        target_ip = remote_ip if remote_ip is not None else self.remote_ip
        target_port = remote_port if remote_port is not None else self.remote_port

        if not target_ip or not target_port:
            logger.error("Remote IP/port not specified for sending data.")
            return

        try:
            if self.sock:
                self.sock.sendto(data, (target_ip, target_port))
        except Exception as e:
            logger.error(f"Error while sending data: {e}")
            # 자동 복구가 활성화되어 있으면 소켓을 비정상으로 표시
            if self.auto_recovery:
                self._mark_socket_unhealthy(e)

    async def receive_data(self):
        """
        비동기적으로 데이터를 수신합니다.
        수신 기능이 활성화되어 있어야 합니다.
        
        :return: (data, addr) 튜플. data는 수신된 bytes, addr는 송신자의 (ip, port)
        """
        if not self.sock:
            logger.error("Socket is not initialized.")
            return None, None
        if not self.is_receiver:
            logger.error("Socket is not configured as receiver.")
            return None, None

        loop = asyncio.get_running_loop()
        try:
            data = await loop.sock_recv(self.sock, self.rcvbuf)
            return data
        except Exception as e:
            logger.error(f"Error receiving data: {e}")
            # 자동 복구가 활성화되어 있으면 소켓을 비정상으로 표시
            if self.auto_recovery:
                self._mark_socket_unhealthy(e)
            return None

    def close_socket(self):
        """소켓을 닫고 자동 복구 스레드를 정리합니다."""
        # 자동 복구 스레드 정지
        self._stop_recovery = True
        if self._recovery_thread and self._recovery_thread.is_alive():
            self._recovery_thread.join(timeout=2)
            logger.info("🛑 Auto recovery monitor stopped")

        # 소켓 닫기
        if self.sock:
            self.sock.close()
            self.sock = None
            logger.info("Socket closed.")

        self._socket_healthy = False

    def clear_buffer(self):
        """소켓의 수신 버퍼를 비웁니다."""
        if not self.sock:
            return
        self.sock.setblocking(False)
        try:
            while True:
                self.sock.recvfrom(self.rcvbuf)
        except socket.error as e:
            # 데이터가 없으면 errno=11 (EAGAIN/EWOULDBLOCK)이 발생함
            if e.errno != 11:
                logger.error(f"Error while clearing buffer: {e}")
        finally:
            self.sock.setblocking(True)
