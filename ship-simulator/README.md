# RSA-Based Ship Simulator

A high-performance, async-based ship simulator that uses RSA (Rudder Sensor Angle) messages for direct rudder control and multicast NMEA data transmission.

## Features

- **RSA-Only Control**: Simplified control using only RSA messages
- **Async Architecture**: High-performance asynchronous simulation loop
- **Multicast NMEA**: Real-time NMEA data transmission via multicast
- **Professional Coordinate Conversion**: High-precision coordinate transformations using pyproj
- **Modular Design**: Clean separation of concerns with dedicated modules

## Architecture

```
RSA Commands (UDP) → ***********:4001 → Ship Simulator
                                              ↓
                                      Ship Dynamics Model
                                              ↓
                                      NMEA Data Generator
                                              ↓
                              *********:6501 → Multicast NMEA
```

## Quick Start

### 1. Install Dependencies

**Requirements:**
- Python 3.11+

```bash
pip install -r requirements.txt
```

**Core packages with versions:**
- numpy==2.3.1 (numerical computing)
- scipy==1.16.0 (scientific computing)
- pandas==2.3.1 (data processing)
- matplotlib==3.10.3 (visualization)
- pyproj==3.7.1 (coordinate transformation)
- pyarrow==21.0.0 (data serialization)
- PyYAML==6.0.2 (YAML parsing)
- redis==6.2.0 (Redis communication)
- pyais==2.12.0 (AIS message processing)

### 2. Run the Simulator

```bash
python run_simulator.py
```

### 3. Send RSA Commands

Use the test tool to send RSA commands:

```bash
python tests/send_rsa_test.py
```

Or send directly via UDP:

```bash
echo '$AGRSA,15.0,A,15.0,V*XX' | nc -u *********** 4001
```

## Communication Protocol

### RSA Input (Port 4001)

The simulator receives RSA (Rudder Sensor Angle) messages:

```
Format: $AGRSA,rudder_angle,A,rudder_angle,V*checksum
Example: $AGRSA,15.0,A,15.0,V*XX
```

- **rudder_angle**: Rudder angle in degrees (positive = starboard, negative = port)
- **Range**: Typically ±35 degrees

### NMEA Output (Multicast *********:6501)

The simulator transmits standard NMEA sentences:

- **GGA**: GPS Fix Data
- **RMC**: Recommended Minimum Navigation Information
- **VTG**: Track Made Good and Ground Speed
- **HDT**: Heading - True
- **ROT**: Rate of Turn
- **VBW**: Dual Ground/Water Speed
- **ZDA**: Time & Date
- **MWV**: Wind Speed and Angle
- **DPT**: Depth of Water

## Project Structure

```
├── core/                     # Main simulator package
│   ├── ship_simulator.py    # Main simulator class
│   ├── communication_manager.py  # UDP/Multicast communication
│   ├── autopilot_interface.py    # RSA message parsing
│   ├── navigation_data.py    # NMEA sentence generation
│   ├── ship_state.py         # Ship state management
│   ├── time_manager.py       # Time management
│   └── simulation_config.py  # Configuration management
├── ship_models/              # Ship dynamics models
│   ├── shipmodel_62065.py    # Main ship dynamics
│   ├── wave_disturbance.py   # Wave effects
│   ├── ship_config_loader.py # Ship parameter loading
│   └── heading_controller.py # Autopilot controller (reference)
├── utils/                    # Utility modules
│   ├── global_local_convertor.py  # Coordinate conversion
│   ├── udp_socket_base.py    # UDP socket utilities
│   └── utils.py              # Mathematical utilities
├── tests/                    # Test scripts
│   ├── test_rsa_simulator.py # Main simulator test
│   └── send_rsa_test.py      # RSA command sender
└── run_simulator.py          # Simple run script
```

## Configuration

### Default Settings

- **RSA Receiver**: `***********:4001`
- **NMEA Multicast**: `*********:6501`
- **Simulation Rate**: 10 Hz
- **Initial Position**: Busan Port (35.1796°N, 129.0756°E)

### Customization

Modify the simulator initialization in `run_simulator.py`:

```python
simulator = ShipSimulator(
    lat=35.1796,    # Initial latitude
    lon=129.0756,   # Initial longitude
    speed=10.0,     # Initial speed (knots)
    course=90.0     # Initial course (degrees)
)
```

## Testing

### RSA Command Testing

The `tests/send_rsa_test.py` script provides three testing modes:

1. **Static Commands**: Predefined sequence of rudder angles
2. **Dynamic Commands**: Sine wave rudder pattern
3. **Manual Commands**: Interactive rudder input

### Simulator Testing

Run the full simulator test:

```bash
python tests/test_rsa_simulator.py
```

## Key Improvements

### From Previous Version

- **Simplified Control**: Removed complex autopilot message processing
- **Direct RSA Control**: Rudder commands come directly from RSA messages
- **Async Performance**: Non-blocking message processing
- **Multicast Output**: Efficient data distribution to multiple receivers
- **Clean Architecture**: Modular design with clear separation of concerns

### Performance

- **10 Hz Simulation Rate**: Consistent timing regardless of message frequency
- **Non-blocking I/O**: Simulation continues even without RSA messages
- **Efficient Multicast**: Single transmission to multiple receivers

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **Network Issues**: Check firewall settings for UDP ports 4001 and 6501
3. **Multicast Problems**: Verify multicast routing is enabled

### Debug Mode

Enable debug output by modifying the simulator:

```python
# Add debug prints in core/ship_simulator.py
print(f"Received RSA: {rudder_angle}")
print(f"Ship state: {self.ship_state.get_summary()}")
```

## License

This project is part of the ship simulation system.

## Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Ensure backward compatibility where possible
