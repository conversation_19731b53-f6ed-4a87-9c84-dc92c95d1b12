#!/usr/bin/env python3
"""
Simple script to run the RSA-based ship simulator.
"""

import asyncio
import sys
import os
import subprocess
from ais_creator.marzip_aivdm_sender import MarzipAIS_Sender
from ais_creator.marzip_processor import Mar<PERSON>p<PERSON>arser


# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def setup_multicast_routing():
    """Setup basic multicast routing for Docker environment."""
    try:
        print("Setting up multicast routing...")

        # 기본 멀티캐스트 라우팅만 설정
        try:
            subprocess.run(['ip', 'route', 'add', '*********/8', 'dev', 'eth0'],
                         capture_output=True, text=True)
            print("✅ Added basic multicast route")
        except Exception as e:
            print(f"⚠️ Could not add multicast route: {e}")

    except Exception as e:
        print(f"⚠️ Could not setup multicast routing: {e}")
        print("Continuing without multicast routing setup...")

from core import ShipSimulator
from core import SimulationConfig
from core.communication_manager import CommunicationManager


async def run_scenario_mode(config: SimulationConfig, simulator):
    """
    Run simulator in SCENARIO mode with marzip-based initial config and AIS targets.

    Features:
    - Loads initial ship position/speed/course from marzip file
    - Spawns AIS targets from marzip scenario data
    - Runs concurrent simulation with AIS target transmission
    """
    print("🎬 RUNNING IN SCENARIO MODE")
    print("=" * 50)

    # Get marzip file from config
    marzip_name = config.simulation_set.get('marzip_name')
    if not marzip_name:
        print("⚠️  No marzip_name in config - falling back to normal mode")
        await run_normal_mode(config, simulator)
        return

    # Resolve marzip file path (relative to config directory)
    config_dir = os.path.dirname(os.path.abspath("config/simulation_config.json"))
    marzip_file = os.path.join(config_dir, marzip_name)
    marzip_file = os.path.normpath(marzip_file)

    if not os.path.exists(marzip_file):
        print(f"⚠️  Marzip file not found: {marzip_file} - falling back to normal mode")
        await run_normal_mode(config, simulator)
        return

    print(f"📦 Using marzip file: {marzip_file}")

    try:
        # Import marzip reader utilities
        from utils.marzip_reader import extract_marzip_for_ais

        print(f"📁 Loading marzip scenario file: {marzip_file}")

        # Extract and update initial configuration from marzip
        # Setup AIS targets
        try:

            # Initialize AIS sender
            ais_sender = MarzipAIS_Sender()
            ais_sender.setup_communication()

            # Process marzip file for AIS
            marzip_data = extract_marzip_for_ais(marzip_file)
            if marzip_data:
                send_queue = MarzipParser(marzip_data).sort_data_with_relative_time()

                print(f"📊 Loaded {len(send_queue)} sensor types from marzip")
                print(f"📡 AIS transmission target: {ais_sender.target_ip}:{ais_sender.target_port}")

                # Create AIS transmission tasks
                ais_tasks = []
                for sensor, target_group in send_queue.items():
                    for target_id, messages in target_group.items():
                        if sensor.lower() == "ais":
                            print(f"📋 AIS target {target_id}: {len(messages)} messages")
                            ais_tasks.append(ais_sender.start_sending(messages))

                if ais_tasks:
                    print(f"🚀 Starting scenario simulation with {len(ais_tasks)} AIS targets...")
                    print("   Simulator + AIS targets running concurrently")
                    # Run simulator and AIS targets concurrently
                    await asyncio.gather(
                        simulator.run_simulation(),
                        *ais_tasks
                    )
                else:
                    print("⚠️  No AIS targets found - running simulator only")
                    await simulator.run_simulation()

                # Clean up
                ais_sender.close_communication()
                print("🧹 AIS sender cleaned up")
            else:
                print("⚠️  Failed to extract AIS data - running simulator only")
                await simulator.run_simulation()

        except ImportError as e:
            print(f"⚠️  AIS modules not available: {e}")
            print("    Install required packages: pip install pyais pyarrow")
            print("    Running simulator with marzip initial config only...")
            await simulator.run_simulation()

    except ImportError as e:
        print(f"⚠️  Marzip utilities not available: {e}")
        print("    Install required packages: pip install pyarrow")
        print("    Falling back to normal mode...")
        await run_normal_mode(config, simulator)
    except Exception as e:
        print(f"❌ Error in scenario mode: {e}")
        import traceback
        traceback.print_exc()
        print("🔄 Falling back to normal mode...")
        await run_normal_mode(config, simulator)


async def run_normal_mode(config, simulator):
    """
    Run simulator in NORMAL mode with standard ship simulation.

    Features:
    - Uses configuration file settings for initial conditions
    - Standard NMEA output via multicast
    - RSA rudder control input
    - No AIS targets or scenario data
    """
    print("🎯 RUNNING IN NORMAL MODE")
    print("=" * 50)
    print("📍 Using configuration file initial conditions:")
    print(f"   Position: {simulator.ship_state.lat:.6f}°, {simulator.ship_state.lon:.6f}°")
    print(f"   Speed: {simulator.ship_state.SOG:.1f} knots")
    print(f"   Course: {simulator.ship_state.COG * 180 / 3.14159:.1f}°")
    print()
    print("🚀 Starting standard ship simulation...")
    print("   NMEA data output: Multicast enabled")
    print("   RSA rudder control: Listening for commands")
    print("   Redis NMEA display: Real-time data updates")

    # Run standard simulation
    await simulator.run_simulation()


async def main():
    """Run the ship simulator."""
    print("=" * 60)
    print("RSA-Based Ship Simulator")
    print("=" * 60)

    # Setup multicast routing for Docker environment
    setup_multicast_routing()

    # Create simulator configuration
    config = SimulationConfig()

    # Create simulator with Docker-friendly network settings
    simulator = ShipSimulator(config)

    # Setup communication using configuration file
    simulator.communication = CommunicationManager("config/communication.json")

    print("Simulator Configuration:")
    print(f"  Mode: {config.simulation_mode}")
    print(f"  Position: {simulator.ship_state.lat:.6f}°, {simulator.ship_state.lon:.6f}°")
    print(f"  Speed: {simulator.ship_state.SOG:.1f} knots")
    print(f"  Course: {simulator.ship_state.COG * 180 / 3.14159:.1f}°")
    print()

    print("Communication Setup:")
    print(f"  RSA Receiver: {simulator.communication.server_ip}:{simulator.communication.server_port}")
    print(f"  NMEA Multicast: {simulator.communication.multicast_ip}:{simulator.communication.parser_port}")
    print()

    print("Controls:")
    print("  Send RSA messages to control rudder angle")
    print("  Example: $AGRSA,15.0,A,15.0,V*XX")
    print("  Press Ctrl+C to stop")
    print("-" * 60)

    try:
        # 🔀 SIMULATION MODE BRANCHING
        print("🔀 SIMULATION MODE SELECTION")
        print("=" * 50)

        if config.simulation_mode == "scenario":
            print("✅ Mode: SCENARIO - Marzip-based simulation with AIS targets")
            await run_scenario_mode(config, simulator)
        else:
            print("✅ Mode: NORMAL - Standard ship simulation")
            await run_normal_mode(config, simulator)

    except KeyboardInterrupt:
        print("\n🛑 Simulator stopped by user")
        print("👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Simulator error: {e}")
        import traceback
        traceback.print_exc()
        print("💡 Check configuration files and dependencies")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
