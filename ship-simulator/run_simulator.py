#!/usr/bin/env python3
"""
Simple script to run the RSA-based ship simulator.
"""

import asyncio
import sys
import os
import subprocess


# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def setup_multicast_routing():
    """Setup basic multicast routing for Docker environment."""
    try:
        print("Setting up multicast routing...")

        # 기본 멀티캐스트 라우팅만 설정
        try:
            subprocess.run(['ip', 'route', 'add', '*********/8', 'dev', 'eth0'],
                         capture_output=True, text=True)
            print("✅ Added basic multicast route")
        except Exception as e:
            print(f"⚠️ Could not add multicast route: {e}")

    except Exception as e:
        print(f"⚠️ Could not setup multicast routing: {e}")
        print("Continuing without multicast routing setup...")

from core import ShipSimulator
from core import SimulationConfig
from core.communication_manager import CommunicationManager


async def main():
    """Run the ship simulator."""
    print("=" * 60)
    print("RSA-Based Ship Simulator")
    print("=" * 60)

    # Setup multicast routing for Docker environment
    setup_multicast_routing()
    
    # Create simulator with default position (Busan port area)
    config = SimulationConfig()

    # Create simulator with Docker-friendly network settings
    simulator = ShipSimulator(config)

    # Setup communication using configuration file
    simulator.communication = CommunicationManager("config/communication.json")
    
    print("Simulator Configuration:")
    print(f"  Position: {simulator.ship_state.lat:.6f}°, {simulator.ship_state.lon:.6f}°")
    print(f"  Speed: {simulator.ship_state.SOG:.1f} knots")
    print(f"  Course: {simulator.ship_state.COG * 180 / 3.14159:.1f}°")
    print()
    
    print("Communication Setup:")
    print(f"  RSA Receiver: {simulator.communication.server_ip}:{simulator.communication.server_port}")
    print(f"  NMEA Multicast: {simulator.communication.multicast_ip}:{simulator.communication.parser_port}")
    print()
    
    print("Controls:")
    print("  Send RSA messages to control rudder angle")
    print("  Example: $AGRSA,15.0,A,15.0,V*XX")
    print("  Press Ctrl+C to stop")
    print("-" * 60)
    
    try:
        await simulator.run_simulation()
    except KeyboardInterrupt:
        print("\nSimulator stopped by user")
    except Exception as e:
        print(f"\nSimulator error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
