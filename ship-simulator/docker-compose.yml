services:
  ship-simulator:
    image: "ship-simulator:dev" #//#: dev"
    container_name: ship-simulator
    network_mode: host
    privileged: true
    cap_add:
      - NET_ADMIN
      - NET_RAW
    environment:
      - REDIS_HOST=localhost
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_SSL=false
      - REDIS_AUTO_CONN=true
      - REDIS_AUTO_UTC_SET=false
    volumes:
      - /home/<USER>/workspace/hinas-control-mock-simulator/ship-simulator:/app
    environment:
      - PYTHONPATH=/app
    command: sleep infinity
