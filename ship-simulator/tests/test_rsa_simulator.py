#!/usr/bin/env python3
"""
Test script for RSA-based ship simulator.

This script demonstrates the new RSA-only communication system:
- Receives RSA commands on ***********:4001
- Sends NMEA data via multicast to *********:6501
- Runs async simulation loop
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core import ShipSimulator


async def main():
    """Main test function."""
    print("=" * 60)
    print("RSA-Based Ship Simulator Test")
    print("=" * 60)
    
    # Create simulator with initial position (Busan port area)
    simulator = ShipSimulator(
        lat=35.1796,    # Busan latitude
        lon=129.0756,   # Busan longitude  
        speed=10.0,     # 10 knots
        course=90.0     # East
    )
    
    print(f"Simulator initialized:")
    print(f"  Initial position: {simulator.ship_state.lat:.6f}°, {simulator.ship_state.lon:.6f}°")
    print(f"  Initial speed: {simulator.ship_state.SOG:.1f} knots")
    print(f"  Initial course: {simulator.ship_state.COG * 180 / 3.14159:.1f}°")
    print()
    
    print("Communication setup:")
    print(f"  RSA receiver: {simulator.communication.server_ip}:{simulator.communication.server_port}")
    print(f"  NMEA multicast: {simulator.communication.multicast_ip}:{simulator.communication.parser_port}")
    print()
    
    print("Starting simulation...")
    print("Send RSA messages to control rudder angle")
    print("Example RSA message: $AGRSA,15.0,A,15.0,V*XX")
    print("Press Ctrl+C to stop")
    print("-" * 60)
    
    try:
        # Run the async simulation
        await simulator.run_simulation()
        
    except KeyboardInterrupt:
        print("\nSimulation stopped by user")
    except Exception as e:
        print(f"\nSimulation error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("Test completed")


def test_rsa_parsing():
    """Test RSA message parsing."""
    print("Testing RSA message parsing...")
    
    from core.communication_manager import CommunicationManager
    
    comm = CommunicationManager()
    
    # Test valid RSA messages
    test_messages = [
        "$AGRSA,15.0,A,15.0,V*XX",
        "$IIRSA,-10.5,A,-10.5,V*XX", 
        "$HCRSA,0.0,A,0.0,V*XX",
        "$INVALID,15.0,A,15.0,V*XX",  # Invalid message type
        "$AGRSA,,A,,V*XX",            # Empty rudder field
        "CORRUPTED MESSAGE",          # Corrupted message
    ]
    
    for msg in test_messages:
        result = comm._parse_rsa_message(msg)
        print(f"  '{msg}' -> {result}")
    
    print("RSA parsing test completed\n")


if __name__ == "__main__":
    print("RSA Ship Simulator Test Suite")
    print("=" * 60)
    
    # Test RSA parsing first
    test_rsa_parsing()
    
    # Run main simulation
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed: {e}")
        import traceback
        traceback.print_exc()
