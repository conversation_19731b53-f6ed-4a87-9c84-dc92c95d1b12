#!/usr/bin/env python3
"""
Test safe environment parameter mapping for wind and current data.
"""

import sys
import os

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class MockShipSimulator:
    """Mock ship simulator for testing safe env param mapping."""
    
    def __init__(self, env_param):
        self.env_param = env_param
    
    def _safe_get_env_param(self, param_path: str, default=None):
        """
        Safely extract nested parameters from env_param.
        
        Args:
            param_path: Dot-separated path like "wind.direction" or "current.speed"
            default: Default value if path not found
            
        Returns:
            Value at the path or default
        """
        try:
            if not self.env_param or not isinstance(self.env_param, dict):
                return default
                
            keys = param_path.split('.')
            current = self.env_param
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default
                    
            return current
            
        except Exception as e:
            print(f"Warning: Error accessing env_param path '{param_path}': {e}")
            return default


def test_safe_mapping():
    """Test safe environment parameter mapping."""
    print("🧪 TESTING SAFE ENV PARAM MAPPING")
    print("="*50)
    
    # Test Case 1: Complete data
    print("\n1️⃣  Test Case: Complete Data")
    complete_env = {
        "wind": {
            "direction": 270.0,
            "speed": 15.5
        },
        "current": {
            "direction": 180.0,
            "speed": 2.3
        }
    }
    
    simulator = MockShipSimulator(complete_env)
    
    wind_dir = simulator._safe_get_env_param("wind.direction", None)
    wind_speed = simulator._safe_get_env_param("wind.speed", None)
    current_dir = simulator._safe_get_env_param("current.direction", None)
    current_speed = simulator._safe_get_env_param("current.speed", None)
    
    print(f"Wind: {wind_dir}° @ {wind_speed} m/s")
    print(f"Current: {current_dir}° @ {current_speed} m/s")
    print("✅ Complete data extraction successful")
    
    # Test Case 2: Partial data
    print("\n2️⃣  Test Case: Partial Data")
    partial_env = {
        "wind": {
            "direction": 90.0
            # speed missing
        },
        "current": {
            "speed": 1.8
            # direction missing
        }
    }
    
    simulator = MockShipSimulator(partial_env)
    
    wind_dir = simulator._safe_get_env_param("wind.direction", None)
    wind_speed = simulator._safe_get_env_param("wind.speed", None)
    current_dir = simulator._safe_get_env_param("current.direction", None)
    current_speed = simulator._safe_get_env_param("current.speed", None)
    
    print(f"Wind: {wind_dir}° @ {wind_speed} m/s")
    print(f"Current: {current_dir}° @ {current_speed} m/s")
    print("✅ Partial data handled safely with nulls")
    
    # Test Case 3: Empty env_param
    print("\n3️⃣  Test Case: Empty env_param")
    simulator = MockShipSimulator({})
    
    wind_dir = simulator._safe_get_env_param("wind.direction", None)
    wind_speed = simulator._safe_get_env_param("wind.speed", None)
    current_dir = simulator._safe_get_env_param("current.direction", None)
    current_speed = simulator._safe_get_env_param("current.speed", None)
    
    print(f"Wind: {wind_dir}° @ {wind_speed} m/s")
    print(f"Current: {current_dir}° @ {current_speed} m/s")
    print("✅ Empty env_param handled safely")
    
    # Test Case 4: None env_param
    print("\n4️⃣  Test Case: None env_param")
    simulator = MockShipSimulator(None)
    
    wind_dir = simulator._safe_get_env_param("wind.direction", None)
    wind_speed = simulator._safe_get_env_param("wind.speed", None)
    current_dir = simulator._safe_get_env_param("current.direction", None)
    current_speed = simulator._safe_get_env_param("current.speed", None)
    
    print(f"Wind: {wind_dir}° @ {wind_speed} m/s")
    print(f"Current: {current_dir}° @ {current_speed} m/s")
    print("✅ None env_param handled safely")
    
    # Test Case 5: Invalid structure
    print("\n5️⃣  Test Case: Invalid Structure")
    invalid_env = {
        "wind": "not_a_dict",
        "current": 123
    }
    
    simulator = MockShipSimulator(invalid_env)
    
    wind_dir = simulator._safe_get_env_param("wind.direction", None)
    wind_speed = simulator._safe_get_env_param("wind.speed", None)
    current_dir = simulator._safe_get_env_param("current.direction", None)
    current_speed = simulator._safe_get_env_param("current.speed", None)
    
    print(f"Wind: {wind_dir}° @ {wind_speed} m/s")
    print(f"Current: {current_dir}° @ {current_speed} m/s")
    print("✅ Invalid structure handled safely")
    
    # Test Case 6: Deep nesting
    print("\n6️⃣  Test Case: Deep Nesting")
    deep_env = {
        "environment": {
            "wind": {
                "surface": {
                    "direction": 45.0,
                    "speed": 12.0
                }
            }
        }
    }
    
    simulator = MockShipSimulator(deep_env)
    
    deep_wind_dir = simulator._safe_get_env_param("environment.wind.surface.direction", None)
    deep_wind_speed = simulator._safe_get_env_param("environment.wind.surface.speed", None)
    
    print(f"Deep Wind: {deep_wind_dir}° @ {deep_wind_speed} m/s")
    print("✅ Deep nesting handled correctly")


def test_nmea_display_integration():
    """Test integration with NMEA display data structure."""
    print("\n🔗 TESTING NMEA DISPLAY INTEGRATION")
    print("="*50)
    
    # Test with realistic env_param
    realistic_env = {
        "wind": {
            "direction": 315.0,  # NW wind
            "speed": 8.2        # m/s
        },
        "current": {
            "direction": 90.0,   # East current
            "speed": 1.5        # m/s
        },
        "wave": {
            "height": 2.1,
            "period": 8.5
        }
    }
    
    simulator = MockShipSimulator(realistic_env)
    
    # Extract data as would be done in _set_nmea_display_data
    wind_direction = simulator._safe_get_env_param("wind.direction", None)
    wind_speed = simulator._safe_get_env_param("wind.speed", None)
    current_direction = simulator._safe_get_env_param("current.direction", None)
    current_speed = simulator._safe_get_env_param("current.speed", None)
    
    # Build partial NMEA structure
    nmea_partial = {
        "WindDirection": wind_direction,
        "WindSpeed": wind_speed,
        "CurrentDirection": current_direction,
        "CurrentSpeed": current_speed
    }
    
    print("📊 NMEA Display Data (Wind/Current):")
    for key, value in nmea_partial.items():
        print(f"  {key}: {value}")
    
    print("✅ NMEA display integration successful")


if __name__ == "__main__":
    print("🧪 SAFE ENV PARAM MAPPING TESTING")
    print("="*50)
    
    test_safe_mapping()
    test_nmea_display_integration()
    
    print("\n" + "="*50)
    print("🏁 TESTING COMPLETE")
    print("✅ Safe parameter extraction implemented")
    print("✅ All edge cases handled gracefully")
    print("✅ No exceptions thrown for invalid data")
    print("✅ Null values returned for missing data")
    print("✅ Deep nesting supported")
    print("✅ NMEA display integration ready")
    print("\n💡 Safe mapping ready for production use!")
