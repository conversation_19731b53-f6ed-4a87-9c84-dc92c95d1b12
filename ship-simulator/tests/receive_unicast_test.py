#!/usr/bin/env python3
"""
Unicast NMEA Data Receiver Test

This script receives NMEA data from the ship simulator's unicast transmission.
The simulator sends NMEA data to ***********:60001.
"""

import socket
import time
import sys
from datetime import datetime


def receive_nmea_unicast(local_ip: str = "***********", port: int = 60001):
    """
    Receive NMEA data from unicast transmission.
    """
    print("=" * 60)
    print("NMEA Unicast Receiver")
    print("=" * 60)
    print(f"Listening on: {local_ip}:{port}")
    print("Press Ctrl+C to stop")
    print("-" * 60)
    
    try:
        # Create UDP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        # Bind to specific IP and port
        try:
            sock.bind((local_ip, port))
            print(f"✅ Socket bound to {local_ip}:{port}")
        except Exception as e:
            print(f"❌ Failed to bind to {local_ip}:{port}: {e}")
            # Try binding to all interfaces
            sock.bind(('', port))
            print(f"✅ Socket bound to all interfaces on port {port}")
        
        sock.settimeout(2.0)
        
        message_count = 0
        last_message_time = time.time()
        
        print("🔍 Waiting for NMEA data...")
        
        while True:
            try:
                # Receive data
                data, addr = sock.recvfrom(1024)
                current_time = time.time()
                
                if data:
                    message_count += 1
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    
                    # Decode message
                    message = data.decode('utf-8', errors='ignore').strip()
                    
                    # Calculate time since last message
                    time_diff = current_time - last_message_time
                    last_message_time = current_time
                    
                    print(f"[{timestamp}] From {addr[0]}:{addr[1]} (+{time_diff:.2f}s)")
                    print(f"  📡 Message #{message_count}: {message}")
                    
                    # Parse NMEA sentence type
                    if message.startswith('$'):
                        parts = message.split(',')
                        if len(parts) > 0:
                            sentence_type = parts[0][1:]  # Remove $
                            print(f"  🔍 NMEA Type: {sentence_type}")
                    
                    print()
                    
            except socket.timeout:
                # No data received within timeout period
                print("⏰ No data received (timeout)")
                continue
                
            except KeyboardInterrupt:
                print("\n🛑 Stopping receiver...")
                break
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
        
    finally:
        try:
            sock.close()
            print("✅ Socket closed")
        except:
            pass
    
    return True


def test_socket_binding():
    """Test socket binding capabilities."""
    print("Testing socket binding...")
    
    test_configs = [
        ("***********", 60001),  # Specific IP
        ("0.0.0.0", 60001),      # All interfaces
        ("", 60001),             # Default binding
    ]
    
    for ip, port in test_configs:
        print(f"\nTesting bind to: {ip or 'default'}:{port}")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind((ip, port))
            print(f"✅ Successfully bound to {ip or 'default'}:{port}")
            sock.close()
        except Exception as e:
            print(f"❌ Failed to bind: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_socket_binding()
    else:
        # Default: receive NMEA data
        receive_nmea_unicast()
