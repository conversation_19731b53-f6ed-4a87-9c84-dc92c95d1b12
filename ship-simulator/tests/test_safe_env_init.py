#!/usr/bin/env python3
"""
Test safe environment parameter initialization.
"""

import sys
import os

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class MockConfig:
    """Mock configuration for testing."""
    
    def __init__(self, wind_enabled=True, current_enabled=True):
        self.wind_enabled = wind_enabled
        self.current_enabled = current_enabled
    
    def is_wind_enabled(self):
        return self.wind_enabled
    
    def is_current_enabled(self):
        return self.current_enabled


class MockShipSimulator:
    """Mock ship simulator for testing safe env param initialization."""
    
    def __init__(self, config, env_param):
        self.config = config
        self.env_param = env_param
    
    def _safe_init_env_params(self):
        """
        Safely initialize wind and current parameters based on config settings.
        Creates the nested structure if it doesn't exist.
        """
        try:
            # Ensure env_param is a dictionary
            if not isinstance(self.env_param, dict):
                self.env_param = {}
            
            # Initialize wind parameters
            if not self.config.is_wind_enabled():
                # Ensure wind dict exists
                if 'wind' not in self.env_param:
                    self.env_param['wind'] = {}
                elif not isinstance(self.env_param['wind'], dict):
                    self.env_param['wind'] = {}
                
                # Set wind to zero
                self.env_param['wind']['speed'] = 0.0
                self.env_param['wind']['direction'] = 0.0
                print("🌬️  Wind disabled - set to zero")
            else:
                # Ensure wind structure exists even if enabled
                if 'wind' not in self.env_param:
                    self.env_param['wind'] = {'speed': 0.0, 'direction': 0.0}
                elif not isinstance(self.env_param['wind'], dict):
                    self.env_param['wind'] = {'speed': 0.0, 'direction': 0.0}
                print("🌬️  Wind enabled")
            
            # Initialize current parameters
            if not self.config.is_current_enabled():
                # Ensure current dict exists
                if 'current' not in self.env_param:
                    self.env_param['current'] = {}
                elif not isinstance(self.env_param['current'], dict):
                    self.env_param['current'] = {}
                
                # Set current to zero
                self.env_param['current']['speed'] = 0.0
                self.env_param['current']['direction'] = 0.0
                print("🌊 Current disabled - set to zero")
            else:
                # Ensure current structure exists even if enabled
                if 'current' not in self.env_param:
                    self.env_param['current'] = {'speed': 0.0, 'direction': 0.0}
                elif not isinstance(self.env_param['current'], dict):
                    self.env_param['current'] = {'speed': 0.0, 'direction': 0.0}
                print("🌊 Current enabled")
                
        except Exception as e:
            print(f"⚠️  Error initializing env_param: {e}")
            # Fallback: create minimal safe structure
            self.env_param = {
                'wind': {'speed': 0.0, 'direction': 0.0},
                'current': {'speed': 0.0, 'direction': 0.0}
            }
            print("🔧 Created fallback env_param structure")


def test_safe_initialization():
    """Test safe environment parameter initialization."""
    print("🧪 TESTING SAFE ENV PARAM INITIALIZATION")
    print("="*50)
    
    # Test Case 1: Normal case with both enabled
    print("\n1️⃣  Test Case: Both Wind and Current Enabled")
    config = MockConfig(wind_enabled=True, current_enabled=True)
    env_param = {
        'wind': {'speed': 15.0, 'direction': 270.0},
        'current': {'speed': 2.5, 'direction': 180.0}
    }
    
    simulator = MockShipSimulator(config, env_param)
    simulator._safe_init_env_params()
    
    print(f"Wind: {simulator.env_param['wind']}")
    print(f"Current: {simulator.env_param['current']}")
    print("✅ Normal case handled correctly")
    
    # Test Case 2: Wind disabled
    print("\n2️⃣  Test Case: Wind Disabled")
    config = MockConfig(wind_enabled=False, current_enabled=True)
    env_param = {
        'wind': {'speed': 15.0, 'direction': 270.0},
        'current': {'speed': 2.5, 'direction': 180.0}
    }
    
    simulator = MockShipSimulator(config, env_param)
    simulator._safe_init_env_params()
    
    print(f"Wind: {simulator.env_param['wind']}")
    print(f"Current: {simulator.env_param['current']}")
    print("✅ Wind disabled correctly")
    
    # Test Case 3: Current disabled
    print("\n3️⃣  Test Case: Current Disabled")
    config = MockConfig(wind_enabled=True, current_enabled=False)
    env_param = {
        'wind': {'speed': 15.0, 'direction': 270.0},
        'current': {'speed': 2.5, 'direction': 180.0}
    }
    
    simulator = MockShipSimulator(config, env_param)
    simulator._safe_init_env_params()
    
    print(f"Wind: {simulator.env_param['wind']}")
    print(f"Current: {simulator.env_param['current']}")
    print("✅ Current disabled correctly")
    
    # Test Case 4: Empty env_param
    print("\n4️⃣  Test Case: Empty env_param")
    config = MockConfig(wind_enabled=True, current_enabled=True)
    env_param = {}
    
    simulator = MockShipSimulator(config, env_param)
    simulator._safe_init_env_params()
    
    print(f"Wind: {simulator.env_param['wind']}")
    print(f"Current: {simulator.env_param['current']}")
    print("✅ Empty env_param handled safely")
    
    # Test Case 5: None env_param
    print("\n5️⃣  Test Case: None env_param")
    config = MockConfig(wind_enabled=False, current_enabled=False)
    env_param = None
    
    simulator = MockShipSimulator(config, env_param)
    simulator._safe_init_env_params()
    
    print(f"Wind: {simulator.env_param['wind']}")
    print(f"Current: {simulator.env_param['current']}")
    print("✅ None env_param handled safely")
    
    # Test Case 6: Invalid structure
    print("\n6️⃣  Test Case: Invalid Structure")
    config = MockConfig(wind_enabled=True, current_enabled=True)
    env_param = {
        'wind': 'not_a_dict',
        'current': 123
    }
    
    simulator = MockShipSimulator(config, env_param)
    simulator._safe_init_env_params()
    
    print(f"Wind: {simulator.env_param['wind']}")
    print(f"Current: {simulator.env_param['current']}")
    print("✅ Invalid structure corrected safely")
    
    # Test Case 7: Missing keys
    print("\n7️⃣  Test Case: Missing Keys")
    config = MockConfig(wind_enabled=False, current_enabled=False)
    env_param = {
        'other_param': 'some_value'
    }
    
    simulator = MockShipSimulator(config, env_param)
    simulator._safe_init_env_params()
    
    print(f"Wind: {simulator.env_param['wind']}")
    print(f"Current: {simulator.env_param['current']}")
    print(f"Other: {simulator.env_param.get('other_param', 'missing')}")
    print("✅ Missing keys added safely")


def test_before_after_comparison():
    """Compare before and after the dangerous code."""
    print("\n🔥 BEFORE/AFTER COMPARISON")
    print("="*50)
    
    print("❌ DANGEROUS CODE (would crash):")
    print("   if not self.config.is_wind_enabled():")
    print("       self.env_param.wind.speed = 0.0      # AttributeError!")
    print("       self.env_param.wind.direction = 0.0  # AttributeError!")
    print()
    
    print("✅ SAFE CODE (never crashes):")
    print("   self._safe_init_env_params()  # Always works!")
    print()
    
    # Demonstrate the fix
    config = MockConfig(wind_enabled=False, current_enabled=False)
    
    # This would crash with the old method
    print("🧪 Testing with problematic env_param:")
    problematic_env = None
    
    simulator = MockShipSimulator(config, problematic_env)
    print("Before init:", simulator.env_param)
    
    simulator._safe_init_env_params()
    print("After safe init:", simulator.env_param)
    print("✅ No crash! Safe initialization completed.")


if __name__ == "__main__":
    print("🧪 SAFE ENV PARAM INITIALIZATION TESTING")
    print("="*50)
    
    test_safe_initialization()
    test_before_after_comparison()
    
    print("\n" + "="*50)
    print("🏁 TESTING COMPLETE")
    print("✅ Safe initialization implemented")
    print("✅ All edge cases handled gracefully")
    print("✅ No AttributeError exceptions")
    print("✅ Proper structure creation")
    print("✅ Config-based enable/disable working")
    print("✅ Fallback mechanism in place")
    print("\n💡 Safe env_param initialization ready!")
