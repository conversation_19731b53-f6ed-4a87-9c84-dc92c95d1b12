#!/usr/bin/env python3
"""
Test simulation mode branching and marzip_reader functionality.
"""

import sys
import os
import json

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_marzip_reader_import():
    """Test marzip_reader import functionality."""
    print("🧪 TESTING MARZIP_READER IMPORT")
    print("=" * 50)
    
    try:
        from utils.marzip_reader import MarzipConfigExtractor, extract_marzip_for_ais
        print("✅ marzip_reader import successful")
        print("   - MarzipConfigExtractor: Available")
        print("   - extract_marzip_for_ais: Available")
        return True
    except ImportError as e:
        print(f"❌ marzip_reader import failed: {e}")
        return False


def test_simulation_config_modes():
    """Test simulation configuration mode detection."""
    print("\n🧪 TESTING SIMULATION CONFIG MODES")
    print("=" * 50)
    
    try:
        from core import SimulationConfig
        
        # Test normal mode (default)
        config = SimulationConfig()
        print(f"✅ Default simulation mode: {config.simulation_mode}")
        
        # Test scenario mode by temporarily modifying config
        config_file = "config/simulation_config.json"
        
        # Read current config
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        
        original_mode = config_data["simulation_set"]["simulation_mode"]
        print(f"📄 Config file mode: {original_mode}")
        
        # Test both modes
        modes_to_test = ["normal", "scenario"]
        
        for mode in modes_to_test:
            # Temporarily modify config
            config_data["simulation_set"]["simulation_mode"] = mode
            
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            # Create new config instance
            test_config = SimulationConfig()
            print(f"✅ Mode '{mode}': {test_config.simulation_mode}")
            
            # Verify mode detection
            if test_config.simulation_mode == mode:
                print(f"   ✓ Mode detection working correctly")
            else:
                print(f"   ❌ Mode detection failed: expected {mode}, got {test_config.simulation_mode}")
        
        # Restore original config
        config_data["simulation_set"]["simulation_mode"] = original_mode
        with open(config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"🔄 Restored original mode: {original_mode}")
        return True
        
    except Exception as e:
        print(f"❌ Simulation config test failed: {e}")
        return False


def test_mode_branching_logic():
    """Test the branching logic for different simulation modes."""
    print("\n🧪 TESTING MODE BRANCHING LOGIC")
    print("=" * 50)
    
    try:
        from core import SimulationConfig
        
        # Test normal mode branching
        config = SimulationConfig()
        
        print("🔀 SIMULATION MODE BRANCHING TEST")
        print(f"Current mode: {config.simulation_mode}")
        
        if config.simulation_mode == "scenario":
            print("✅ Would execute: run_scenario_mode()")
            print("   - Load marzip file")
            print("   - Extract initial conditions")
            print("   - Setup AIS targets")
            print("   - Run concurrent simulation")
        else:
            print("✅ Would execute: run_normal_mode()")
            print("   - Use config file initial conditions")
            print("   - Standard NMEA output")
            print("   - RSA rudder control")
            print("   - No AIS targets")
        
        return True
        
    except Exception as e:
        print(f"❌ Mode branching test failed: {e}")
        return False


def test_ais_creator_imports():
    """Test ais_creator module imports."""
    print("\n🧪 TESTING AIS_CREATOR IMPORTS")
    print("=" * 50)
    
    try:
        from ais_creator.marzip_processor import MarzipParser
        print("✅ ais_creator.marzip_processor.MarzipParser: Available")
        
        try:
            from ais_creator.marzip_aivdm_sender import MarzipAIS_Sender
            print("✅ ais_creator.marzip_aivdm_sender.MarzipAIS_Sender: Available")
        except ImportError as e:
            print(f"⚠️  MarzipAIS_Sender import failed: {e}")
            print("   This is expected if pyais is not installed")
        
        return True
        
    except ImportError as e:
        print(f"❌ AIS creator imports failed: {e}")
        return False


def test_file_structure():
    """Test the reorganized file structure."""
    print("\n🧪 TESTING FILE STRUCTURE")
    print("=" * 50)
    
    # Check utils directory
    utils_files = [
        "utils/marzip_reader.py",  # New name
        "utils/redis_manager.py",
        "utils/coordinate_formatter.py",
        "utils/nmea_config.py"
    ]
    
    for file_path in utils_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: Exists")
        else:
            print(f"❌ {file_path}: Missing")
    
    # Check ais_creator directory
    ais_files = [
        "ais_creator/marzip_processor.py",  # AIS processing logic
        "ais_creator/marzip_aivdm_sender.py",
        "ais_creator/aivdm_message.py"
    ]
    
    for file_path in ais_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: Exists")
        else:
            print(f"❌ {file_path}: Missing")
    
    # Check that old file is gone
    if not os.path.exists("utils/marzip_utils.py"):
        print("✅ utils/marzip_utils.py: Properly removed")
    else:
        print("❌ utils/marzip_utils.py: Still exists (should be removed)")
    
    return True


if __name__ == "__main__":
    print("🧪 SIMULATION MODE BRANCHING TESTING")
    print("=" * 60)
    
    tests = [
        test_marzip_reader_import,
        test_simulation_config_modes,
        test_mode_branching_logic,
        test_ais_creator_imports,
        test_file_structure
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("🏁 TESTING SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ marzip_utils → marzip_reader rename complete")
        print("✅ File structure properly organized")
        print("✅ Import references updated")
        print("✅ Simulation mode branching working")
        print("✅ Ready for scenario/normal mode execution")
    else:
        print("⚠️  Some tests failed - check output above")
    
    print("\n💡 Usage:")
    print("   Normal mode:   simulation_mode = 'normal'")
    print("   Scenario mode: simulation_mode = 'scenario'")
    print("   Config file:   config/simulation_config.json")
