"""
Unit tests for ship models.
"""

import unittest
import numpy as np
import tempfile
import os
from ship_models import ShipConfigLoader, ShipModel62065, DynamicWaveDisturbance


class TestShipConfigLoader(unittest.TestCase):
    """Test cases for ShipConfigLoader."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.loader = ShipConfigLoader()
    
    def test_load_ship_config(self):
        """Test loading ship configuration."""
        for ship_type in [1, 2, 3]:
            with self.subTest(ship_type=ship_type):
                config = self.loader.load_ship_config(ship_type)
                self.assertIsInstance(config, dict)
                self.assertEqual(config["ship_type"], ship_type)
                self.assertIn("name", config)
                self.assertIn("physical_parameters", config)
                self.assertIn("dynamics_parameters", config)
                self.assertIn("control_parameters", config)
    
    def test_get_ship_parameters(self):
        """Test getting ship parameters."""
        for ship_type in [1, 2, 3]:
            with self.subTest(ship_type=ship_type):
                ship_param = self.loader.get_ship_parameters(ship_type)
                self.assertIsInstance(ship_param, dict)
                
                # Check required parameters
                required_params = ["Lpp", "Mass", "U_max", "wind_coefficient"]
                for param in required_params:
                    self.assertIn(param, ship_param)
                
                # Check wind coefficient format
                wind_coeff = ship_param["wind_coefficient"]
                self.assertIsInstance(wind_coeff, np.ndarray)
                self.assertEqual(wind_coeff.shape[1], 4)  # [angle, Cx, Cy, Cn]
    
    def test_validate_wind_coefficients(self):
        """Test wind coefficient validation."""
        # Valid coefficients
        valid_coeff = np.array([
            [0, 0.8, 0.0, 0.0],
            [90, 0.0, 0.8, 0.4],
            [180, -0.8, 0.0, 0.0]
        ])
        self.assertTrue(self.loader.validate_wind_coefficients(valid_coeff))
        
        # Invalid shape
        invalid_coeff = np.array([[0, 0.8, 0.0]])  # Missing Cn column
        self.assertFalse(self.loader.validate_wind_coefficients(invalid_coeff))
    
    def test_invalid_ship_type(self):
        """Test handling of invalid ship type."""
        with self.assertRaises(ValueError):
            self.loader.load_ship_config(99)


class TestShipModel62065(unittest.TestCase):
    """Test cases for ShipModel62065."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.model = ShipModel62065()
        self.loader = ShipConfigLoader()
        
        # Create test ship state
        self.ship_state = {
            'u': 5.0, 'v': 0.0, 'r': 0.0,
            'x': 0.0, 'y': 0.0, 'psi': 0.0,
            'uw': 5.0, 'vw': 0.0, 'COG': 0.0,
            'del': 0.0
        }
        
        # Get ship parameters
        self.ship_param = self.loader.get_ship_parameters(2)  # Container ship
        
        # Create test command and environment
        self.command = {'rudder': 5.0}
        self.env_param = {
            'lever': 0.5, 'lever_cmd': 0.5,
            'TN': 0.0, 'TE': 0.0,
            'wind_dir': 0.0, 'wind_speed': 0.0
        }
    
    def test_calculate_dynamics(self):
        """Test ship dynamics calculation."""
        updated_state = self.model.calculate_dynamics(
            self.ship_state, self.ship_param, self.command, 
            self.env_param, 0.1, 0.0
        )
        
        self.assertIsInstance(updated_state, dict)
        
        # Check that state was updated
        required_keys = ['u', 'v', 'r', 'x', 'y', 'psi', 'SOG']
        for key in required_keys:
            self.assertIn(key, updated_state)
        
        # Check that values are reasonable
        self.assertGreaterEqual(updated_state['SOG'], 0)  # Speed should be non-negative
    
    def test_model_info(self):
        """Test model information."""
        info = self.model.get_model_info()
        self.assertIsInstance(info, dict)
        self.assertIn('name', info)
        self.assertIn('version', info)
        self.assertIn('features', info)


class TestDynamicWaveDisturbance(unittest.TestCase):
    """Test cases for DynamicWaveDisturbance."""
    
    def test_initialization(self):
        """Test wave disturbance initialization."""
        for sea_state in [2, 5]:
            with self.subTest(sea_state=sea_state):
                wave = DynamicWaveDisturbance(sea_state)
                self.assertEqual(wave.sea_state, sea_state)
                self.assertEqual(wave.current_t_end, 0.0)
                self.assertEqual(wave.current_Wp, 0.0)
    
    def test_invalid_sea_state(self):
        """Test handling of invalid sea state."""
        with self.assertRaises(ValueError):
            DynamicWaveDisturbance(99)
    
    def test_get_wb(self):
        """Test wave disturbance value generation."""
        wave = DynamicWaveDisturbance(2)
        
        # Test multiple time points
        for t in [0.0, 1.0, 5.0, 10.0]:
            wb = wave.get_Wb(t)
            self.assertIsInstance(wb, float)
    
    def test_supported_sea_states(self):
        """Test supported sea states."""
        supported = DynamicWaveDisturbance.get_supported_sea_states()
        self.assertEqual(supported, {2, 5})
    
    def test_sea_state_info(self):
        """Test sea state information."""
        wave = DynamicWaveDisturbance(2)
        info = wave.get_sea_state_info()
        self.assertIsInstance(info, dict)
        self.assertIn('name', info)
        self.assertIn('description', info)
    
    def test_reset(self):
        """Test wave disturbance reset."""
        wave = DynamicWaveDisturbance(2)
        
        # Generate some values
        wave.get_Wb(5.0)
        self.assertGreater(wave.current_t_end, 0)
        
        # Reset
        wave.reset()
        self.assertEqual(wave.current_t_end, 0.0)
        self.assertEqual(wave.current_Wp, 0.0)
    
    def test_scale_factor(self):
        """Test scale factor setting."""
        wave = DynamicWaveDisturbance(2)
        
        # Test valid scale factor
        wave.set_scale_factor(0.05)
        self.assertEqual(wave.scale_factor, 0.05)
        
        # Test invalid scale factor
        with self.assertRaises(ValueError):
            wave.set_scale_factor(-0.1)


class TestIntegration(unittest.TestCase):
    """Integration tests combining multiple components."""
    
    def test_ship_model_with_wave_disturbance(self):
        """Test ship model with wave disturbance."""
        # Setup components
        model = ShipModel62065()
        loader = ShipConfigLoader()
        wave = DynamicWaveDisturbance(2)
        
        # Create test data
        ship_state = {
            'u': 5.0, 'v': 0.0, 'r': 0.0,
            'x': 0.0, 'y': 0.0, 'psi': 0.0,
            'uw': 5.0, 'vw': 0.0, 'COG': 0.0,
            'del': 0.0
        }
        
        ship_param = loader.get_ship_parameters(2)
        command = {'rudder': 0.0}
        env_param = {
            'lever': 0.5, 'lever_cmd': 0.5,
            'TN': 0.0, 'TE': 0.0,
            'wind_dir': 0.0, 'wind_speed': 0.0
        }
        
        # Run simulation step with wave disturbance
        # Note: Current model doesn't use wave parameter, but test structure
        updated_state = model.calculate_dynamics(
            ship_state, ship_param, command, env_param, 0.1, 0.0
        )
        
        self.assertIsInstance(updated_state, dict)
        self.assertIn('SOG', updated_state)


if __name__ == '__main__':
    unittest.main()
