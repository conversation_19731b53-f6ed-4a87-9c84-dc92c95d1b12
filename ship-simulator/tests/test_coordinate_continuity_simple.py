#!/usr/bin/env python3
"""
Coordinate continuity test - validates smooth local coordinate transitions.
"""

import math
import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from core.coordinate_transformer import CoordinateTransformer


def test_coordinate_continuity():
    """Test that local coordinates change smoothly without unexpected jumps."""
    print("🧪 COORDINATE CONTINUITY TEST - 1000KM CHALLENGE")
    print("="*60)

    transformer = CoordinateTransformer(1.0, 100.0, "ENU")
    transformer.min_update_interval = 0.0
    transformer.update_threshold = 25000.0  # 25km

    # Test: 1000km eastward movement in 5km steps
    step_size = 5000  # 5km per step
    total_steps = 200  # 200 steps × 5km = 1000km

    positions = []
    coordinate_jumps = []  # Track unexpected coordinate jumps

    current_x = 0.0
    current_y = 0.0

    print("Testing 1000km eastward movement in 5km steps")
    print("Step | Local (km)     | Coord Jump (m) | Expected Jump (m) | Status")
    print("-" * 70)

    for step in range(total_steps):
        # Move eastward by 1km
        current_x += step_size

        # Convert to global and back through coordinate system
        current_lat, current_lon = transformer.local_to_global(current_x, current_y)
        new_x, new_y, updated = transformer.check_and_update_reference(
            current_lat, current_lon, current_x, current_y, step * 0.1
        )

        positions.append((new_x, new_y))

        # Calculate coordinate jump (difference from expected smooth progression)
        expected_x = step_size * (step + 1)  # Expected smooth progression
        expected_y = 0.0

        coordinate_jump = math.sqrt((new_x - expected_x)**2 + (new_y - expected_y)**2)
        coordinate_jumps.append(coordinate_jump)

        # Calculate step-to-step change
        step_change = 0.0
        if step > 0:
            prev_x, prev_y = positions[step-1]
            step_change = math.sqrt((new_x - prev_x)**2 + (new_y - prev_y)**2)

        # Status: coordinate should be smooth (close to expected position)
        is_smooth = coordinate_jump < 100  # 100m tolerance for numerical precision
        status = "✅SMOOTH" if is_smooth else "🔴JUMP!"

        # Print every 20 steps or when there's an issue
        if step % 20 == 0 or not is_smooth or updated:
            distance_km = (step + 1) * step_size / 1000
            print(f"{step:4d} | ({new_x/1000:6.1f}, {new_y/1000:6.1f}) | "
                  f"{coordinate_jump:10.1f} | {step_size:13.1f} | {status} | {distance_km:4.0f}km")

    # Analysis
    max_coord_jump = max(coordinate_jumps) if coordinate_jumps else 0
    avg_coord_jump = sum(coordinate_jumps) / len(coordinate_jumps) if coordinate_jumps else 0
    large_jumps = [j for j in coordinate_jumps if j > 100]

    print(f"\n📊 1000KM CONTINUITY ANALYSIS")
    print(f"Total distance traveled: {total_steps * step_size / 1000:.0f}km")
    print(f"Max coordinate deviation: {max_coord_jump:.1f}m")
    print(f"Avg coordinate deviation: {avg_coord_jump:.1f}m")
    print(f"Large deviations (>100m): {len(large_jumps)}")
    print(f"Final position: ({positions[-1][0]/1000:.1f}km, {positions[-1][1]/1000:.1f}km)")

    if len(large_jumps) > 0:
        print(f"❌ 1000KM TEST FAILED - {len(large_jumps)} large coordinate jumps")
        return False
    else:
        print(f"✅ 1000KM TEST PASSED - Perfect continuity over extreme distance!")
        return True


def test_physics_simulation_stability():
    """Test coordinate stability during typical ship physics simulation."""
    print("\n⚓ PHYSICS SIMULATION STABILITY TEST")
    print("="*50)

    transformer = CoordinateTransformer(35.0, 139.0, "ENU")  # Tokyo Bay
    transformer.min_update_interval = 0.0
    transformer.update_threshold = 30000.0  # 30km

    # Simulate realistic ship movement: 10 knots for 2 hours
    ship_speed = 5.14  # 10 knots = 5.14 m/s
    timestep = 0.1  # 100ms physics timestep
    simulation_time = 7200  # 2 hours
    total_steps = int(simulation_time / timestep)

    # Ship moves in gentle curve (realistic navigation)
    positions = []
    max_step_jump = 0.0
    large_jumps = 0

    current_x = 0.0
    current_y = 0.0

    print(f"Simulating {simulation_time/3600:.1f}h ship movement at {ship_speed*1.944:.1f} knots")
    print("Time | Position (km) | Step Jump (m) | Status")
    print("-" * 45)

    for step in range(0, total_steps, 600):  # Print every minute
        # Simulate ship movement (gentle curve)
        angle = step * timestep * 0.001  # Very gentle turn
        dx = ship_speed * timestep * math.cos(angle)
        dy = ship_speed * timestep * math.sin(angle)

        current_x += dx
        current_y += dy

        # Apply coordinate transformation
        current_lat, current_lon = transformer.local_to_global(current_x, current_y)
        new_x, new_y, updated = transformer.check_and_update_reference(
            current_lat, current_lon, current_x, current_y, step * timestep
        )

        positions.append((new_x, new_y))

        # Calculate step-to-step jump
        step_jump = 0.0
        if len(positions) > 1:
            prev_x, prev_y = positions[-2]
            step_jump = math.sqrt((new_x - prev_x)**2 + (new_y - prev_y)**2)
            max_step_jump = max(max_step_jump, step_jump)

            # Count large jumps (>1m is problematic for physics)
            if step_jump > 1.0:
                large_jumps += 1

        # Update position
        current_x, current_y = new_x, new_y

        # Print status
        time_min = step * timestep / 60
        status = "✅STABLE" if step_jump <= 1.0 else "🔴UNSTABLE"

        print(f"{time_min:5.1f}m | ({new_x/1000:5.2f}, {new_y/1000:5.2f}) | "
              f"{step_jump:9.3f} | {status}")

    print(f"\n📊 PHYSICS STABILITY ANALYSIS")
    print(f"Max step jump: {max_step_jump:.3f}m")
    print(f"Large jumps (>1m): {large_jumps}")
    print(f"Total distance: {math.sqrt(current_x**2 + current_y**2)/1000:.1f}km")

    if large_jumps > 0:
        print(f"❌ PHYSICS STABILITY FAILED - {large_jumps} unstable steps")
        return False
    else:
        print(f"✅ PHYSICS STABILITY PASSED - Smooth coordinate progression")
        return True


if __name__ == "__main__":
    print("🧪 COORDINATE CONTINUITY VALIDATION")
    print("="*50)

    # Run focused tests for ship simulation
    test1 = test_coordinate_continuity()
    test2 = test_physics_simulation_stability()

    # Results
    print("\n" + "="*50)
    print("🏁 VALIDATION RESULTS")
    print("="*50)
    print(f"Coordinate continuity: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"Physics stability:     {'✅ PASSED' if test2 else '❌ FAILED'}")

    if test1 and test2:
        print("\n🎉 VALIDATION PASSED - Coordinate system is ready for ship simulation!")
        print("✅ Local coordinates change smoothly without jumps")
        print("✅ Physics simulation will remain stable")
    else:
        print("\n⚠️  VALIDATION FAILED - Coordinate system needs fixes")
        print("❌ Coordinate jumps detected - may disrupt physics simulation")
