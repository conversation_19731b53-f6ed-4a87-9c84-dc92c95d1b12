#!/usr/bin/env python3
"""
Test hot reload functionality for NMEA configuration.
"""

import json
import os
import sys
import time
import tempfile
from pathlib import Path

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.navigation_data import NavigationDataGenerator


def test_config_hot_reload():
    """Test that config changes are automatically detected and applied."""
    print("🔥 TESTING CONFIG HOT RELOAD")
    print("="*50)
    
    # Create temporary config file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        temp_config_path = temp_file.name
        
        # Initial config
        initial_config = {
            "communication_mode": "nmea_0183",
            "default_source": "GP0001",
            "talkers": {
                "GP": "Global Positioning System",
                "GL": "GLONASS"
            }
        }
        
        json.dump(initial_config, temp_file, indent=2)
    
    try:
        # Track config reload events
        reload_count = 0
        last_config = None
        
        def on_config_reload(config):
            nonlocal reload_count, last_config
            reload_count += 1
            last_config = config
            print(f"📥 Config reload #{reload_count}: {config.get('default_source', 'N/A')}")
        
        # Initialize generator with hot reload enabled
        print(f"📁 Using temp config: {temp_config_path}")
        generator = NavigationDataGenerator(
            config_file=temp_config_path,
            auto_reload=True,
            reload_callback=on_config_reload
        )
        
        print(f"Initial NMEA version: {generator.nmea_version}")
        print(f"Initial default source: {generator.default_source}")
        print(f"Initial talkers: {list(generator.talkers.keys())}")
        
        # Test 1: Modify config file
        print("\n🔧 TEST 1: Changing NMEA version and source")
        time.sleep(1)  # Ensure different timestamp
        
        modified_config = {
            "communication_mode": "nmea_0450",
            "default_source": "EI0002", 
            "talkers": {
                "GP": "Global Positioning System",
                "GL": "GLONASS",
                "GA": "Galileo",
                "BD": "BeiDou"
            }
        }
        
        with open(temp_config_path, 'w') as f:
            json.dump(modified_config, f, indent=2)
        
        # Wait for auto-reload
        print("⏳ Waiting for auto-reload...")
        start_time = time.time()
        while reload_count == 0 and time.time() - start_time < 5:
            time.sleep(0.1)
        
        if reload_count > 0:
            print(f"✅ Auto-reload detected! New version: {generator.nmea_version}")
            print(f"✅ New default source: {generator.default_source}")
            print(f"✅ New talkers: {list(generator.talkers.keys())}")
        else:
            print("❌ Auto-reload failed")
            return False
        
        # Test 2: Manual reload
        print("\n🔧 TEST 2: Manual reload")
        
        manual_config = {
            "communication_mode": "nmea_0183",
            "default_source": "GN0003",
            "talkers": {
                "GN": "Global Navigation Satellite System"
            }
        }
        
        with open(temp_config_path, 'w') as f:
            json.dump(manual_config, f, indent=2)
        
        # Manual reload
        generator.reload_config_now()
        
        print(f"✅ Manual reload: {generator.nmea_version}")
        print(f"✅ Manual source: {generator.default_source}")
        print(f"✅ Manual talkers: {list(generator.talkers.keys())}")
        
        # Test 3: Invalid config handling
        print("\n🔧 TEST 3: Invalid config handling")
        
        with open(temp_config_path, 'w') as f:
            f.write("{ invalid json }")
        
        time.sleep(2)  # Wait for auto-reload attempt
        
        # Should still have previous valid config
        print(f"✅ After invalid config: {generator.nmea_version}")
        print(f"✅ Config preserved: {generator.default_source}")
        
        # Test 4: Stop auto-reload
        print("\n🔧 TEST 4: Stop auto-reload")
        generator.stop_auto_reload()
        
        # Modify config - should not auto-reload
        valid_config = {
            "communication_mode": "nmea_0450",
            "default_source": "SHOULD_NOT_RELOAD",
            "talkers": {"XX": "Test"}
        }
        
        with open(temp_config_path, 'w') as f:
            json.dump(valid_config, f, indent=2)
        
        time.sleep(2)
        
        if generator.default_source != "SHOULD_NOT_RELOAD":
            print("✅ Auto-reload properly stopped")
        else:
            print("❌ Auto-reload still active")
            return False
        
        print("\n🎉 ALL HOT RELOAD TESTS PASSED!")
        return True
        
    finally:
        # Cleanup
        generator.stop_auto_reload()
        if os.path.exists(temp_config_path):
            os.unlink(temp_config_path)


def test_config_usage_examples():
    """Show practical usage examples."""
    print("\n📚 CONFIG HOT RELOAD USAGE EXAMPLES")
    print("="*50)
    
    print("""
🔥 Basic Hot Reload:
    generator = NavigationDataGenerator(
        config_file="config/nmea_talkers.json",
        auto_reload=True
    )

🔥 With Callback:
    def on_config_change(config):
        print(f"Config changed: {config['default_source']}")
    
    generator = NavigationDataGenerator(
        config_file="config/nmea_talkers.json", 
        auto_reload=True,
        reload_callback=on_config_change
    )

🔥 Manual Control:
    generator = NavigationDataGenerator(auto_reload=False)
    
    # Later, reload manually
    generator.reload_config_now()
    
    # Or enable auto-reload later
    generator.auto_reload = True
    generator._start_file_watcher()

🔥 Production Usage:
    # Disable auto-reload in production for performance
    generator = NavigationDataGenerator(auto_reload=False)
    
    # Use manual reload for controlled updates
    if config_update_needed:
        generator.reload_config_now()
    """)


if __name__ == "__main__":
    print("🧪 NMEA CONFIG HOT RELOAD TESTING")
    print("="*50)
    
    success = test_config_hot_reload()
    test_config_usage_examples()
    
    print("\n" + "="*50)
    if success:
        print("🎉 HOT RELOAD SYSTEM READY!")
        print("✅ Config changes will be applied automatically")
        print("✅ Invalid configs are handled gracefully") 
        print("✅ Manual reload control available")
    else:
        print("❌ HOT RELOAD SYSTEM NEEDS FIXES")
