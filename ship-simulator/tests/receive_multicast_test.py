#!/usr/bin/env python3
"""
Multicast NMEA Data Receiver Test

This script receives NMEA data from the ship simulator's multicast transmission.
The simulator sends NMEA data to multicast group ***********:60001.
"""

import socket
import struct
import time
import sys
from datetime import datetime


def create_multicast_receiver(multicast_ip: str, port: int):
    """
    Create a multicast receiver socket.

    Args:
        multicast_ip: Multicast group IP (e.g., "***********")
        port: Port number (e.g., 60001)
    """
    # Create UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    # Allow multiple sockets to bind to the same address
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    # Bind to all interfaces on the port
    sock.bind(('', port))
    print(f"Socket bound to all interfaces on port {port}")

    # Join multicast group (use any available interface)
    mreq = struct.pack("4s4s",
                       socket.inet_aton(multicast_ip),
                       socket.inet_aton('0.0.0.0'))
    sock.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)

    print(f"Joined multicast group {multicast_ip}")

    # Set socket timeout for non-blocking receive
    sock.settimeout(1.0)

    return sock


def receive_nmea_data(multicast_ip: str = "***********",
                     port: int = 60001):
    """
    Receive NMEA data from multicast transmission.
    """
    print("=" * 60)
    print("NMEA Multicast Receiver")
    print("=" * 60)
    print(f"Multicast Group: {multicast_ip}:{port}")
    print("Press Ctrl+C to stop")
    print("-" * 60)

    try:
        # Create multicast receiver
        sock = create_multicast_receiver(multicast_ip, port)
        
        message_count = 0
        last_message_time = time.time()
        
        print("Waiting for NMEA data...")
        
        while True:
            try:
                # Receive data
                data, addr = sock.recvfrom(1024)
                current_time = time.time()
                
                if data:
                    message_count += 1
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    
                    # Decode message
                    message = data.decode('utf-8', errors='ignore').strip()
                    
                    # Calculate time since last message
                    time_diff = current_time - last_message_time
                    last_message_time = current_time
                    
                    print(f"[{timestamp}] From {addr[0]}:{addr[1]} (+{time_diff:.2f}s)")
                    print(f"  📡 Message #{message_count}: {message}")
                    
                    # Parse NMEA sentence type
                    if message.startswith('$'):
                        parts = message.split(',')
                        if len(parts) > 0:
                            sentence_type = parts[0][1:]  # Remove $
                            print(f"  🔍 NMEA Type: {sentence_type}")
                    
                    print()
                    
            except socket.timeout:
                # No data received within timeout period
                continue
                
            except KeyboardInterrupt:
                print("\n🛑 Stopping receiver...")
                break
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
        
    finally:
        try:
            # Leave multicast group
            mreq = struct.pack("4s4s",
                             socket.inet_aton(multicast_ip),
                             socket.inet_aton('0.0.0.0'))
            sock.setsockopt(socket.IPPROTO_IP, socket.IP_DROP_MEMBERSHIP, mreq)
            sock.close()
            print("✅ Socket closed and left multicast group")
        except:
            pass
    
    return True


def test_multicast_connectivity():
    """Test basic multicast connectivity."""
    print("Testing multicast connectivity...")

    multicast_ip = "***********"
    port = 60001

    print(f"\nTesting: {multicast_ip}:{port} on all interfaces")
    try:
        sock = create_multicast_receiver(multicast_ip, port)
        print("✅ Socket created successfully")
        sock.close()
    except Exception as e:
        print(f"❌ Failed: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_multicast_connectivity()
    else:
        # Default: receive NMEA data
        receive_nmea_data()
