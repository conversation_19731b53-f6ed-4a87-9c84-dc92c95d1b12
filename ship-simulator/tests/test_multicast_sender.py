#!/usr/bin/env python3
"""
Simple Multicast Sender Test

이 스크립트는 간단한 멀티캐스트 송신 테스트를 수행합니다.
시뮬레이터와 동일한 설정으로 멀티캐스트 패킷을 전송합니다.
"""

import socket
import time
import sys
from datetime import datetime


def test_multicast_sender():
    """간단한 멀티캐스트 송신 테스트"""
    
    multicast_ip = "***********"
    multicast_port = 60001
    
    print("=" * 60)
    print("Multicast Sender Test")
    print("=" * 60)
    print(f"Target: {multicast_ip}:{multicast_port}")
    print("Press Ctrl+C to stop")
    print("-" * 60)
    
    try:
        # UDP 소켓 생성
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        
        # 멀티캐스트 송신 설정
        print("Configuring multicast sender...")
        
        # TTL 설정
        sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, 10)
        print("✅ Set multicast TTL to 10")
        
        # 루프백 활성화
        sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_LOOP, 1)
        print("✅ Enabled multicast loopback")
        
        # 기본 인터페이스 사용
        sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_IF, socket.inet_aton('0.0.0.0'))
        print("✅ Set multicast interface to default")
        
        print("🚀 Starting multicast transmission...")
        print()
        
        message_count = 0
        
        while True:
            message_count += 1
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            
            # 테스트 NMEA 메시지 생성
            test_message = f"$GPGGA,{timestamp.replace(':', '').replace('.', '')},3510.7760,N,12904.5360,E,1,08,1.0,100.0,M,0.0,M,,*XX"
            
            try:
                # 멀티캐스트 그룹으로 전송
                sock.sendto(test_message.encode('utf-8'), (multicast_ip, multicast_port))
                print(f"[{timestamp}] Sent #{message_count}: {test_message}")
                
            except Exception as e:
                print(f"❌ Error sending message #{message_count}: {e}")
            
            time.sleep(1)  # 1초마다 전송
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping sender...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
        
    finally:
        try:
            sock.close()
            print("✅ Socket closed")
        except:
            pass
    
    return True


def test_simple_multicast():
    """참조 코드와 동일한 방식의 멀티캐스트 송신"""
    print("Testing multicast with reference code style...")

    import struct

    # 호스트 IP 찾기 (가장 일반적인 방법)
    def get_host_ip():
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(('*******', 80))
                return s.getsockname()[0]
        except:
            return "0.0.0.0"  # 기본값

    host_ip = get_host_ip()
    print(f"Using host IP: {host_ip}")

    # 참조 코드와 정확히 동일한 소켓 설정
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, socket.IPPROTO_UDP)
    sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_IF, socket.inet_aton(host_ip))
    sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, struct.pack("b", 1))  # 참조 코드와 동일한 TTL=1
    sock.bind((host_ip, 0))

    print(f"✅ Socket configured and bound to {host_ip}")

    for i in range(5):
        message = f"Reference style test message {i+1}"
        sock.sendto(message.encode(), ('***********', 60001))
        print(f"Sent: {message}")
        time.sleep(1)

    sock.close()
    print("Reference style test completed")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "simple":
        test_simple_multicast()
    else:
        test_multicast_sender()
