"""
Unit tests for coordinate transformer with safe reference updates.
Tests the improved coordinate system update functionality.
"""

import unittest
import math
import sys
import os

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.coordinate_transformer import CoordinateTransformer


class TestCoordinateTransformer(unittest.TestCase):
    """Test coordinate transformer functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Singapore coordinates for testing
        self.ref_lat = 1.3521
        self.ref_lon = 103.8198
        self.transformer = CoordinateTransformer(self.ref_lat, self.ref_lon, "ENU")

        # Adjust settings for testing - disable time-based limiting for tests
        self.transformer.min_update_interval = 0.0  # Disable time limiting for tests
        self.transformer.update_threshold = 50000.0  # 50km
        self.transformer.update_hysteresis = 5000.0  # 5km
    
    def test_basic_coordinate_conversion(self):
        """Test basic coordinate conversion functionality."""
        print("\n=== Testing Basic Coordinate Conversion ===")
        
        # Test conversion at reference point
        x, y = self.transformer.global_to_local(self.ref_lat, self.ref_lon)
        print(f"Reference point conversion: ({x:.2f}, {y:.2f})")
        
        # Should be very close to origin
        self.assertLess(abs(x), 1.0, "X coordinate should be close to 0 at reference")
        self.assertLess(abs(y), 1.0, "Y coordinate should be close to 0 at reference")
        
        # Test reverse conversion
        lat, lon = self.transformer.local_to_global(x, y)
        print(f"Reverse conversion: ({lat:.6f}, {lon:.6f})")
        
        self.assertAlmostEqual(lat, self.ref_lat, places=5)
        self.assertAlmostEqual(lon, self.ref_lon, places=5)
    
    def test_coordinate_update_threshold(self):
        """Test coordinate reference update based on distance threshold."""
        print("\n=== Testing Coordinate Update Threshold ===")
        
        # Start at reference point
        current_x, current_y = 0.0, 0.0
        current_lat, current_lon = self.ref_lat, self.ref_lon
        
        # Move to a position within threshold (30km)
        test_distance = 30000  # 30km
        current_x = test_distance
        current_y = 0
        current_lat, current_lon = self.transformer.local_to_global(current_x, current_y)
        
        print(f"Position within threshold: ({current_x/1000:.1f}km, {current_y/1000:.1f}km)")
        print(f"Global coordinates: ({current_lat:.6f}, {current_lon:.6f})")
        
        # Should not trigger update (threshold is 50km)
        new_x, new_y, updated = self.transformer.check_and_update_reference(
            current_lat, current_lon, current_x, current_y, 0.0
        )
        
        print(f"Update triggered: {updated}")
        self.assertFalse(updated, "Should not update reference within threshold")
        self.assertEqual(new_x, current_x, "X coordinate should remain unchanged")
        self.assertEqual(new_y, current_y, "Y coordinate should remain unchanged")
    
    def test_coordinate_update_beyond_threshold(self):
        """Test coordinate reference update when beyond threshold."""
        print("\n=== Testing Coordinate Update Beyond Threshold ===")
        
        # Move to a position beyond threshold (60km)
        test_distance = 60000  # 60km
        current_x = test_distance
        current_y = 0
        current_lat, current_lon = self.transformer.local_to_global(current_x, current_y)
        
        print(f"Position beyond threshold: ({current_x/1000:.1f}km, {current_y/1000:.1f}km)")
        print(f"Global coordinates: ({current_lat:.6f}, {current_lon:.6f})")
        print(f"Original reference: ({self.transformer.ref_lat:.6f}, {self.transformer.ref_lon:.6f})")
        print(f"Distance from ref: {math.sqrt(current_x**2 + current_y**2)/1000:.1f}km")
        print(f"Threshold: {self.transformer.update_threshold/1000:.1f}km")
        print(f"Last update time: {self.transformer.last_update_time}")

        # Should trigger update (threshold is 50km) - use sufficient time
        new_x, new_y, updated = self.transformer.check_and_update_reference(
            current_lat, current_lon, current_x, current_y, 100.0  # Use sufficient time
        )
        
        print(f"Update triggered: {updated}")
        print(f"New coordinates: ({new_x:.1f}, {new_y:.1f})")
        print(f"New reference: ({self.transformer.ref_lat:.6f}, {self.transformer.ref_lon:.6f})")
        
        self.assertTrue(updated, "Should update reference beyond threshold")
        
        # New coordinates should be close to origin
        self.assertLess(abs(new_x), 1000, "New X should be close to origin")
        self.assertLess(abs(new_y), 1000, "New Y should be close to origin")
        
        # Reference should be updated to current position
        self.assertAlmostEqual(self.transformer.ref_lat, current_lat, places=5)
        self.assertAlmostEqual(self.transformer.ref_lon, current_lon, places=5)
    
    def test_time_based_rate_limiting(self):
        """Test time-based rate limiting for coordinate updates."""
        print("\n=== Testing Time-Based Rate Limiting ===")

        # Enable time limiting for this test and reset time tracking
        self.transformer.min_update_interval = 1.0  # 1 second minimum
        self.transformer.last_update_time = 0.0  # Reset for clean test

        # Move beyond threshold
        test_distance = 60000  # 60km
        current_x = test_distance
        current_y = 0
        current_lat, current_lon = self.transformer.local_to_global(current_x, current_y)

        # First update should succeed (with sufficient time)
        new_x1, new_y1, updated1 = self.transformer.check_and_update_reference(
            current_lat, current_lon, current_x, current_y, 100.0  # Start at 100 seconds
        )
        print(f"First update: {updated1}")
        self.assertTrue(updated1, "First update should succeed")

        # Move to another position beyond threshold immediately
        current_x = -test_distance
        current_y = 0
        current_lat, current_lon = self.transformer.local_to_global(current_x, current_y)

        # Second update should be blocked by rate limiting (same time)
        new_x2, new_y2, updated2 = self.transformer.check_and_update_reference(
            current_lat, current_lon, current_x, current_y, 100.5  # Only 0.5 seconds later
        )
        print(f"Second update (too soon): {updated2}")
        self.assertFalse(updated2, "Second update should be blocked by rate limiting")

        # Third update after sufficient time should succeed
        new_x3, new_y3, updated3 = self.transformer.check_and_update_reference(
            current_lat, current_lon, current_x, current_y, 102.0  # 2 seconds later
        )
        print(f"Third update (after time): {updated3}")
        self.assertTrue(updated3, "Third update should succeed after time delay")
    
    def test_coordinate_continuity(self):
        """Test that coordinate updates maintain continuity."""
        print("\n=== Testing Coordinate Continuity ===")
        
        # Start at a known position
        start_x, start_y = 45000, 30000  # 45km east, 30km north
        start_lat, start_lon = self.transformer.local_to_global(start_x, start_y)
        
        print(f"Start position: ({start_x/1000:.1f}km, {start_y/1000:.1f}km)")
        print(f"Start global: ({start_lat:.6f}, {start_lon:.6f})")
        
        # Trigger coordinate update (distance is ~54km, beyond 50km threshold)
        new_x, new_y, updated = self.transformer.check_and_update_reference(
            start_lat, start_lon, start_x, start_y, 100.0  # Use sufficient time
        )
        
        if updated:
            print(f"After update: ({new_x:.1f}, {new_y:.1f})")
            
            # Convert back to global coordinates
            check_lat, check_lon = self.transformer.local_to_global(new_x, new_y)
            print(f"Check global: ({check_lat:.6f}, {check_lon:.6f})")
            
            # Should represent the same global position
            self.assertAlmostEqual(check_lat, start_lat, places=5, 
                                 msg="Latitude should remain consistent after update")
            self.assertAlmostEqual(check_lon, start_lon, places=5,
                                 msg="Longitude should remain consistent after update")
    
    def test_error_handling(self):
        """Test error handling in coordinate updates."""
        print("\n=== Testing Error Handling ===")
        
        # Test with invalid coordinates
        invalid_lat = 91.0  # Invalid latitude
        invalid_lon = 181.0  # Invalid longitude
        current_x, current_y = 60000, 0
        
        try:
            new_x, new_y, updated = self.transformer.check_and_update_reference(
                invalid_lat, invalid_lon, current_x, current_y, 0.0
            )
            print(f"Invalid coordinate test - Updated: {updated}")
            # Should either handle gracefully or return False
            if updated:
                print("Warning: Invalid coordinates were accepted")
        except Exception as e:
            print(f"Exception handled: {e}")
            # This is acceptable - error was caught


def run_coordinate_tests():
    """Run all coordinate transformer tests."""
    print("🧪 Running Coordinate Transformer Tests...")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCoordinateTransformer)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All coordinate transformer tests passed!")
    else:
        print("❌ Some tests failed!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_coordinate_tests()
