#!/usr/bin/env python3
"""
RSA message sender for testing ship simulator.

This script sends RSA (Rudder Sensor Angle) messages to the ship simulator
running on ***********:4001.
"""

import socket
import time
import math
import sys


def calculate_nmea_checksum(sentence):
    """Calculate NMEA checksum."""
    checksum = 0
    # Skip the $ and stop at *
    for char in sentence[1:]:
        if char == '*':
            break
        checksum ^= ord(char)
    return f"{checksum:02X}"


def create_rsa_message(rudder_angle):
    """
    Create RSA NMEA message.
    
    Args:
        rudder_angle: Rudder angle in degrees (positive = starboard)
        
    Returns:
        Complete RSA NMEA message with checksum
    """
    # RSA format: $AGRSA,rudder_angle,A,rudder_angle,V*checksum
    sentence_body = f"$AGRSA,{rudder_angle:.1f},A,{rudder_angle:.1f},V"
    checksum = calculate_nmea_checksum(sentence_body)
    return f"{sentence_body}*{checksum}\r\n"


def send_rsa_message(sock, target_ip, target_port, rudder_angle):
    """Send RSA message to target."""
    message = create_rsa_message(rudder_angle)
    try:
        sock.sendto(message.encode('utf-8'), (target_ip, target_port))
        print(f"Sent: {message.strip()}")
        return True
    except Exception as e:
        print(f"Error sending message: {e}")
        return False


def test_static_rudder_commands():
    """Test with static rudder commands."""
    target_ip = "***********"
    target_port = 59902
    
    print(f"Sending static RSA commands to {target_ip}:{target_port}")
    print("-" * 50)
    
    # Create UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    try:
        # Test different rudder angles
        test_angles = [0, 10, 20, 15, 5, -5, -15, -20, -10, 0]
        
        for angle in test_angles:
            send_rsa_message(sock, target_ip, target_port, angle)
            time.sleep(2)  # Wait 2 seconds between commands
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    finally:
        sock.close()


def test_dynamic_rudder_commands():
    """Test with dynamic rudder commands (sine wave)."""
    target_ip = "***********"
    target_port = 4001
    
    print(f"Sending dynamic RSA commands to {target_ip}:{target_port}")
    print("Rudder angle follows sine wave pattern")
    print("-" * 50)
    
    # Create UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    try:
        start_time = time.time()
        
        while True:
            # Generate sine wave rudder command
            elapsed = time.time() - start_time
            rudder_angle = 20 * math.sin(elapsed * 0.5)  # 20° amplitude, 0.5 rad/s frequency
            
            send_rsa_message(sock, target_ip, target_port, rudder_angle)
            time.sleep(0.5)  # Send every 0.5 seconds
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    finally:
        sock.close()


def test_manual_rudder_commands():
    """Test with manual rudder input."""
    target_ip = "***********"
    target_port = 4001
    
    print(f"Manual RSA command sender to {target_ip}:{target_port}")
    print("Enter rudder angles in degrees (positive = starboard, negative = port)")
    print("Type 'quit' to exit")
    print("-" * 50)
    
    # Create UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    try:
        while True:
            try:
                user_input = input("Rudder angle (°): ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                rudder_angle = float(user_input)
                
                # Limit rudder angle to reasonable range
                if abs(rudder_angle) > 35:
                    print(f"Warning: Rudder angle {rudder_angle}° is large (max recommended: ±35°)")
                
                send_rsa_message(sock, target_ip, target_port, rudder_angle)
                
            except ValueError:
                print("Invalid input. Please enter a number.")
            except KeyboardInterrupt:
                break
                
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    finally:
        sock.close()


def main():
    """Main function with test menu."""
    print("RSA Message Sender for Ship Simulator")
    print("=" * 50)
    print("Target: ***********:59902")
    print()
    print("Test options:")
    print("1. Static rudder commands (predefined sequence)")
    print("2. Dynamic rudder commands (sine wave)")
    print("3. Manual rudder commands (interactive)")
    print("4. Exit")
    print()
    
    while True:
        try:
            choice = input("Select test (1-4): ").strip()
            
            if choice == '1':
                test_static_rudder_commands()
            elif choice == '2':
                test_dynamic_rudder_commands()
            elif choice == '3':
                test_manual_rudder_commands()
            elif choice == '4':
                print("Exiting...")
                break
            else:
                print("Invalid choice. Please select 1-4.")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
