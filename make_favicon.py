from PIL import Image, ImageDraw

# 64x64 투명 배경 이미지 생성
img = Image.new("RGBA", (64, 64), (255, 255, 255, 0))
draw = ImageDraw.Draw(img)

# 얼굴 테두리 (원)
draw.ellipse((4, 4, 60, 60), outline="black", width=2)

# 눈: 반쯤 감긴 눈 (아치 형태)
draw.arc((16, 20, 28, 32), start=0, end=180, fill="black", width=3)
draw.arc((36, 20, 48, 32), start=0, end=180, fill="black", width=3)

# 입: 처진 느낌의 직선 (또는 약간 굽은 선)
draw.line((20, 44, 44, 44), fill="black", width=3)

# 저장 (ICO 포맷)
img.save("favicon.ico", format="ICO")

from PIL import Image, ImageDraw

def create_favicon_sad(filename):
    size = 64
    img = Image.new("RGBA", (size, size), (255,255,255,0))
    draw = ImageDraw.Draw(img)
    # 얼굴 테두리 그리기
    draw.ellipse((4, 4, 60, 60), outline="black", width=2)
    
    # 눈: 반쯤 감긴 아치 형태의 눈
    draw.arc((16, 20, 28, 32), start=0, end=180, fill="black", width=3)
    draw.arc((36, 20, 48, 32), start=0, end=180, fill="black", width=3)
    
    # 입: 처진 슬픈 입 (아래로 볼록한 곡선)
    # 0도는 오른쪽, 90도는 아래, 180도는 왼쪽, 270도는 위
    # 45~135도는 얼굴 하단 쪽의 호로 처진 입 모양
    draw.arc((20, 38, 44, 54), start=45, end=135, fill="black", width=3)
    
    img.save(filename, format="ICO")

def create_favicon_happy(filename):
    size = 64
    img = Image.new("RGBA", (size, size), (255,255,255,0))
    draw = ImageDraw.Draw(img)
    # 얼굴 테두리 그리기
    draw.ellipse((4, 4, 60, 60), outline="black", width=2)
    
    # 눈: 둥근 눈 (작은 원)
    draw.ellipse((16, 20, 24, 28), fill="black")
    draw.ellipse((40, 20, 48, 28), fill="black")
    
    # 입: 웃는 입 (위로 볼록한 곡선)
    # 웃는 입은 200도부터 340도로 그리면 위쪽으로 볼록한 호가 생성됨
    draw.arc((18, 30, 46, 50), start=200, end=340, fill="black", width=3)
    
    img.save(filename, format="ICO")

create_favicon_sad("favicon-sad.ico")
create_favicon_happy("favicon-happy.ico")
